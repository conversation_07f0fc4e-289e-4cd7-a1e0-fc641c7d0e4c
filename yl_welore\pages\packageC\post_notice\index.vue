<template>
    <view>
        <cu-custom bgColor="bg-white" :isBack="true" :isSearch="false">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">发帖须知</view>
        </cu-custom>
        <view style="padding: 10px 20px 0px 20px">
            <text>{{ info.notice }}</text>
        </view>
    </view>
</template>

<script>
const app = getApp();
const http = require('../../../util/http.js');
export default {
    data() {
        return {
            info: {}
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {},
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {
        const b = app.globalData.api_root + 'User/get_post_notice';
        const e = app.globalData.getCache('userinfo');
        const params = {
            token: e.token,
            openid: e.openid
        };
        http.POST(b, {
            params: params,
            success: (res) => {
                this.info = res.data;
            },
            fail: () => {
                uni.showModal({
                    title: '提示',
                    content: '网络繁忙，请稍候重试！',
                    showCancel: false,
                    success: (res) => {}
                });
            }
        });
    },
    methods: {}
};
</script>
<style>
page {
    background-color: #fff;
}
</style>

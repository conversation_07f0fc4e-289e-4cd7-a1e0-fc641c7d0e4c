<template>
    <view>
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
            <view slot="backText"></view>
            <view slot="content" style="color: #000000; font-weight: 600; font-size: 36rpx">{{ info.file_name }}</view>
        </cu-custom>
        <view class="cu-bar bg-white margin-top">
            <view class="action">
                <text class="cuIcon-titles text-yellow"></text>
                <text v-if="info.count > 0" style="font-size: 13px; color: #999999">共有（{{ info.count }}）个文件</text>
                <text v-if="info.count == 0" style="font-size: 13px; color: #999999">没有文件</text>
            </view>
        </view>
        <view class="flex justify-between align-center" v-for="(item, index) in list" :key="index">
            <view class="padding-sm margin-xs">
                <view class="flex align-center" @tap="open_inside" :data-index="index">
                    <view class="margin-xs">
                        <image :src="http_root + 'addons/yl_welore/web/static/file_icon/' + item.file_icon"
                            style="height: 30px; width: 30px"></image>
                    </view>
                    <view class="margin-xs" style="margin-left: 15px">
                        <view class="font-yl-2 text_num_1" style="font-size: 14px; font-weight: 600">
                            <text>{{ item.file_name }}</text>
                            <text v-if="item.is_dir == 0">.{{ item.file_suffix }}</text>
                        </view>
                        <view style="font-size: 12px; margin-top: 8px; color: #9e9e9e">
                            <text>{{ item.add_time }}</text>
                            <text v-if="item.is_dir == 0" style="margin-left: 20px">{{ item.file_size }}</text>
                        </view>
                    </view>
                </view>
            </view>

            <view @tap="openMod" :data-index="index" class="padding-sm margin-xs">
                <text class="cuIcon-more text-gray" style="font-size: 20px"></text>
            </view>
        </view>
        <view style="margin: 20px 0px 100px 0px" :class="'cu-load ' + (!di_msg ? 'loading' : 'over')"></view>
        <!-- 文件操作 -->
        <view :class="'cu-modal bottom-modal ' + (modalName == 'bottomModal' ? 'show' : '')" @tap="hideMod">
            <view class="cu-dialog" style="border-top-left-radius: 10px; border-top-right-radius: 10px"
                @tap.stop.prevent="maopao">
                <view class="cu-bar bg-white solid-bottom">
                    <view class="action">
                        <view class="flex justify-start align-center">
                            <view>
                                <image :src="http_root + 'addons/yl_welore/web/static/file_icon/' + file_info.file_icon"
                                    style="height: 30px; width: 30px"></image>
                            </view>
                            <view>
                                <text class="font-yl-2 text-grey text_num_1"
                                    style="font-size: 14px; margin-left: 30rpx">{{ file_info.file_name }}</text>
                            </view>
                        </view>
                        <view></view>
                    </view>
                </view>
                <view class="bg-white" style="text-align: left">
                    <view class="cu-list menu">
                        <view v-if="file_info.is_dir == 0" @tap="openFile" class="cu-item margin-tb">
                            <view class="content text-black">
                                <text class="cuIcon-down" style="font-size: 20px; vertical-align: middle"></text>
                                <text style="font-size: 12px">{{ file_info.suf == 1 ? '预览' : '复制下载链接' }}</text>
                            </view>
                        </view>
                        <view @tap="openForward" class="cu-item margin-tb">
                            <view class="content text-black">
                                <text class="cuIcon-forward" style="font-size: 20px; vertical-align: middle"></text>
                                <text style="font-size: 12px">分享</text>
                            </view>
                        </view>
                        <view v-if="file_info.is_dir == 0" @tap="openModMove" class="cu-item margin-tb">
                            <view class="content text-black">
                                <text class="cuIcon-exit" style="font-size: 20px; vertical-align: middle"></text>
                                <text style="font-size: 12px">移动</text>
                            </view>
                        </view>
                        <view @tap="openModRename" class="cu-item margin-tb">
                            <view class="content text-black">
                                <text class="cuIcon-edit" style="font-size: 20px; vertical-align: middle"></text>
                                <text style="font-size: 12px">重命名</text>
                            </view>
                        </view>
                        <view @tap="DelMod" class="cu-item margin-tb">
                            <view class="content text-black">
                                <text class="cuIcon-delete" style="font-size: 20px; vertical-align: middle"></text>
                                <text style="font-size: 12px">删除</text>
                            </view>
                        </view>
                    </view>
                </view>
                <view @tap="hideMod" style="height: 55px; line-height: 55px">取消</view>
            </view>
        </view>
        <!-- 文件操作 -->
        <!-- 重命名 -->
        <view :class="'cu-modal ' + (modalName == 'Rename' ? 'show' : '')">
            <view class="cu-dialog">
                <view class="cu-bar bg-white justify-end">
                    <view class="content" style="font-size: 18px; font-weight: 600">重命名</view>
                    <view class="action" @tap="hideMod">
                        <text class="cuIcon-close text-red"></text>
                    </view>
                </view>
                <view class="padding-sm">
                    <view class="cu-form-group">
                        <input placeholder="" @input="get_name" :value="file_info.file_name" maxlength="20" />
                        <text @tap="clean_name" class="cuIcon-roundclosefill" style="color: #d8d9dc"></text>
                    </view>
                </view>
                <view class="cu-bar bg-white justify-center">
                    <view class="action">
                        <button class="cu-btn line-yellow text-yellow" @tap="hideMod">取消</button>
                        <button class="cu-btn bg-yellow margin-left-xl" @tap="update_file_name">确定</button>
                    </view>
                </view>
            </view>
        </view>
        <!-- 重命名 -->
        <!-- 移动 -->
        <view :class="'cu-modal bottom-modal ' + (modalName == 'Move' ? 'show' : '')" @tap="hideMod">
            <view class="cu-dialog bg-white" style="height: 65%" @tap.stop.prevent="maopao">
                <view class="cu-bar bg-white solid-bottom">
                    <view style="font-size: 12px" class="text_num_1 action text-grey">移动文件：{{ file_info.file_name }} 到
                    </view>
                    <view class="action text-blue" @tap="confirm_move"
                        style="margin-right: 20px; font-weight: 600; font-size: 16px">确定移动</view>
                </view>
                <scroll-view :scroll-y="true" class="padding-sm margin-xs" style="height: 85%">
                    <view class="flex justify-between align-center solid-bottom" @tap="select_dir" :data-index="d_index"
                        v-for="(item, d_index) in dir_list" :key="d_index">
                        <view class="flex align-center" style="padding: 20px 0px">
                            <view class="margin-xs">
                                <image :src="http_root + 'addons/yl_welore/web/static/file_icon/' + item.file_icon"
                                    style="height: 30px; width: 30px"></image>
                            </view>
                            <view class="margin-xs" style="margin-left: 15px; text-align: left">
                                <view class="font-yl-2 text_num_1" style="font-size: 14px; font-weight: 600">{{
                                    item.file_name }}</view>
                                <view style="font-size: 12px; margin-top: 8px; color: #9e9e9e">
                                    <text>{{ item.id == 0 ? '根目录' : item.add_time }}</text>
                                </view>
                            </view>
                        </view>

                        <view style="padding-right: 40px">
                            <text style="font-size: 40rpx" v-if="dir_index == d_index"
                                class="cuIcon-check text-green"></text>
                        </view>
                    </view>
                </scroll-view>
            </view>
        </view>
        <!-- 移动 -->
        <!-- 上传进度 -->
        <view :class="'cu-modal ' + (modalName == 'schedule' ? 'show' : '')">
            <view class="cu-dialog">
                <view class="cu-bar bg-white justify-end">
                    <view class="content">正在上传中...</view>
                </view>
                <view class="padding-xl">
                    <view class="flex">
                        <view class="cu-progress round">
                            <view class="bg-green" :style="'width:' + loading + '%;'"></view>
                        </view>
                        <text class="margin-left">{{ loading }}%</text>
                    </view>
                    <view style="margin-top: 15px">文件上传中，请不要关闭页面...</view>
                </view>
            </view>
        </view>
        <!-- 上传进度 -->
        <view class="btn">
            <view @tap.stop.prevent="click" :class="'btn-main ' + btnAnimation">
                <text class="_icon-add"></text>
            </view>
        </view>
        <view :class="'mask1 ' + maskAnimation" @touchmove.stop.prevent="preventdefault"></view>
        <view class="menu-container" @touchmove.stop.prevent="preventdefault" v-if="!isShow">
            <view class="add_menu">
                <view class="menu-list">
                    <view class="menu-item" @tap="new_upload" style="width: 50%; animation-delay: 0.1s">
                        <image mode="aspectFill" :src="http_root + 'addons/yl_welore/web/static/file_icon/wx.png'"
                            class="menu-icon"></image>
                        <text class="menu-name">上传微信文件</text>
                    </view>
                    <view class="menu-item" @tap="new_upload_local" style="width: 50%; animation-delay: 0.1s">
                        <image mode="aspectFill" :src="http_root + 'addons/yl_welore/web/static/file_icon/upload.png'"
                            class="menu-icon"></image>
                        <text class="menu-name">上传本地文件</text>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp();
var http = require('../../../util/http.js');
var upload = require('../../../util/upload.js');
const yl_file = uni.getFileSystemManager();

export default {
    data() {
        return {
            isShow: true,
            http_root: app.globalData.http_root,
            modalName: '',
            info: {},
            net_info: {},
            page: 1,
            list: [],
            dir_page: 1,
            dir_list: [],
            dir_index: null,
            loading: 0,
            file_info: {},
            file_index: 0,
            onload: 0,
            di_msg: false,
            btnAnimation: '',
            maskAnimation: '',
            id: ''
        }
    },
    methods: {
        new_upload_local() {
            uni.navigateTo({
                url: '/yl_welore/pages/packageF/netdisc_local/index?pid=' + this.id
            });
        },
        openForward() {
            var that = this;
            var info = this.file_info;
            if (info['file_status'] == 0) {
                uni.showModal({
                    title: '提示',
                    content: '文件已被屏蔽，现已被删除！',
                    showCancel: false,
                    success: (res) => { }
                });
                that.del_flie_do(info);
                return;
            }
            if (info['is_sell'] == 0) {
                uni.showToast({
                    title: '文件禁止分享',
                    icon: 'none'
                });
                return;
            }
            uni.navigateTo({
                url: '/yl_welore/pages/packageA/add/index?type=0&fa_class=0&name=&gambit_name=&gambit_id=0&file_id=' + info.id
            });
        },
        openFile() {
            var info = this.file_info;
            var that = this;
            if (info['file_status'] == 0) {
                uni.showModal({
                    title: '提示',
                    content: '文件已被屏蔽，现已被删除！',
                    showCancel: false,
                    success: (res) => { }
                });
                that.del_flie_do(info);
                return;
            }
            if (info.suf == 0) {
                uni.setClipboardData({
                    data: info.file_url,
                    success: (res) => {
                        that.modalName = '';
                    }
                });
                return;
            }
            var params = new Object();
            params.url = info.file_url;
            params.type = info.file_suffix;
            params.id = info.id;
            params.name = info.file_name;
            params.is_sell = info.is_sell;
            //var id=this.file_info['id'];
            upload.Access(params, yl_file, (a_res) => {
                console.log(a_res);
                upload.download_file(
                    params,
                    (res) => {
                        console.log(res);
                        if (res.errMsg == 'downloadFile:ok') {
                            //获取文件类型
                            params.url = res.tempFilePath;
                            upload.open_file(params, yl_file);
                        }
                    },
                    (res) => {
                        console.log(res);
                        this.modalName = '';
                    },
                    (res) => {
                        console.log(res);
                        if (res.progress >= 100) {
                            this.modalName = '';
                        }
                        this.loading = res.progress;
                    }
                );
            });
        },
        DelMod() {
            var that = this;
            var info = this.file_info;
            this.hideMod();
            uni.showModal({
                title: '操作提醒',
                content: '确定要删除「' + info.file_name + '」文件吗？',
                success(res) {
                    if (res.confirm) {
                        that.del_flie_do(info);
                    }
                }
            });
        },
        del_flie_do(info) {
            var that = this;
            var b = app.globalData.api_root + 'Storage/del_folder_dir';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = info.id;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.code == 0) {
                        uni.showToast({
                            title: '删除成功！',
                            icon: 'none',
                            duration: 1000
                        });
                        that.hideMod();
                        let cartList = that.list;
                        cartList.splice(that.file_index, 1);
                        that.list = cartList;
                        that.info.count = that.info.count - 1 < 0 ? 0 : that.info.count - 1;
                        //that.get_my_file();
                    } else {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: (res) => { }
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        },
        confirm_move(d) {
            if (this.dir_index == null) {
                uni.showToast({
                    title: '请选择一个目录',
                    icon: 'none',
                    duration: 1500
                });
                return;
            }
            var dir_info = this.dir_list[this.dir_index];
            var b = app.globalData.api_root + 'Storage/move_file_dir';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.file_info.id;
            params.move_id = dir_info.id;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.code == 0) {
                        uni.showToast({
                            title: '移动成功！',
                            icon: 'none',
                            duration: 1000
                        });
                        that.hideMod();
                        setTimeout(() => {
                            that.list = [];
                            that.page = 1;
                            that.dir_index = null;
                            that.get_inside_list();
                        }, 500);
                    } else {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: (res) => { }
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        },
        select_dir(d) {
            var index = d.currentTarget.dataset.index;
            //var dir=this.dir_list[index];
            this.dir_index = index;
        },
        openModMove() {
            this.modalName = 'Move';
            this.dir_page = 1;
            this.dir_list = [];
            this.get_dir_list();
        },
        hideMod() {
            this.modalName = '';
        },
        update_file_name() {
            var info = this.file_info;
            if (info['file_name'] == '') {
                uni.showToast({
                    title: '文件名不能为空！',
                    icon: 'none',
                    duration: 1500
                });
                return;
            }
            var b = app.globalData.api_root + 'Storage/update_file_name';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = info.id;
            params.name = info.file_name;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.code == 0) {
                        uni.showToast({
                            title: '修改成功！',
                            icon: 'none',
                            duration: 1000
                        });
                        that.list[that.file_index].file_name = info.file_name;
                        that.hideMod();
                    } else {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: (res) => { }
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        },
        get_name(d) {
            var info = this.file_info;
            info.file_name = d.detail.value;
            this.file_info = info;
        },
        clean_name() {
            var info = this.file_info;
            info['file_name'] = '';
            this.file_info = info;
        },
        openMod(d) {
            var index = d.currentTarget.dataset.index;
            var info = this.list[index];
            this.modalName = 'bottomModal';
            this.file_info = info;
            this.file_index = index;
        },
        openModRename(d) {
            this.modalName = 'Rename';
        },
        get_dir_list() {
            var b = app.globalData.api_root + 'Storage/get_dir';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.dir_page = this.dir_page;
            var allMsg = that.dir_list;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    for (var i = 0; i < res.data.length; i++) {
                        allMsg.push(res.data[i]);
                    }
                    that.dir_list = allMsg;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        },

        get_my_volume() {
            var b = app.globalData.api_root + 'Storage/get_my_volume';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.code == 0) {
                        that.net_info = res.data.info;
                    } else {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: (res) => { }
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        },
        get_inside_list() {
            var b = app.globalData.api_root + 'Storage/get_inside_list';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.pid = this.id;
            params.page = this.page;
            var allMsg = that.list;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.code == 0) {
                        for (var i = 0; i < res.data.list.length; i++) {
                            allMsg.push(res.data.list[i]);
                        }
                        that.list = allMsg;
                        that.info = res.data.info;
                        if (res.data.list.length == 0 || allMsg.length < 10) {
                            that.di_msg = true;
                        }
                    } else {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: (res) => { }
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        },
        open_inside(d) {
            var info = this.list[d.currentTarget.dataset.index];
            if (info['is_dir'] != 1) {
                this.file_info = info;
                this.openFile();
                return;
            }
        },
        click() {
            this.isShow = !this.isShow;
            if (this.isShow) {
                this.maskAnimation = 'maskClose';
                this.btnAnimation = 'menuClose';
            } else {
                this.maskAnimation = 'maskOpen';
                this.btnAnimation = 'menuOpen';
            }
        },
        new_upload() {
            var that = this;
            this.modalName = 'schedule';
            var params = new Object();
            params.pid = this.id;
            params.upload_limit_b = this.net_info.upload_limit_b;
            params.upload_limit = this.net_info.upload_limit;
            params.user_big_b = this.net_info.user_big_b;
            params.user_use_b = this.net_info.user_use_b;
            params.upload_type_limited = this.net_info.upload_type_limited;
            upload.upload_file(
                params,
                (res) => {
                    //成功
                    console.log(res);
                    if (res.status == 'success') {
                        that.modalName = '';
                        that.page = 1;
                        that.list = [];
                        that.loading = 0;
                        uni.hideLoading();
                        uni.showToast({
                            title: '上传成功',
                            icon: 'none',
                            duration: 1000
                        });
                        that.get_inside_list();
                        that.click();
                    } else {
                        that.modalName = '';
                        uni.showToast({
                            title: res.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                (res) => {
                    //取消选择文件
                    console.log(res);
                    this.modalName = '';
                },
                (res) => {
                    //失败
                    console.log(res);
                    this.modalName = '';
                    uni.showToast({
                        title: '系统错误！',
                        icon: 'none',
                        duration: 2000
                    });
                },
                (res) => {
                    //失败
                    console.log(res);
                    this.modalName = '';
                },
                (res) => {
                    console.log(res);
                    if (res.progress >= 100) {
                        uni.showLoading({
                            title: '上传中...'
                        });
                    }
                    this.loading = res.progress;
                }
            );
        },
        preventdefault() {
            // 阻止默认事件
        },
        maopao() {
            // 阻止冒泡
        },
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.id = options.id;
        this.get_inside_list();
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {
        this.get_my_volume();
        if (this.onload == 1) {
            this.page = 1;
            this.list = [];
            this.get_inside_list();
            this.click();
        }
    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() { },
    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {
        this.page = this.page + 1;
        this.get_inside_list();
    }
};
</script>
<style scoped>
page {
    background-color: #FEFEFE;
}

.mask1 {
    height: 48px;
    width: 48px;
    background-color: #ffffff;
    opacity: 0.85;
    z-index: 1000;
    border-radius: 750rpx;
    transform: scale(0);
    position: fixed;
    bottom: 0;
    left: 50%;
    margin-left: -48rpx;
}

.maskOpen {
    animation: maskO 0.3s both;
}

.maskClose {
    animation: maskC 0.3s both;
}

@keyframes maskO {
    0% {
        transform: scale(0);
    }

    20% {
        transform: scale(4);
    }

    40% {
        transform: scale(18);
    }

    60% {
        transform: scale(24);
    }

    80% {
        transform: scale(38);
    }

    100% {
        transform: scale(48);
    }
}

@keyframes maskC {
    0% {
        transform: scale(48);
    }

    25% {
        transform: scale(24);
    }

    100% {
        transform: scale(0);
    }
}

.btn {
    width: 100%;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    z-index: 1005;
    bottom: calc(10rpx + env(safe-area-inset-bottom));
}

.btn-main {
    border-radius: 50%;
    z-index: 1005;
    height: 48px;
    font-size: 28px;
    width: 48px;
    line-height: 48px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    color: #fff;
    background: #fbbd08;
}

.menuOpen {
    animation: menuO 0.3s both;
}

.menuClose {
    animation: menuC 0.3s both;
}

@keyframes menuO {
    0% {
        transform: rotate(0deg);
        color: #fff;
        background: #fbbd08;
    }

    100% {
        transform: rotate(45deg);
        color: #000;
        background: #fff;
    }
}

@keyframes menuC {
    0% {
        transform: rotate(45deg);
        color: #000;
        background: #fff;
    }

    100% {
        transform: rotate(-0deg);
        color: #fff;
        background: #fbbd08;
    }
}

.menu-container {
    position: fixed;
    width: 100%;
    z-index: 1002;
    bottom: 0rpx;
}

.add_menu {
    padding-bottom: calc(48px + 40rpx + env(safe-area-inset-bottom));
}

.menu-list {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    width: 100%;
    padding-bottom: 15rpx;
}

.menu-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    animation: bounceInDown 0.45s linear both;
}

.menu-icon {
    width: 110rpx;
    height: 110rpx;
    margin-bottom: 15rpx;
    border-radius: 100%;
}

.menu-name {
    color: #333;
    font-size: 25rpx;
    font-weight: bold;
    margin-bottom: 10rpx;
}

@keyframes bounceInDown {
    0% {
        opacity: 0;
        transform: translateY(100%)
    }

    60% {
        transform: translateY(-10%);
    }

    80% {
        transform: translateY(10%);
    }

    100% {
        opacity: 1;
        transform: translateY(0%)
    }
}
</style>

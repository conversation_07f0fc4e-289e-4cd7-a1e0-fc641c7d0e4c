<template>
    <view>
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">获奖记录</view>
        </cu-custom>

        <view style="margin: 10px; background-color: #fff; border-radius: 5px">
            <view class="bg-white">
                <view class="grid col-3 text-center" style="padding: 15px" v-for="(item, index) in list" :key="index">
                    <view style="float: left">{{ item.join_time }}</view>

                    <view class="text-cut">{{ item.prize_name }}</view>

                    <view>
                        <button
                            v-if="item.win_type == 1"
                            @tap="get_info"
                            :data-key="index"
                            style="height: 30px; padding: 0 10px; font-size: 12px; margin-top: -6px; float: right"
                            class="cu-btn round bg-blue"
                            role="button"
                            :aria-disabled="false"
                        >
                            查看详情
                        </button>
                        <text v-if="item.win_type != 1" style="width: 68px; color: #00cc66; font-size: 20px; float: right">+{{ item.reward_score }}</text>
                    </view>
                </view>
            </view>
        </view>

        <view :class="['cu-modal', { show: modalName }]">
            <view class="cu-dialog">
                <view class="cu-bar bg-white justify-end">
                    <view class="content">获奖详情</view>
                    <view class="action" @tap="hideModal">
                        <text class="cuIcon-close text-red"></text>
                    </view>
                </view>
                <view class="padding-xl bg-white" style="padding: 15px">
                    <view>恭喜您获得：{{ info.prize_name }}</view>
                    <view>
                        礼物编码：
                        <text :user-select="true">{{ info.record_number }}</text>
                        <text style="font-size: 12px">(长按复制)</text>
                    </view>
                    <view v-if="info.address_details == null">
                        <view>请填写真实地址接收礼品</view>
                        <view class="cu-form-group">
                            <view class="title">姓名</view>
                            <input @input="form_data" data-key="1" placeholder="收货姓名" />
                        </view>
                        <view class="cu-form-group">
                            <view class="title">电话</view>
                            <input type="number" @input="form_data" data-key="2" placeholder="联系电话" />
                        </view>
                        <view class="cu-form-group">
                            <view class="title">地址</view>
                            <input @input="form_data" data-key="3" placeholder="收货地址" />
                        </view>
                    </view>
                    <view v-if="info.address_details != null">
                        <view class="cu-form-group">
                            <view class="title">姓名</view>
                            <view style="flex: 1">{{ info.address_format.name }}</view>
                        </view>
                        <view class="cu-form-group">
                            <view class="title">电话</view>
                            <view style="flex: 1">{{ info.address_format.phone }}</view>
                        </view>
                        <view class="cu-form-group">
                            <view class="title">地址</view>
                            <view style="flex: 1">{{ info.address_format.address }}</view>
                        </view>
                    </view>
                    <view v-if="info.delivery_status == 0">
                        <text style="color: #ff3300">礼品未派发</text>
                    </view>
                    <view v-if="info.delivery_status == 1">
                        <view style="color: #009900">礼品已派发</view>
                        <text style="color: #009900">
                            物流单号或兑换码:
                            <text :user-select="true">{{ info.courier_convert }}</text>
                        </text>
                    </view>
                </view>
                <view class="cu-bar bg-white justify-end">
                    <view class="action">
                        <button class="cu-btn line-green text-green" @tap="hideModal">取消</button>
                        <button class="cu-btn bg-green margin-left" @tap="ok_address">确定</button>
                    </view>
                </view>
            </view>
        </view>

        <view :class="'cu-load ' + (!di_msg ? 'loading' : 'over')"></view>
    </view>
</template>

<script>
const app = getApp();
const http = require('../../../util/http.js');
export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            list: [],
            page: 1,
            info: {},
            modalName: false,
            name: '',
            phone: '',
            address: '',
            di_msg: false
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad() {
        const subscribe = app.globalData.getCache('subscribe');
        if (!subscribe) {
            app.globalData.subscribe_message(
                (res) => {
                    //请求成功的回调函数
                    console.log(res);
                    if (res == '') {
                        return;
                    }
                    app.globalData.setCache('subscribe', res.parallelism_data);
                },
                () => {
                    //请求失败的回调函数，不需要时可省略
                }
            );
        }
        this.page = 1;
        this.list = [];
        this.get_user_luck();
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {},
    /**
     * 加载下一页
     */
    onReachBottom() {
        this.page = this.page + 1;
        this.get_user_luck();
    },
    methods: {
        form_data(d) {
            const key = d.target.dataset.key;
            if (key == 1) {
                this.name = d.detail.value;
            }
            if (key == 2) {
                this.phone = d.detail.value;
            }
            if (key == 3) {
                this.address = d.detail.value;
            }
        },
        get_info(d) {
            console.log();
            const info = this.list[d.currentTarget.dataset.key];
            this.info = info;
            this.modalName = true;
        },
        hideModal() {
            this.modalName = false;
        },
        ok_address() {
            if (this.info['address_details'] != null) {
                console.log('fei_NULL');
                const subscribe = app.globalData.getCache('subscribe');
                if (subscribe && subscribe['YL0006'] && subscribe['YL0009'] && subscribe['YL0010']) {
                    app.globalData.authorization(subscribe['YL0006'], subscribe['YL0009'], subscribe['YL0010'], (res) => {});
                }
                return;
            }
            const b = app.globalData.api_root + 'Subscribe/ok_address';
            const e = app.globalData.getCache('userinfo');
            const params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.name = this.name;
            params.phone = this.phone;
            params.address = this.address;
            params.id = this.info['id'];
            http.POST(b, {
                params: params,
                success: (res) => {
                    if (res.data.status == 'success') {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: (res) => {
                                if (res.confirm) {
                                    const subscribe = app.globalData.getCache('subscribe');
                                    if (subscribe && subscribe['YL0006'] && subscribe['YL0009'] && subscribe['YL0010']) {
                                        app.globalData.authorization(subscribe['YL0006'], subscribe['YL0009'], subscribe['YL0010'], () => {});
                                    }
                                    this.hideModal();
                                    this.onLoad();
                                } else if (res.cancel) {
                                    const subscribe = app.globalData.getCache('subscribe');
                                    if (subscribe && subscribe['YL0006'] && subscribe['YL0009'] && subscribe['YL0010']) {
                                        app.globalData.authorization(subscribe['YL0006'], subscribe['YL0009'], subscribe['YL0010'], () => {});
                                    }
                                    this.hideModal();
                                    this.onLoad();
                                }
                            }
                        });
                    } else {
                        console.log('no_NULL');
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: function (res) {}
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },
        get_user_luck() {
            const b = app.globalData.api_root + 'Subscribe/get_user_luck';
            const e = app.globalData.getCache('userinfo');
            const params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.page = this.page;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res.data);
                    if (res.data.status == 'success') {
                        this.list = this.list.concat(res.data.info);
                        if (res.data.info.length == 0 || this.list.length < 20) {
                            this.di_msg = true;
                        }
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        }
    }
};
</script>
<style></style>

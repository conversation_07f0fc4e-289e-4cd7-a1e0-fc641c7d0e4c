<template>
    <view>
        <view class="" style="position: fixed; width: 100%; z-index: 100000; background-color: #ffffff;top: 86px;">
            <view @tap="handleChange" data-key="tab1" style="line-height: 25px; height: 30px; width: 33%; float: left; text-align: center">
                <text :class="current == 'tab1' ? '_this_index5' : '_no_index5'">此刻</text>
                <view v-if="current == 'tab1'" style="width: 20%; height: 2px; background-color: #55bbbc; margin-top: 18px; border-radius: 10px; margin: 0 auto"></view>
            </view>
            <view @tap="handleChange" data-key="tab3" style="line-height: 25px; height: 33px; width: 30%; float: left; text-align: center">
                <text :class="current == 'tab3' ? '_this_index5' : '_no_index5'">推荐</text>
                <view v-if="current == 'tab3'" style="width: 20%; height: 2px; background-color: #55bbbc; margin-top: 18px; border-radius: 10px; margin: 0 auto"></view>
            </view>
            <view @tap="handleChange" data-key="tab2" style="line-height: 25px; height: 33px; width: 30%; float: left; text-align: center">
                <text :class="current == 'tab2' ? '_this_index5' : '_no_index5'">关注</text>
                <view v-if="current == 'tab2'" style="width: 20%; height: 2px; background-color: #55bbbc; margin-top: 18px; border-radius: 10px; margin: 0 auto"></view>
            </view>
        </view>
        <view style="margin-top: 35px"></view>
        <view v-if="sw_info.length > 0">
            <swiper
                style="height: 600rpx"
                class="screen-swiper square-dot"
                :indicator-dots="true"
                :circular="true"
                :autoplay="true"
                interval="5000"
                duration="500"
                indicator-active-color="#696D7D"
            >
                <block v-for="(item, index) in sw_info" :key="index">
                    <swiper-item>
                        <view style="width: 100%;height: 100%;"
                            class="swiper-item"
                            @tap.native.stop.prevent="open_navigator"
                            :data-src="item.playbill_url"
                            :data-path="item.wx_app_url"
                            :data-type="item.practice_type"
                            :data-url="item.url"
                        >
                            <image :src="item.playbill_url" mode="aspectFill" style="padding: 0px 20px"></image>
                        </view>
                    </swiper-item>
                </block>
            </swiper>
        </view>

        <view style="clear: both; height: 0"></view>
    </view>
</template>

<script>
import Home from './home.vue';
export default {
    components: {
        Home 
    },
    props: ['data', 'compName'],
    computed: {
        parentData() {
            return this.$parent.$data;
        },
        sw_info() {
            return this.$parent.$data.sw_info;
        },
        cardCur() {
            return this.$parent.$data.cardCur;
        },
        current() {
            return this.$parent.$data.current;
        },
        home_list() {
            return this.$parent.$data.home_list;
        },
        http_root() {
            return this.$parent.$data.http_root;
        },
        
    },
    methods: {
        bindchange_top(e) {
            this.$emit('bindchange_top', e);
        },
        open_navigator(e) {
            this.$emit('open_navigator', e);
        },
        handleChange(e) {
            this.$emit('handleChange', e);
        },
        gambit_list(e) {
            this.$emit('gambit_list', e);
        },
        cardSwiper(e) {
            this.$emit('cardSwiper', e);
        },
        set_one(e) {
            this.$emit('set_one', e);
        },
        top_url(e) {
            this.$emit('top_url', e);
        },
        get_all_qq(e) {
            this.$emit('get_all_qq', e);
        },
        nex_my_qq(e) {
            this.$emit('nex_my_qq', e);
        },
        this_url(e) {
            this.$emit('this_url', e);
        }
    }
};
</script>
<style></style>

<template>
  <view class="page-container">
    <cu-custom bgColor="none" :isSearch="false" :isBack="true">
      <view slot="backText">返回</view>
      <view slot="content" style="color: #2c2b2b;font-weight: 600; font-size: 36rpx;">商品详情</view>
    </cu-custom>
    <view class="swiper-container">
      <swiper class="square-dot product-swiper" indicator-active-color="#ff6b6b" indicator-color="rgba(255,255,255,0.5)"
        :indicator-dots="true" style="height:740rpx;width:100%;" :autoplay="true" interval="5000" duration="1000">
        <swiper-item v-for="(item, index) in good_info.product_img" :key="index">
          <image class="product-image"  :src="item" />
        </swiper-item>
      </swiper>
    </view>
    <view class="product-info-card">
      <view class="product-basic-info">
        <view class="product-title">{{ good_info.product_name }}</view>
        <view class="product-synopsis">
          {{ good_info.product_synopsis }}
        </view>
      </view>
      <view class="price-section">
        <view class="current-price">
          <image v-if="good_info.pay_type == 0 || good_info.pay_type == 1" class="currency-icon" mode="widthFix"
            :src="$state.diy.currency_icon"></image>
          <text class="price-text" :class="good_info.pay_type == 2 ? 'text-price' : ''">{{ good_info.product_price }}</text>
          <text v-if="good_info.pay_type == 0 || good_info.pay_type == 1"
            class="currency-label">({{ good_info.pay_type == 0 ? design.currency : design.confer }})</text>
        </view>
        <view v-if="good_info.open_discount == 1" class="member-price-section">
          <view class="member-price-badge">👑 会员价</view>
          <view class="member-price">
            <image v-if="good_info.pay_type == 0 || good_info.pay_type == 1" class="currency-icon-small" mode="widthFix"
              :src="$state.diy.currency_icon"></image>
            <text class="member-price-text"
              :class="good_info.pay_type == 2 ? 'text-price' : ''">{{ toFix(good_info.product_price * good_info.noble_discount) }}</text>
            <text v-if="good_info.pay_type == 0 || good_info.pay_type == 1"
              class="currency-label-small">({{ good_info.pay_type == 0 ? design.currency : design.confer }})</text>
          </view>
        </view>
      </view>
      <view v-if="good_info.noble_rebate != 0" class="divider"></view>
      <view v-if="good_info.noble_rebate != 0" class="rebate-section">
        <text v-if="good_info.noble_exclusive" class="exclusive-badge">🌟 [会员专属]</text>
        <text class="rebate-text">🎁 会员赠送{{ good_info.noble_rebate }}{{ design.confer }}</text>
      </view>
    </view>
    <view v-if="good_info.easy != ''" class="merchant-info-card">
      <view class="merchant-content">
        <view class="merchant-avatar">
          <image :src="good_info.easy.merchant_icon_carousel[0]" class="avatar-image"></image>
        </view>
        <view class="merchant-details">
          <view class="merchant-name">🏪 {{ good_info.easy.merchant_name }}</view>
          <view class="merchant-address" @tap.stop.prevent="get_position" :data-pos_name="good_info.easy.address_name"
            :data-latitude="good_info.easy.address_latitude" :data-longitude="good_info.easy.address_longitude">
            <text class="location-icon">📍</text>
            <text class="address-text">{{ good_info.easy.address_name }}</text>
          </view>
        </view>
      </view>
      <view class="phone-action" @tap.stop.prevent="open_phone" :data-p="good_info.easy.merchant_phone">
        <text class="phone-icon">📞</text>
      </view>
    </view>
    <view style="clear:both;height:0"></view>
    <view class="detail-header">
      <view class="detail-title">
        <text class="detail-icon">📋</text>
        <text class="detail-text">商品详情</text>
      </view>
    </view>
    <view class="detail-content">
      <mp-html @linktap="linktap" :selectable="true" :copy-link="false" :lazy-load="true"
        :content="good_info.product_detail" />
    </view>
    <view class="bottom-action-bar" :class="$state.isIphoneX ? 'iphoneX-height' : ''">
      <view class="product-stats">
        <view class="stat-item">
          <text class="stat-icon">📦</text>
          <text class="stat-text">库存：{{ good_info.product_inventory }}</text>
        </view>
        <view class="stat-divider">|</view>
        <view class="stat-item">
          <text class="stat-icon">🔥</text>
          <text class="stat-text">销量：{{ good_info.sales_volume }}</text>
        </view>
        <view v-if="good_info.product_restrict > 0" class="stat-divider">|</view>
        <view v-if="good_info.product_restrict > 0" class="stat-item">
          <text class="stat-icon">⚠️</text>
          <text class="stat-text">限购：{{ good_info.product_restrict }}</text>
        </view>
      </view>
      <view class="action-button-container">
        <button @tap="open_sku" class="purchase-button">🛒 立即订购</button>
      </view>
    </view>
    <view :class="'cu-modal ' + (paper_mod ? 'show' : '')">
      <view class="balance-modal">
        <view class="modal-header">
          <view class="modal-title">💰 系统提示</view>
          <view class="modal-close" @tap="hideModal">
            <text class="close-icon">❌</text>
          </view>
        </view>
        <view class="modal-body">
          <view class="insufficient-balance-text">
            😔 余额不足
          </view>
          <view class="balance-info">
            <image :src="$state.diy.currency_icon" class="balance-icon"></image>
            <text v-if="good_info.pay_type == 0" class="balance-text">我的{{ copyright.currency }}：{{ user_info.conch }}</text>
            <text v-if="good_info.pay_type == 1" class="balance-text">我的{{ copyright.confer }}：{{ user_info.fraction }}</text>
          </view>
        </view>
        <view class="modal-footer">
          <button class="cancel-button" @tap="hideModal">❌ 取消</button>
          <button class="wallet-button" @tap="do_url">💳 去钱包</button>
        </view>
      </view>
    </view>
    <view :class="'cu-modal bottom-modal ' + (sku_mod ? 'show' : '')" @tap="open_sku">
      <view class="cu-dialog sku-modal" @tap.stop.prevent="ca">
        <view class="sku-modal-content">
          <view class="sku-product-info">
            <view class="sku-image-container">
              <image @tap.stop.prevent="open_img" :data-src="sku_info.sa_img" class="sku-product-image"
                :src="sku_info.sa_img"></image>
            </view>
            <view class="sku-product-details">
              <view class="sku-price-section">
                <image v-if="good_info.pay_type == 0 || good_info.pay_type == 1" class="sku-currency-icon" mode="widthFix"
                  :src="$state.diy.currency_icon"></image>
                <text class="sku-price-text">{{ sku_info.price }}</text>
                <text v-if="good_info.pay_type == 0 || good_info.pay_type == 1"
                  class="sku-currency-label">({{ good_info.pay_type == 0 ? design.currency : design.confer }})</text>
              </view>
              <view class="sku-inventory-section">
                <text class="inventory-label">📦 剩余</text>
                <text class="inventory-count">{{ sku_info.inventory_count }}</text>
                <text class="inventory-unit">件</text>
              </view>
              <view class="sku-selected-section">
                <text class="selected-label">✅ 已选</text>
                <text class="selected-value">{{ sku_info.at_name }}</text>
              </view>
            </view>
          </view>
          <view class="text-left text-black" style="margin:30rpx 0px;font-weight: 700;">{{ good_info.sku.sa_name }}</view>
          <view class="text-left">
            <block v-for="(item, gg_index) in (good_info.sku.list)" :key="gg_index">
              <view @tap.stop.prevent="select_sku" :data-index="gg_index"
                :class="'cu-tag ' + (sku_index == gg_index ? 'bg-red' : 'text-black') + '  light radius margin-xs'">
                {{ item.at_name }}</view>
            </block>
          </view>
          <view class="padding flex flex-direction" style="margin-top:30rpx">
            <button @tap.stop.prevent="exchange" class="cu-btn margin-tb-sm lg round text-white"
              style="background: linear-gradient(to right, #ff6034, #ee0a24);">立即订购</button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<!-- <script module="filters" lang="wxs" src="@/yl_welore/pages/packageA/good_info/tofix.wxs"></script> -->
<script>
import http from "../../../util/http.js";
const app = getApp();
export default {
  components: {},
  data() {
    return {
      http_root: app.http_root,
      good_info: {
        product_img: [],
        product_name: "",
        product_synopsis: "",
        pay_type: 0,
        product_price: "",
        open_discount: 0,
        noble_discount: 0,
        noble_rebate: 0,
        noble_exclusive: "",
        easy: {
          merchant_icon_carousel: "",
          merchant_name: "",
          address_name: "",
          address_latitude: "",
          address_longitude: "",
          merchant_phone: ""
        },
        product_detail: "",
        product_inventory: "",
        sales_volume: "",
        product_restrict: 0,
        sku: {
          sa_name: "",
          list: []
        }
      },
      paper_mod: false,
      sku_mod: false,
      copyright: {
        currency: "",
        confer: ""
      },
      isIpx: app.globalData.isIpx,
      sku_index: 0,
      sku_info: {
        sa_img: "",
        price: "",
        inventory_count: "",
        at_name: ""
      },
      id: "",
      design: {
        currency: "",
        confer: ""
      },
      user_info: {
        conch: "",
        fraction: ""
      },
      gg_index: 0
    };
  },
  /**
  * 生命周期函数--监听页面加载
  */
  onLoad(options) {
    var design = uni.getStorageSync('is_diy');
    this.id = options.id;
    this.design = design;
    this.get_goods();
    this.authority();
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.get_user_info();
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    var forward = app.globalData.forward;
    var good_info = this.good_info;
    if (forward) {
      return {
        title: forward.title,
        path: '/yl_welore/pages/packageA/good_info/index?id=' + this.id,
        imageUrl: forward.reis_img
      };
    } else {
      return {
        title: good_info.product_name,
        path: '/yl_welore/pages/packageA/good_info/index?id=' + this.id,
        imageUrl: good_info.product_img[0]
      };
    }
  },

  methods: {
    toFix(value) {
    var na = (parseInt(value * 100) / 100).toFixed(2);
    return na;
  },
    open_img(d) {
      var src = d.currentTarget.dataset.src;
      uni.previewImage({
        current: src,
        // 当前显示图片的http链接
        urls: [src] // 需要预览的图片http链接列表
      });
    },
    open_sku() {
      if (this.good_info.sku.length == 0) {
        this.exchange();
        return;
      }
      var sku_list = this.good_info.sku.list;
      this.sku_mod = !this.sku_mod;
      this.sku_info = sku_list[0];
      this.sku_index = 0;
    },
    select_sku(d) {
      var sku_list = this.good_info.sku.list;
      this.sku_index = d.currentTarget.dataset.index;
      this.sku_info = sku_list[d.currentTarget.dataset.index];
    },
    /**
     * 打开地图
     */
    get_position(d) {
      console.log(d);
      var a = Number(d.currentTarget.dataset.latitude);
      var o = Number(d.currentTarget.dataset.longitude);
      var name = d.currentTarget.dataset.pos_name;
      if (a && o) {
        uni.openLocation({
          latitude: a,
          longitude: o,
          name: name
        });
      }
    },
    open_phone(d) {
      console.log(d);
      uni.makePhoneCall({
        phoneNumber: d.currentTarget.dataset.p
      });
    },
    hideModal() {
      this.paper_mod = false;
    },
    do_url() {
      this.hideModal();
      uni.navigateTo({
        url: '/yl_welore/pages/packageC/user_details/index'
      });
    },

    //获取用户信息
    get_user_info() {
      var b = app.globalData.api_root + 'User/get_article_user_info';
      var e = app.globalData.getCache("userinfo"); //console.log(e);
      var params = {
        token: e.token,
        openid: e.openid
      };
      http.POST(b, {
        params: params,
        success: res => {
          if (res.data['status'] == 'feng') {
            uni.navigateTo({
              url: '/yl_welore/pages/black_house/index?msg=' + res.data.user.forbid_prompt + '&open_id=' + res.data.user.open_id
            });
          }
          if (res.data.status == 'success') {
            this.user_info = res.data.info; //app.setCache("userinfo", res.data.info);
            if (res.data.info.status == 0) {
              uni.navigateTo({
                url: '/yl_welore/pages/black_house/index'
              });
              return;
            }
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) { }
          });
        }
      });
    },
    /**
     * 信息站点
     */
    authority() {
      var b = app.globalData.api_root + 'User/get_article_authority';
      var e = app.globalData.getCache("userinfo");
      var params = {
        token: e.token,
        openid: e.openid
      };
      http.POST(b, {
        params: params,
        success: res => {
          console.log(res);
          this.copyright = res.data;
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) { }
          });
        }
      });
    },

    /**
     * 兑换物品
     */
    exchange() {
      var e = app.globalData.getCache("userinfo");
      var params = {
        token: e.token,
        openid: e.openid,
        uid: e.uid,
        id: this.id,
        sa_id: this.sku_index
      };
      var b = app.globalData.api_root + 'User/exchange_goods';
      http.POST(b, {
        params: params,
        success: res => {
          console.log(res);
          if (res.data.status == "success") {
            uni.navigateTo({
              url: '/yl_welore/pages/packageA/good_address/index?id=' + this.id + "&sa_id=" + this.sku_index
            });
          } else {
            if (res.data.code == 1) {
              uni.showToast({
                title: res.data.msg,
                icon: 'none',
                duration: 2000
              });
            } else {
              this.sku_mod = false;
              this.paper_mod = true;
            }
          }
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) { }
          });
        }
      });
    },
    linktap(e) {
      console.log(e);
      var key = e.detail.jump_type;
      if (key == 0) {
        return;
      } else if (key == 1) {
        uni.navigateToMiniProgram({
          appId: e.detail.data_appid,
          path: e.detail.href
        });
      } else {
        uni.navigateTo({
          url: '/yl_welore/pages/web/index?url=' + e.detail.href
        });
      }
    },
    /**
     * 获取商品详情
     */
    get_goods() {
      var e = app.globalData.getCache("userinfo");
      var params = {
        token: e.token,
        openid: e.openid,
        uid: e.uid,
        id: this.id
      };
      var b = app.globalData.api_root + 'User/get_goods';
      http.POST(b, {
        params: params,
        success: res => {
          console.log(res);
          if (res.data.status == "success") {
            this.good_info = res.data.info;
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) { }
          });
        }
      });
    },
    ca() {
      console.log("占位：函数 ca 未声明");
    }
  }
};
</script>
<style>
page {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

.page-container {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 轮播图样式 */
.swiper-container {
  margin: 20rpx;
  border-radius: 20rpx;
  overflow: hidden;
}

.product-swiper {
  border-radius: 20rpx;
}

.product-image {
  border-radius: 20rpx;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 商品信息卡片 */
.product-info-card {
  margin: 20rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.product-basic-info {
  margin-bottom: 30rpx;
}

.product-title {
  font-size: 44rpx;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1.4;
  margin-bottom: 20rpx;
}

.product-synopsis {
  font-size: 28rpx;
  color: #7f8c8d;
  line-height: 1.6;
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 16rpx;
}

.price-section {
  margin-top: 30rpx;
}

.current-price {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.currency-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 16rpx;
}

.price-text {
  font-size: 48rpx;
  font-weight: 700;
  color: #e74c3c;
}

.currency-label {
  font-size: 24rpx;
  color: #95a5a6;
  margin-left: 16rpx;
}

.member-price-section {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  padding: 20rpx;
  border-radius: 16rpx;
  margin-top: 20rpx;
}

.member-price-badge {
  font-size: 24rpx;
  color: #8b4513;
  font-weight: 600;
  margin-bottom: 10rpx;
}

.member-price {
  display: flex;
  align-items: center;
}

.currency-icon-small {
  width: 30rpx;
  height: 30rpx;
  margin-right: 10rpx;
}

.member-price-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #8b4513;
}

.currency-label-small {
  font-size: 20rpx;
  color: #8b4513;
  margin-left: 10rpx;
}

.divider {
  height: 2rpx;
  background: linear-gradient(90deg, transparent 0%, #e0e0e0 50%, transparent 100%);
  margin: 30rpx 0;
}

.rebate-section {
  padding: 20rpx;
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
  border-radius: 16rpx;
}

.exclusive-badge {
  font-size: 24rpx;
  color: #27ae60;
  font-weight: 600;
  margin-right: 10rpx;
}

.rebate-text {
  font-size: 24rpx;
  color: #27ae60;
  font-weight: 500;
}
/* 商家信息卡片 */
.merchant-info-card {
  margin: 20rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.merchant-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.merchant-avatar {
  margin-right: 20rpx;
}

.avatar-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: 4rpx solid #ffffff;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.merchant-details {
  flex: 1;
}

.merchant-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 10rpx;
}

.merchant-address {
  display: flex;
  align-items: center;
  color: #7f8c8d;
  font-size: 24rpx;
}

.location-icon {
  margin-right: 8rpx;
}

.address-text {
  flex: 1;
}

.phone-action {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(52, 152, 219, 0.3);
}

.phone-icon {
  font-size: 32rpx;
  color: white;
}

/* 详情标题 */
.detail-header {
  margin:20rpx;
}

.detail-title {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  padding: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.detail-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.detail-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
}

/* 详情内容 */
.detail-content {
  margin: 0 20rpx 220rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

/* 底部操作栏 */
.bottom-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  padding: 20rpx 30rpx;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.product-stats {
  display: flex;
  align-items: center;
  flex: 1;
}

.stat-item {
  display: flex;
  align-items: center;
}

.stat-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.stat-text {
  font-size: 24rpx;
  color: #7f8c8d;
}

.stat-divider {
  margin: 0 15rpx;
  color: #bdc3c7;
  font-size: 24rpx;
}

.action-button-container {
  margin-left: 20rpx;
}

.purchase-button {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 10rpx 40rpx;
  font-size: 28rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 16rpx rgba(231, 76, 60, 0.3);
  min-width: 200rpx;
}

.iphoneX-height {
  padding-bottom: 25px !important;
}

/* 余额不足弹窗 */
.balance-modal {
  background: #ffffff;
  border-radius: 24rpx;
  margin: 40rpx;
  overflow: hidden;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.2);
}

.modal-header {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  color: white;
  font-size: 32rpx;
  font-weight: 600;
}

.modal-close {
  padding: 10rpx;
}

.close-icon {
  font-size: 24rpx;
  color: white;
}

.modal-body {
  padding: 40rpx 30rpx;
  text-align: center;
}

.insufficient-balance-text {
  font-size: 36rpx;
  color: #e74c3c;
  font-weight: 600;
  margin-bottom: 30rpx;
}

.balance-info {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 16rpx;
}

.balance-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
}

.balance-text {
  font-size: 28rpx;
  color: #7f8c8d;
}

.modal-footer {
  padding: 30rpx;
  display: flex;
  gap: 20rpx;
}

.cancel-button {
  flex: 1;
  background: #ecf0f1;
  color: #7f8c8d;
  border: none;
  border-radius: 50rpx;
  padding: 24rpx;
  font-size: 28rpx;
}

.wallet-button {
  flex: 1;
  background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: 600;
}

/* SKU选择弹窗 */
.sku-modal {
  background: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  height: 45%;
  overflow: hidden;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.sku-modal-content {
  padding: 40rpx 30rpx;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.sku-product-info {
  display: flex;
  padding-bottom: 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
  margin-bottom: 30rpx;
}

.sku-image-container {
  margin-right: 20rpx;
}

.sku-product-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.sku-product-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.sku-price-section {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.sku-currency-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 12rpx;
}

.sku-price-text {
  font-size: 40rpx;
  font-weight: 700;
  color: #e74c3c;
}

.sku-currency-label {
  font-size: 24rpx;
  color: #95a5a6;
  margin-left: 12rpx;
}

.sku-inventory-section {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.inventory-label {
  font-size: 24rpx;
  color: #7f8c8d;
}

.inventory-count {
  font-size: 28rpx;
  font-weight: 600;
  color: #e74c3c;
  margin: 0 10rpx;
}

.inventory-unit {
  font-size: 24rpx;
  color: #7f8c8d;
}

.sku-selected-section {
  display: flex;
  align-items: center;
}

.selected-label {
  font-size: 24rpx;
  color: #7f8c8d;
}

.selected-value {
  font-size: 24rpx;
  color: #2c3e50;
  margin-left: 16rpx;
  font-weight: 500;
}

.sku-options-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 20rpx;
}

.sku-options-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 40rpx;
}

.sku-option {
  padding: 16rpx 32rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 50rpx;
  font-size: 26rpx;
  color: #7f8c8d;
  background: #ffffff;
  transition: all 0.3s ease;
}

.sku-option.selected {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: white;
  border-color: #e74c3c;
  box-shadow: 0 4rpx 16rpx rgba(231, 76, 60, 0.3);
}

.sku-action-section {
  margin-top: auto;
}

.sku-purchase-button {
  width: 100%;
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 28rpx;
  font-size: 32rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 16rpx rgba(231, 76, 60, 0.3);
}
</style>
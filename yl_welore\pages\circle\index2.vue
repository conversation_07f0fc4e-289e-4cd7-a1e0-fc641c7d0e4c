<template>
	<view style="background-color:#E7ECF0;min-height:45em;padding:10px;padding-bottom:100px;">
		<view v-if="rank.length > 0" style="background-color:#fff;border-radius:5px;overflow:hidden;margin-bottom:10px;">
			<view style="padding:10px 0px 10px 10px;font-size:15px;font-weight:600;">排行榜</view>
			<scroll-view :scroll-x="true" style="padding: 15px; white-space: nowrap">
				<view v-for="(item, index) in rank" :key="index" @tap="open_ph"
					style="position: relative;;display: inline-block;margin-right: 30rpx;">
					<view class="ph_txt">{{ item.ranking_name }}</view>
					<image :data-id="item.id" class="ph_img" :src="item.bg_img" style="width: 290rpx"></image>
				</view>
			</scroll-view>
		</view>
		<view style="background-color:#fff;border-radius:5px;overflow:hidden;padding-bottom:10px;">
			<view style="padding:10px 0px 10px 10px;font-size:15px;font-weight:600;float:left;">我加入的</view>
			<navigator v-if="copyright.tory_arbor == 1" url="/yl_welore/pages/packageB/set_territory/index"
				style="display:inline;" hover-class="none">
				<view style="padding:10px 10px 10px 0px;font-size:15px;float:right;">
					<text
						style="background-color:#19A1F3;border-radius:15px;padding:5px 10px;font-size:12px;color:#fff;">创建{{ design.landgrave }}</text>
				</view>
			</navigator>
			<view style="clear:both;height:0"></view>
			<view style="border:0.5px solid #F2F4F6;width:95%;margin:0 auto;"></view>
			<view class="bg-white padding-sm">
				<view class="grid col-4 text-center">
					<view style="padding:10px;" v-for="(item, i_key) in info" :key="i_key">
						<navigator :url="'/yl_welore/pages/packageA/circle_info/index?id=' + item.id" hover-class="none">
							<view class="cu-avatar radius lg"
								:style="'background-color:#fff;background-image:url(' + item.realm_icon + ');'">
								<view v-if="item.attention == 1" class="cu-tag badge cuIcon-lock bg-pink"></view>
							</view>
						</navigator>
						<view class="text-cut" style='font-size:14px;margin-top: 5px;'>{{ item.realm_name }}</view>
					</view>
				</view>
			</view>
		</view>

		<block v-for="(item, index) in (needle)" :key="index">



			<view style="background-color:#fff;border-radius:5px;overflow:hidden;padding-bottom:10px;margin-top:10px;">
				<view style="padding:10px 0px 10px 15px;font-size:15px;font-weight:600;float:left;">{{ item.name }}</view>
				<view @tap="quan_url" :data-id="item.id" style="padding:10px 10px 10px 0px;font-size:15px;float:right;">
					<text style="padding:5px 0px;font-size:13px;color:#718190;">查看更多</text>
					<text class="cuIcon-right lg text-gray"></text>
				</view>
				<view style="clear:both;height:0"></view>
				<view style="border:0.5px solid #F2F4F6;width:95%;margin:0 auto;"></view>

				<view class="">
					<view class="bg-white padding-sm">
						<view class="grid col-4 margin-bottom text-center">
							<view style="padding:15px 0px;" v-for="(c_item, index1) in (item.children)" :key="index1">


								<navigator :url="'/yl_welore/pages/packageA/circle_info/index?id=' + (c_item.id)"
									hover-class="none">
									<view class="cu-avatar radius lg"
										:style="'background-color:#fff;background-image:url(' + (c_item.realm_icon) + ');'">
										<view v-if="c_item.attention == 1" class="cu-tag badge cuIcon-lock bg-pink">
										</view>
									</view>
									<view class="text-cut" style="font-size:14px;margin-top: 5px;">{{ c_item.realm_name }}
									</view>
								</navigator>
							</view>
						</view>
					</view>
				</view>
			</view>
		</block>
		<view style="margin:10px;text-align: center;">
			<span v-if="gc_msg == true" @tap.native.stop.prevent="page_next" class="cu-tag bg-white">加载更多</span>
			<span v-if="gc_msg == false" class="cu-tag bg-grey light">暂无更多</span>
		</view>
	</view>
</template>

<script>
export default {
	props: ['data', 'compName'],
	computed: {
		rank() {
			return this.$parent.$data.rank;
		},
		http_root() {
			return this.$parent.$data.http_root;
		},
		design() {
			return this.$parent.$data.design;
		},
		copyright() {
			return this.$parent.$data.copyright;
		},
		info() {
			return this.$parent.$data.info;
		},
		needle() {
			return this.$parent.$data.needle;
		},
		tj_list() {
			return this.$parent.$data.tj_list;
		},
		di_msg() {
			return this.$parent.$data.di_msg;
		},
	},
	methods: {
		open_ph(e) {
			this.$emit('open_ph', e);
		},
		nex_my_qq(e) {
			this.$emit('nex_my_qq', e);
		},
		this_url(e) {
			this.$emit('this_url', e);
		},
		quan_url(e) {
			this.$emit('quan_url', e);
		},
	}
};
</script>
<style></style>
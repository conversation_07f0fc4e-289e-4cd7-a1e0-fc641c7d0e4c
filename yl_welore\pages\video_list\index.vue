<template>
<view>
<cu-custom bgColor="bg-white" :isBack="true" :isSearch="false">
	<view slot="backText">返回</view>
	<view slot="content" style="color: #2c2b2b;font-weight: 600; font-size: 36rpx;">{{version==1?'美文':'视频'}}</view>
</cu-custom>
<view style="background-color:#FFFFFF;padding-bottom:80px;min-height:400px;">
	<block v-for="(item,dataListindex) in (list)" :key="dataListindex">
		



<view style="background-color:#fff;overflow:hidden;position: relative;">
			<view style="">
				<!-- 头像 -->
				<view class="cu-list menu-avatar">
					<view class="cu-item">
						<view @tap="home_url" :data-index="dataListindex" data-k="1" :data-user_id="item.user_id" class="cu-avatar round lg" :style="'background-image:url('+(item.user_head_sculpture)+');'">
							<view :class="'cu-tag badge '+(item.gender==2?'cuIcon-female bg-pink':'cuIcon-male bg-blue')"></view>
							<!-- <image class="now_level" style="height: 30rpx;width: 30rpx;position: absolute;right:-1px;bottom:-3px;z-index:100;max-width:initial" src="{{item.attr.attest.at_icon}}"></image> -->
						</view>
						<view class="content flex-sub">
              <view class="align-center">
                <view :class="item.user_id!=0?item.special:''" style="font-size:13px;">{{item.user_nick_name}}</view>
                <image v-if="item.user_vip==1&&item.user_id!=0" :src="(http_root)+'addons/yl_welore/web/static/applet_icon/vip.png'" style="width:30rpx;height:30rpx;;vertical-align:bottom;margin-left:3px;"></image>
                <image v-if="item.user_id!=0" mode="heightFix" class="now_level" :src="item.level" style="height:13px;vertical-align: middle;margin-left: 3px;"></image>
                <image mode="heightFix" class="now_level" v-if="item.wear_merit&&item.user_id!=0" :src="item.wear_merit" style="height:13px;vertical-align:bottom;margin-left:3px;"></image>
              </view>
              <view class="text-gray text-sm flex">
                <text style="font-size: 13px;color: #888888;">{{item.adapter_time}}</text>
              </view>
						</view>
					</view>
				</view>

				<!-- 头像 -->
				<!-- 内容 -->
				<view>
					<view class="weui-cell" style="padding:0rpx 10px 10px 20px;">
						<view class="weui-cell__hd text_num" :style="'font-size:16px;position: relative;margin-right: 10px;color:'+(item.study_title_color)+';'">
							<text @tap="gambit_list" :data-id="item.gambit_id" style="color:#0099FF;margin-right: 5px;">{{item.gambit_name}}</text>
							<text @tap="home_url" :data-index="dataListindex" data-k="3" :data-type="item.study_type" :data-id="item.id" v-if="item.study_title||item.study_content">{{item.study_title==''?item.study_content:item.study_title}}</text>
						</view>
					</view>
					<view v-if="item.study_type==2">
						<view>
							<view class="video">
								<video @tap="video_play" :data-id="item.id" :id="item.id" class="myVideo" :src="item.study_video" :custom-cache="false">
								</video>
							</view>
						</view>
					</view>
				</view>
				<!-- 内容 -->
				<view style="clear:both;height:0"></view>
				<view class="" style="padding-bottom:10px;padding-top:5px;">
					<view @tap="home_url" :data-index="dataListindex" data-k="2" :data-id="item.tory_id" style="float:left;margin-left:20px;font-size:14px;padding-top:6px;font-weight:500;color:#3399FF;" class="weui-flex__item">
						{{item.realm_name}}
					</view>
					<view style="float:right;margin-right:15px;" class="weui-flex__item">
						<button @tap="home_url" :data-index="dataListindex" data-k="3" :data-type="item.study_type" :data-id="item.id" hover-class="none">
							<image :src="(http_root)+'addons/yl_welore/web/static/mineIcon/index3/xiaoxi.png'" style="width: 20px;vertical-align:middle;height:20px;"></image>
							<text class="index_nav_name" style="color:#000;font-size:12px;vertical-align:super;">{{item.study_repount}}</text>
						</button>
					</view>
					<view style="float:right;margin-bottom:15px;margin-right:15px;" class="weui-flex__item">
						<button hover-class="none" @tap="parseEventDynamicCode($event, item.is_buy==1?'':'add_zan')" :data-id="item.id" :data-key="dataListindex">
              <image v-if="item.is_info_zan==false" :src="(http_root)+'addons/yl_welore/web/static/mineIcon/index3/zan_1.png'" style="width: 46rpx;height:46rpx;vertical-align:middle;">
              </image>
              <image v-if="item.is_info_zan==true" :src="(http_root)+'addons/yl_welore/web/static/mineIcon/index3/zan.png'" style="width: 46rpx;height:46rpx;vertical-align:middle;">
              </image>
							<text class="index_nav_name" style="color:#000;font-size:12px;vertical-align:super;">{{item.info_zan_count_this>10000?item.info_zan_count:item.info_zan_count_this}}</text>
						</button>
					</view>
				</view>
			</view>
		</view>




		



<view style="width:93%;height:1px;background-color:#F2F2F2;margin:0 auto;"></view>




	</block>
	<view :class="'cu-load '+(!di_msg?'loading':'over')"></view>
</view>
</view>
</template>

<script >
var app = getApp();
var http = require("../../util/http.js");

export default {
  /**
   * 页面的初始数据
   */
  data() {
    return {
      http_root: app.globalData.http_root,
      di_reply: false,
      page: 1,
      list: [],
      videoimage: "block",
      //默认显示封面
      bindplay: null,
      _index: 0,
      indexCurrent: null,
      version: 1,
      di_msg: false,
      check_user_login: false,
      index: 0
    }
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.get_video_list();
  },
  methods: {
    /**
     * 点赞
     */
    add_zan(data) {
      var e = app.globalData.getCache("userinfo");
      var id = data.currentTarget.dataset.id;
      var key = data.currentTarget.dataset.key;
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.id = id;
      params.uid = e.uid;
      params.applaud_type = 0;
      params.zan_type = this.list[key]['is_info_zan'] == true ? 1 : 0;
      var list = this.list;
      uni.vibrateShort();

      if (list[key]['is_info_zan'] == false) {
        this.$set(this.list[key], 'is_info_zan', true);
        this.$set(this.list[key], 'info_zan_count_this', list[key]['info_zan_count_this'] + 1);
      } else {
        this.$set(this.list[key], 'is_info_zan', false);
        this.$set(this.list[key], 'info_zan_count_this', list[key]['info_zan_count_this'] - 1 < 0 ? 0 : list[key]['info_zan_count_this'] - 1);
      }
      var b = app.globalData.api_root + 'User/add_user_zan';
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          if (res.data.status == "success") {} else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => {}
          });
        }
      });
    },
    get_video_list() {
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.page = this.page;
      var b = app.globalData.api_root + 'Index/get_video_list';
      var allMsg = this.list;
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          if (res.data.info.length == 0) {
            this.di_msg = true;
          }
          for (var i = 0; i < res.data.info.length; i++) {
            allMsg.push(res.data.info[i]);
          }
          this.list = allMsg;
          this.version = res.data.version;
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => {}
          });
        }
      });
    },
    bindplay(e) {
      var index = e.currentTarget.dataset.id;
      this.index = index;
      var videoContextPrev = uni.createVideoContext("myVideo" + this._index);
      videoContextPrev.seek(0);
      videoContextPrev.pause();
      setTimeout(() => {
        //将点击视频进行播放
        var videoContext = uni.createVideoContext('myVideo' + index);
        videoContext.play();
        this._index = index;
      }, 100);
    },
    // 只播放一个视频，其他视频暂停
    video_play(e) {
      var curIdx = e.currentTarget.id;
      // 没有播放时播放视频
      console.log(curIdx);
      if (!this.indexCurrent) {
        this.indexCurrent = curIdx;
        var videoContext = uni.createVideoContext(curIdx, this); //这里对应的视频id
        videoContext.play();
      } else {
        // 有播放时先将prev暂停，再播放当前点击的current
        var videoContextPrev = uni.createVideoContext(this.indexCurrent, this); //this是在自定义组件下，当前组件实例的this，以操作组件内 video 组件（在自定义组件中药加上this，如果是普通页面即不需要加）
        if (this.indexCurrent != curIdx) {
          console.log(123);
          videoContextPrev.pause();
          this.indexCurrent = curIdx;
          var videoContextCurrent = uni.createVideoContext(curIdx, this);
          videoContextCurrent.play();
        }
      }
    },
    /**
     * 点击话题
     */
    gambit_list(d) {
      var e = app.globalData.getCache("userinfo");
      var warrant_arbor = getApp().globalData.store.getState().copyright['warrant_arbor'];
      if (e.tourist == 1 && warrant_arbor == 1) {
        this.check_user_login = true;
        return;
      }
      var id = d.currentTarget.dataset.id;
      uni.navigateTo({
        url: '/yl_welore/pages/gambit/index?id=' + id
      });
    },
    /**
     * 首页跳转链接
     */
    home_url(dd) {
      var warrant_arbor = getApp().globalData.store.getState().copyright['warrant_arbor'];
      var key = dd.currentTarget.dataset.k; //跳转类型
      var e = app.globalData.getCache("userinfo");
      if (key == 1) {
        //头像跳转
        uni.navigateTo({
          url: '/yl_welore/pages/packageB/my_home/index?id=' + dd.currentTarget.dataset.user_id
        });
        return;
      }
      if (key == 2) {
        //圈子跳转
        uni.navigateTo({
          url: '/yl_welore/pages/packageA/circle_info/index?id=' + dd.currentTarget.dataset.id
        });
        return;
      }
      if (key == 3) {
        //内容跳转
        var douyin = app.globalData.__PlugUnitScreen('5fb4baf1f25fe251685b526dc8c30b8f');
        var info = this.list[dd.currentTarget.dataset.index];
        if (dd.currentTarget.dataset.type == 2 && info.is_buy == 0 && e.user_phone && douyin) {
          uni.navigateTo({
            url: '/yl_welore/pages/packageF/full_video/index?id=' + dd.currentTarget.dataset.id
          });
          return;
        }
        uni.navigateTo({
          url: '/yl_welore/pages/packageA/article/index?id=' + dd.currentTarget.dataset.id + '&type=' + dd.currentTarget.dataset.type
        });
        return;
      }
    },
  },
  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    uni.showNavigationBarLoading(); //在标题栏中显示加载
    //模拟加载
    setTimeout(() => {
      uni.hideNavigationBarLoading(); //完成停止加载
      uni.stopPullDownRefresh(); //停止下拉刷新
    }, 1500);
    this.list = [];
    this.page = 1;
    this.get_video_list();
  },
  /**
   * 加载下一页
   */
  onReachBottom() {
    this.page = this.page + 1;
    this.get_video_list();
  },
  onShow() {}
};
</script>
<style >
 page{
  background-color: #ffffff;
}
.myVideo{ 
  width:92%;
  margin: 0 auto;
  height: calc(9 * 750rpx / 16);
  background-color: #fff;
}
.controls{
  position:absolute;
  z-index:1; 
  background-color: #fff;    
  height: calc(9 * 750rpx / 16); 
  margin-top:-427rpx;
}
.play{ 
    position:absolute;
    left:50%;
    margin-top:-270rpx;
    margin-left:-50rpx;    

  }
  .video{
    text-align: center;

  }
.avatar {
  position:absolute;
  display: block;
  width: 56rpx;
  height: 56rpx;
  margin-top: 5rpx;
  margin-left: 30rpx;
  border-radius: 50%;
}
#name{
float: left;
  font-size:13px;
  margin-left:50px;
  margin-top:9px;
}
button::after {
  line-height: normal;
  font-size: 30rpx;
  width: 0;
  height: 0;
  top: 0;
  left: 0;
}

button {
  line-height: normal;
  display: block;
  padding-left: 0px;
  padding-right: 0px;
  background-color: rgba(255, 255, 255, 0);
  font-size: 30rpx;
  overflow: inherit;
}
  
</style>
<template>
    <view :class="'cu-modal ' + (check_phone ? 'show' : '')">
        <view class="cu-dialog">
            <view class="cu-bar bg-white justify-end">
                <view class="content">快捷登陆</view>
                <view class="action" @tap="hideModal">
                    <text class="cuIcon-close text-red"></text>
                </view>
            </view>
            <view class="bg-white" style="padding: 0rpx 50rpx 50rpx 50rpx">
                <view style="text-align: center">
                    <image :src="$state.copyright.sgraph" mode="widthFix" style="width: 120px; height: 120px"></image>
                </view>
                <view style="font-size: 12px" class="text-grey">根据《网络安全法》相关规定，请绑定手机号</view>
                <view class="padding flex flex-direction">
                    <button style="height: 80rpx; width: 100%" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber" class="cu-btn round bg-black" >
                        登陆
                    </button>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
// tabBarComponent/tabBar.js
var app = getApp();
var http = require('../http.js');

export default {
    /**
     * 组件的属性列表
     */
    props: {
        check_phone: {
            type: Boolean,
            default: false
        }
    },
    /**
     * 组件的方法列表
     */
    methods: {
        hideModal() {
            this.$emit('close_phone_modal');
        },
        /**
         * 获取手机号
         */
        getPhoneNumber(c) {
            console.log(c);
            if (c.detail.errMsg == 'getPhoneNumber:ok') {
                var b = app.globalData.api_root + 'Service/get_user_phone_new';
                var that = this;
                var e = app.globalData.getCache('userinfo');
                console.log(e);
                var params = new Object();
                params.token = e.token;
                params.openid = e.openid;
                params.uid = e.uid;
                params.code = c.detail.code;
                http.POST(b, {
                    params: params,
                    success: (res) => {
                        console.log(res);
                        if (res.data.status == 'success') {
                            var e = app.globalData.getCache('userinfo');
                            e.user_phone = res.data.phone;
                            console.log(e);
                            app.globalData.setCache('userinfo', e);
                            uni.showToast({
                                title: res.data.msg,
                                icon: 'none',
                                duration: 2000
                            });
                        } else {
                            uni.showModal({
                                title: '提示',
                                content: res.data.msg,
                                showCancel: false,
                                success: () => {}
                            });
                        }
                        that.$emit('close_phone_modal');
                    },
                    fail: () => {
                        uni.showModal({
                            title: '提示',
                            content: '网络繁忙，请稍候重试！',
                            showCancel: false,
                            success: () => {}
                        });
                    }
                });
            } else {
                uni.showModal({
                    title: '提示',
                    content: c.detail.errMsg,
                    showCancel: false,
                    success: () => {}
                });
            }
        }
    }
}
</script>
<style></style>

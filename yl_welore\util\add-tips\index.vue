<template>
    <view :class="'box animation-' + get_animation + ' ' + (get_animation == 'slide-right' ? 'animation-reverse' : '')" v-if="show_top" :style="'top:' + (CustomBar - 10) + 'px;'">
        <view class="arrow"></view>
        <view class="body">
            <text style="vertical-align: middle" class="man_font">添加到「我的小程序</text>
            <text class="cuIcon-favorfill lg" style="color: #f8c346; font-size: 12px"></text>
            <text style="vertical-align: middle" class="man_font">」，访问更便捷</text>
            <text @tap="off_top" class="cuIcon-close lg text-gray" style="font-size: 12px; margin-left: 5px"></text>
        </view>
    </view>

    <!-- modal -->
    <!-- <view class='modal' wx:if="{{SHOW_MODAL}}">
  <view style='flex-direction: row;align-items:center;'>
    <text>1. 点击</text>
    <image src='./assets/fav-1.jpg' style='width:100px;height:40px;'></image>
  </view>
  <view>
    <text>2. 点击「添加到我的小程序」</text>
    <image src='./assets/fav-2.jpg' style='width:100%;height:200px;'></image>
  </view>
  <view>
    <text>3. 微信首页下拉，快速进入小程序</text>
    <image src='./assets/fav-3.jpg' style='width:100%;height:150px;'></image>
  </view>
  <view class='ok-btn' hover-class='btn-hover' bindtap='okHandler'>
    <view>
      <text>我知道了！</text>
    </view>
  </view>
</view> -->
</template>

<script>
const STORAGE_KEY = 'PLUG-ADD-MYAPP-KEY';

export default {
    /**
     * 组件的属性列表
     */
    props: {
        // 提示文字
        text: {
            type: String,
            default: '添加到「我的小程序」，下次访问更便捷'
        },
        // 多少秒后关闭
        duration: {
            type: Number,
            default: 5
        }
    },
    /**
     * 组件的初始数据
     */
    data() {
        return {
            show_top: false,
            CustomBar: uni.getSystemInfoSync().statusBarHeight + 44,
            get_animation: ''
        }
    },
    mounted() {
        // 判断是否已经显示过
        let cache = uni.getStorageSync(STORAGE_KEY);
        if (cache) {
            return;
        }
        // 没显示过，则进行展示
        this.show_top = true;
        this.get_animation = 'fade';

        // 关闭时间
        setTimeout(() => {
            uni.setStorage({
                key: STORAGE_KEY,
                data: +new Date()
            });
            this.get_animation = 'slide-right';
        }, this.duration * 1000);
    },
    /**
     * 组件的方法列表
     */
    methods: {
        off_top() {
            uni.setStorage({
                key: STORAGE_KEY,
                data: +new Date()
            });
            this.get_animation = 'slide-right';
        }
    }
}
</script>
<style>
@import '../../colorui/animation.css';
@import '../../colorui/icon.css';
.box {
    position: fixed;
    top: 10%;
    /* left: 0; */
    right: 0;
    z-index: 999;
    display: flex;
    justify-content: flex-end;
    align-items: flex-end;
    flex-direction: column;
    width: 600rpx;
}
.arrow {
    width: 0;
    height: 0;
    margin-right: 120rpx;
    border-width: 20rpx;
    border-style: solid;
    border-color: transparent transparent #cce6ff transparent;
}
.body {
    background-color: #cce6ff;
    box-shadow: 0 10rpx 20rpx -10rpx #cce6ff;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 70rpx;
    padding: 0 20rpx;
    margin-right: 40rpx;
}
.man_font {
    color: #0081ff;
    font-size: 12px;
    font-weight: 400;
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 20px;
}
.modal > view {
    margin: 10px 0;
    display: flex;
    /* align-items: center; */
    flex-direction: column;
}
.modal > view > text {
    font-size: 16px;
    font-weight: 400;
    margin-bottom: 5px;
    color: #333;
}
.modal > view > image {
    border-radius: 10px;
}

.ok-btn {
    width: 100%;
    margin-top: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.ok-btn > view {
    height: 40px;
    width: 120px;
    background-color: #34b5e2;
    box-shadow: 0 5px 10px -px #34b5e2;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 40px;
}
.ok-btn > view > text {
    font-size: 14px;
    color: #fff;
    font-weight: 400;
}
.btn-hover {
    opacity: 0.6;
}
</style>

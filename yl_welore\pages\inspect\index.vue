<template>
    <view class="page">
        <view class="wx_user_login_box">
            <view class="wx_user_face" style="">
                <icon type="warn" size="93"></icon>
            </view>
            <view class="wx_user_face" style="width: 100%">
                <text style="font-size: 48rpx; color: #f76260">站点未授权</text>
            </view>
            <view class="wx_login_info">
                <text style="font-size: 28rpx; color: #999999">该站点暂未授权，请联系客服授权！</text>
            </view>
        </view>
    </view>
</template>

<script>
var app = getApp();
export default {
    data() {
        return {};
    }
    /**
     * 生命周期函数--监听页面加载
     */,
    onLoad (options) {
        uni.hideShareMenu();
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow () {
        uni.hideShareMenu();
    },
    methods: {
        onGotUserInfo (q) {
            var that = this;
            var e = app.globalData.getCache('userinfo');
            if (e) {
                return;
            }
            app.globalData.getUserInfo(q.detail.userInfo, (t)=> {
                if (app != 1000) {
                    uni.navigateBack();
                } else {
                }
            });
        }
    }
};
</script>
<style>
/* yl_welore/pages/author/index.wxss */
</style>

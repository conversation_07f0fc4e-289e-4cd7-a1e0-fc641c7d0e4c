<template>
    <view>
        <web-view @message="getMsg" :src="url"></web-view>
    </view>
</template>

<script>
const app = getApp();
export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            http_root: app.globalData.http_root,
            url: ''
        }
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        var pid = options.pid;
        var e = app.globalData.getCache('userinfo');
        var url = this.http_root + 'addons/yl_welore/web/index.php?s=/api/wechat/local_upload&open_id=' + e.openid + '&token=' + e.token + '&much_id=' + e.much_id + '&pid=' + pid;
        console.log(url);
        this.url = url;
    },
    methods: {
        getMsg(options) {
            console.log(options);
            var pages = getCurrentPages();
            var prevPage = pages[pages.length - 2]; //上一个页面
            prevPage.onload = 1;
            // 将我们想要传递的参数在这里直接赋值。上个页面就会执行这里的操作。
        }
    }
}
</script>
<style>
/* yl_welore/pages/packageF/netdisc_local/index.wxss */
</style>

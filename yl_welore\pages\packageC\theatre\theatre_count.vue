<template>
    <view>
        <swiper @animationfinish="changeSwiper" :current="current" :circular="circular" vertical style="height: 100vh;">
            <swiper-item v-for="(item, index) in videoList" :key="index" style="position: relative;" :animation="current==index?animBack:''">
                <view v-if="!select"
                    style="color: #ffffff;min-height: 220rpx;background-color: rgba(0, 0, 0, 0.5);position: absolute;bottom: 125rpx;z-index: 1;width: 100%;padding:20rpx">
                    <view class="flex justify-between align-center" style="letter-spacing: 2rpx;">
                        <view class="text_num_1" style="width: 520rpx;">
                            <text class="cicon-record"
                                style="color: orange;font-size: 35rpx;vertical-align: middle;"></text>
                            <text style="margin-left: 10rpx;vertical-align: middle;">{{ item.title }}</text>
                            <text style="margin-left: 20rpx;vertical-align: middle;">{{ item.total_episodes
                                }}集全</text>
                        </view>
                        <view>
                            <button @tap.stop="open_mode" :data-id="item.msi_id"
                                class="cu-btn round lines-white sm">查看全集</button>
                        </view>
                    </view>
                    <view class="text_num" style="margin-top: 20rpx;">
                        <text>{{ item.plot_summary }}</text>
                    </view>
                </view>
                <view v-if="current==index" style="width: 100%;height: 100%;">
                    <video id="myVideo" :src="item.msi_episode_url" :controls="false" object-fit="contain"
                        :loop="true" initial-time='0.01' :custom-cache="false" :danmu-btn="false"
                        @waiting="videoWaiting" @timeupdate="timeupdate" @loadedmetadata="loadedmetadata"
                        @progress="progress" @play="eventPlay" :autoplay="true" :show-progress="true"
                        :show-fullscreen-btn="false" :show-play-btn="false" :show-center-play-btn="false"
                        :enable-progress-gesture="true" @tap.stop="playOrPause"></video>
                </view>
                <!-- 暂停 -->
                <view v-if="!isPlaying" :data-index="index" @tap.stop="playOrPause">
                    <image src="/static/yl_welore/style/icon/home_bf.png" class="pause"></image>
                </view>
            </swiper-item>
        </swiper>
    </view>
</template>

<script>
export default {
    props: ['data', 'compName'],
    computed: {
        current() {
            return this.$parent.$data.current;
        },
        circular() {
            return this.$parent.$data.circular;
        },
        videoList() {
            return this.$parent.$data.videoList;
        },
        select(){
            return this.$parent.$data.select;
        },
        animBack() {
            return this.$parent.$data.animBack;
        },
        isPlaying() {
            return this.$parent.$data.isPlaying;
        },
    },
    methods:{
        get_ser_name(e){
            this.$emit('get_ser_name', e);
        },
        changeSwiper(e){
            this.$emit('changeSwiper', e);
        },
        open_mode(e){
            this.$emit('open_mode', e);
        },
        videoWaiting(e){
            this.$emit('videoWaiting', e);
        },
        timeupdate(e){
            this.$emit('timeupdate', e);
        },
        loadedmetadata(e){
            this.$emit('loadedmetadata', e);
        },
        progress(e){
            this.$emit('progress', e);
        },
        eventPlay(e){
            this.$emit('eventPlay', e);
        },
        playOrPause(e){
            this.$emit('playOrPause', e);
        },
    }
};
</script>

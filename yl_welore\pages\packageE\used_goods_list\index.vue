<template>
    <view class="page-container">
        <cu-custom bgColor="bg-gradient" :isBack="true" :isSearch="false">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">🛍️ {{ design.custom_title }}</view>
        </cu-custom>
        <view class="search-container">
            <view class="search-form-modern">
                <text class="cuIcon-search search-icon"></text>
                <input @input="get_ser_name" :value="content" type="text" placeholder="输入物品名搜搜看..." class="search-input" />
            </view>
            <view class="action">
                <button @tap="sou" class="search-btn">搜索</button>
            </view>
        </view>
        <view class="tab-container">
            <ui-tab @change="handleChange" :tab="tab_list" mark="text-blue" tpl="long" scroll />
        </view>
        <view style="padding: 10rpx 0px 140rpx 0px">
            <view class="grid col-2">
                <block v-for="(item, index) in list" :key="index">
                    <view class="padding-xs" @tap="get_url" :data-id="item.id" :style="'max-height: ' + (current == 'tab1' ? 700 : 800) + 'rpx;position: relative;'">
                        <image
                            v-if="item.item_status == 2 && item.release_type == 0"
                            class="now_level"
                            :src="http_root + 'addons/yl_welore/web/static/applet_icon/g1.png'"
                            style="z-index: 10; width: 140rpx; height: 140rpx; position: absolute"
                        ></image>
                        <image
                            v-if="item.item_status == 2 && item.release_type == 1"
                            class="now_level"
                            :src="http_root + 'addons/yl_welore/web/static/applet_icon/g2.png'"
                            style="z-index: 10; width: 140rpx; height: 140rpx; position: absolute"
                        ></image>
                        <image
                            v-if="item.item_status == 2 && item.release_type == 2"
                            class="now_level"
                            :src="http_root + 'addons/yl_welore/web/static/applet_icon/g3.png'"
                            style="z-index: 10; width: 140rpx; height: 140rpx; position: absolute"
                        ></image>
                        <image
                            v-if="item.item_status == 2 && item.release_type == 3"
                            class="now_level"
                            :src="http_root + 'addons/yl_welore/web/static/applet_icon/g3.png'"
                            style="z-index: 10; width: 140rpx; height: 140rpx; position: absolute"
                        ></image>
                        <image
                            v-if="item.item_status == 2 && item.release_type == 4"
                            class="now_level"
                            :src="http_root + 'addons/yl_welore/web/static/applet_icon/g3.png'"
                            style="z-index: 10; width: 140rpx; height: 140rpx; position: absolute"
                        ></image>
                        <view class="item-card">
                            <image v-if="item.image_part.length > 0" class="now_level item-image" :src="item.image_part[0]" mode="aspectFill"></image>
                            <image
                                v-if="item.image_part.length == 0"
                                class="now_level item-image"
                                :src="http_root + 'addons/yl_welore/web/static/applet_icon/zanwu.jpg'"
                                mode="aspectFill"
                            ></image>
                            <view class="item-content">
                                <view class="item-name">{{ item.item_name }}</view>
                                <view class="flex justify-between align-end">
                                    <view class="flex-sub item-price-container">
                                        <view class="price-highlight">
                                            <text v-if="item.item_price >= 0">¥{{ item.item_price }}</text>
                                            <text v-else>💰 {{ item.item_price }}</text>
                                        </view>
                                    </view>
                                    <view class="status-tag-container">
                                        <text v-if="item.release_type == 0" class="status-tag status-sale">🛒 {{ item.release_type_name }}</text>
                                        <text v-if="item.release_type == 1" class="status-tag status-exchange">🔄 {{ item.release_type_name }}</text>
                                        <text v-if="item.release_type == 2" class="status-tag status-gift">🎁 {{ item.release_type_name }}</text>
                                        <text v-if="item.release_type == 3" class="status-tag status-wanted">🔍 {{ item.release_type_name }}</text>
                                        <text v-if="item.release_type == 4" class="status-tag status-other">📦 {{ item.release_type_name }}</text>
                                    </view>
                                </view>
                                <view class="item-info">
                                    <text class="info-time">🕒 {{ item.create_time }}</text>
                                    <!-- <text class="cicon-location-on location-icon"></text> -->
                                    <text class="info-location">📍 {{ item.secondhand_address }}</text>
                                </view>
                                <view v-if="current == 'tab2'" class="audit-status">
                                    <text class="audit-pending" v-if="item.audit_status == 0">⏳ 审核中</text>
                                    <text class="audit-passed" v-if="item.audit_status == 1">✅ 审核通过</text>
                                    <text class="audit-failed" v-if="item.audit_status == 2">❌ 审核未通过：</text>
                                    <view v-if="item.audit_status == 2" class="audit-reason">{{ item.audit_reason }}</view>
                                </view>
                                <view v-if="current == 'tab2'" class="delete-container">
                                    <view @tap.stop.prevent="DelInfo" :data-id="item.id" class="delete-btn">🗑️ 删除</view>
                                </view>
                            </view>
                        </view>
                    </view>
                </block>
            </view>
            <view style="clear: both; height: 0"></view>
            <view :class="'cu-load ' + (!di_msg ? 'loading' : 'over')"></view>
        </view>
        <view class="bottom-tabbar">
            <view @tap="handleChangeDi" data-key="tab1" :class="'tab-action ' + (current == 'tab1' ? 'tab-active' : 'tab-inactive')">
                <view class="tab-icon">🏠</view>
                <text class="tab-text">首页</text>
            </view>
            <view class="add-action-container">
                <button
                    @tap="add_local"
                    data-key="0"
                    class="add-btn"
                >
                    <text class="cuIcon-add"></text>
                </button>
            </view>
            <view @tap="handleChangeDi" data-key="tab2" :class="'tab-action ' + (current == 'tab2' ? 'tab-active' : 'tab-inactive')">
                <view class="tab-icon">😀</view>
                <text class="tab-text">我的</text>
            </view>
        </view>
    </view>
</template>

<script>
import uiTab from '@/yl_welore/colorui/ui-tab/ui-tab';
const app = getApp();
var http = require('../../../util/http.js');

export default {
    components: {
        uiTab
    },
    /**
     * 页面的初始数据
     */
    data() {
        return {
            http_root: app.globalData.http_root,
            type_id: 0,
            current: 'tab1',
            page: 1,
            list: [],
            tab_list: [
                {
                    realm_name: '最新',
                    id: 0
                }
            ],
            content: '',
            design: {},
            di_msg: false
        }
    },
    onShow() {
        var lost = app.globalData.__PlugUnitScreen('a11eb9c1955977a6d890dca4991209f6');
        if (!lost) {
            uni.showToast({
                title: '未开通插件',
                icon: 'none',
                duration: 2000
            });
            setTimeout(() => {
                this.BackPage();
            }, 1000);
            return;
        }
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.get_list();
        this.get_lost_type();
        var dd = uni.getStorageSync('is_diy');
        if (dd) {
            this.design = dd;
        } else {
            this.get_diy();
        }
    },
    /**
     * 下拉刷新
     */
    onPullDownRefresh() {
        //模拟加载
        setTimeout(() => {
            uni.hideNavigationBarLoading(); //完成停止加载
            uni.stopPullDownRefresh(); //停止下拉刷新
        }, 1500);
        this.list = [];
        this.page = 1;
        this.di_msg = false;
        this.get_list();
    },
    onReachBottom() {
        this.page = this.page + 1;
        this.get_list();
    },
    methods: {
        DelInfo(d) {
            var id = d.currentTarget.dataset.id;
            uni.showModal({
                title: '提示',
                content: '确定删除吗？',
                success: (res) => {
                    if (res.confirm) {
                        this.DelInfoDo(id);
                    }
                }
            });
        },
        DelInfoDo(id) {
            var b = app.globalData.api_root + 'Used/DelInfoDo';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = id;
            http.POST(b, {
                params: params,
                success: (res) => {
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none',
                        duration: 2000
                    });
                    this.page = 1;
                    this.list = [];
                    this.get_list();
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },
        get_diy() {
            var b = app.globalData.api_root + 'User/get_diy';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    this.design = res.data;
                    uni.setStorageSync('is_diy', res.data);
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },
        get_ser_name(d) {
            this.content = d.detail.value;
        },
        sou() {
            this.list = [];
            this.page = 1;
            this.type_id = 0;
            this.di_msg = false;
            this.get_list();
        },
        handleChangeDi(detail) {
            var key = detail.currentTarget.dataset.key;
            console.log(key);
            this.list = [];
            this.page = 1;
            this.current = key;
            this.di_msg = false;
            this.get_list();
        },
        handleChange(detail) {
            var id = detail.detail.data.id;
            this.list = [];
            this.page = 1;
            this.type_id = id;
            this.di_msg = false;
            this.get_list();
        },
        get_lost_type() {
            var b = app.globalData.api_root + 'Used/getLostType';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            http.POST(b, {
                params: params,
                success: (res) => {
                    var tab_list = this.tab_list;
                    tab_list.push(...res.data);
                    this.tab_list = tab_list;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },
        add_local() {
            uni.navigateTo({
                url: '/yl_welore/pages/packageE/used_goods_add/index'
            });
        },
        get_url(d) {
            console.log(d);
            var id = d.currentTarget.dataset.id;
            uni.navigateTo({
                url: '/yl_welore/pages/packageE/used_goods_info/index?id=' + id
            });
        },
        get_list() {
            var b = app.globalData.api_root + 'Used/getLostList';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.page = this.page;
            params.type_id = this.type_id;
            params.current = this.current;
            params.search = this.content;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.length == 0 || res.data.length < 7) {
                        this.di_msg = true;
                    }
                    var list = this.list;
                    list.push(...res.data);
                    this.list = list;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        }
    }
};
</script>
<style>
page {
    background: linear-gradient(135deg, #e3f2fd 0%, #f0f8ff 50%, #e8f5e8 100%);
    min-height: 100vh;
}

/* 页面容器 */
.page-container {
    background: linear-gradient(135deg, #e3f2fd 0%, #f0f8ff 50%, #e8f5e8 100%);
    min-height: 100vh;
}

/* 搜索容器样式 */
.search-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8fbff 100%);
    padding: 20rpx;
    margin: 10rpx 20rpx;
    border-radius: 25rpx;
    box-shadow: 0 8rpx 25rpx rgba(33, 150, 243, 0.15);
    display: flex;
    align-items: center;
    gap: 20rpx;
}

.search-form-modern {
    flex: 1;
    background: #f8fbff;
    border-radius: 50rpx;
    padding: 15rpx 25rpx;
    display: flex;
    align-items: center;
    border: 2rpx solid #e3f2fd;
    transition: all 0.3s ease;
}

.search-form-modern:focus-within {
    border-color: #2196f3;
    box-shadow: 0 0 0 4rpx rgba(33, 150, 243, 0.1);
}

.search-icon {
    color: #2196f3;
    margin-right: 15rpx;
    font-size: 32rpx;
}

.search-input {
    flex: 1;
    border: none;
    background: transparent;
    font-size: 28rpx;
    color: #333;
}

.search-btn {
    background: linear-gradient(135deg, #2196f3 0%, #21cbf3 100%);
    color: white;
    border: none;
    border-radius: 50rpx;
    padding: 5rpx 30rpx;
    font-size: 28rpx;
    font-weight: 600;
    box-shadow: 0 6rpx 20rpx rgba(33, 150, 243, 0.3);
    transition: all 0.3s ease;
    width: 160rpx;
}

.search-btn:active {
    transform: translateY(2rpx);
    box-shadow: 0 4rpx 15rpx rgba(33, 150, 243, 0.4);
}

/* 标签容器 */
.tab-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8fbff 100%);
    margin: 10rpx 20rpx;
    border-radius: 20rpx;
    box-shadow: 0 6rpx 20rpx rgba(33, 150, 243, 0.1);
    overflow: hidden;
}

/* 物品卡片样式 */
.item-card {
    background: linear-gradient(135deg, #ffffff 0%, #fafbff 100%);
    border-radius: 20rpx;
    overflow: hidden;
    box-shadow: 0 8rpx 25rpx rgba(33, 150, 243, 0.12);
    transition: all 0.3s ease;
    height: 100%;
}

.item-card:active {
    transform: translateY(-5rpx);
    box-shadow: 0 15rpx 35rpx rgba(33, 150, 243, 0.2);
}

.item-image {
    width: 100%;
    height: 360rpx;
    border-radius: 20rpx 20rpx 0 0;
}

.item-content {
    padding: 20rpx 25rpx 30rpx 25rpx;
}

.item-name {
    height: 76rpx;
    font-size: 28rpx;
    font-weight: 500;
    letter-spacing: 1rpx;
    color: #2c3e50;
    line-height: 38rpx;
    margin-bottom: 15rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.item-price-container {
    margin-top: 15rpx;
}

.price-highlight {
    color: #e74c3c;
    display: inline-block;
    font-size: 32rpx;
    font-weight: 700;
    text-shadow: 0 2rpx 4rpx rgba(231, 76, 60, 0.2);
    padding-bottom: 2rpx;
}

.status-tag-container {
    margin-top: 15rpx;
}

.status-tag {
    padding: 8rpx 15rpx;
    border-radius: 15rpx;
    font-size: 22rpx;
    font-weight: 500;
    display: inline-block;
}

.status-sale {
    background: linear-gradient(135deg, #2196f3 0%, #42a5f5 100%);
    color: white;
    box-shadow: 0 4rpx 15rpx rgba(33, 150, 243, 0.3);
}

.status-exchange {
    background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%);
    color: white;
    box-shadow: 0 4rpx 15rpx rgba(76, 175, 80, 0.3);
}

.status-gift {
    background: linear-gradient(135deg, #ff9800 0%, #ffb74d 100%);
    color: white;
    box-shadow: 0 4rpx 15rpx rgba(255, 152, 0, 0.3);
}

.status-wanted {
    background: linear-gradient(135deg, #f44336 0%, #ef5350 100%);
    color: white;
    box-shadow: 0 4rpx 15rpx rgba(244, 67, 54, 0.3);
}

.status-other {
    background: linear-gradient(135deg, #9c27b0 0%, #ba68c8 100%);
    color: white;
    box-shadow: 0 4rpx 15rpx rgba(156, 39, 176, 0.3);
}

._this {
    font-weight: 600;
    font-size: 20px;
}
/* 物品信息样式 */
.item-info {
    text-align: center;
    font-size: 22rpx;
    color: #95a5a6;
    margin-top: 20rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10rpx;
}

.info-time, .info-location {
    font-weight: 400;
}

.location-icon {
    color: #3498db;
    margin: 0 5rpx;
}

/* 审核状态样式 */
.audit-status {
    font-size: 24rpx;
    margin: 20rpx 0;
    text-align: center;
}

.audit-pending {
    color: #f39c12;
    font-weight: 500;
}

.audit-passed {
    color: #27ae60;
    font-weight: 500;
}

.audit-failed {
    color: #e74c3c;
    font-weight: 500;
}

.audit-reason {
    color: #7f8c8d;
    font-size: 22rpx;
    margin-top: 10rpx;
    line-height: 1.4;
}

/* 删除按钮样式 */
.delete-container {
    text-align: center;
    margin-top: 20rpx;
}

.delete-btn {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
    padding: 12rpx 30rpx;
    border-radius: 25rpx;
    font-size: 24rpx;
    font-weight: 500;
    display: inline-block;
    box-shadow: 0 4rpx 15rpx rgba(231, 76, 60, 0.3);
    transition: all 0.3s ease;
}

.delete-btn:active {
    transform: translateY(2rpx);
    box-shadow: 0 2rpx 10rpx rgba(231, 76, 60, 0.4);
}

.mask1 {
    height: 48px;
    width: 48px;
    background-color: #ffffff;
    opacity: 0.85;
    z-index: 1000;
    border-radius: 750rpx;
    transform: scale(0);
    position: fixed;
    bottom: 0;
    left: 50%;
    margin-left: -24px;
}

.maskOpen {
    animation: maskO 0.5s both;
}

.maskClose {
    animation: maskC 0.3s both;
}

@keyframes maskO {
    0% {
        transform: scale(0);
    }

    20% {
        transform: scale(4);
    }

    40% {
        transform: scale(18);
    }

    60% {
        transform: scale(24);
    }
    80% {
        transform: scale(38);
    }
    100% {
        transform: scale(48);
    }
}

@keyframes maskC {
    0% {
        transform: scale(48);
    }

    25% {
        transform: scale(24);
    }

    100% {
        transform: scale(0);
    }
}

.btn {
    width: 100%;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    z-index: 1005;
    bottom: calc(10rpx + env(safe-area-inset-bottom));
}

.btn-main {
    border-radius: 50%;
    z-index: 1005;
    height: 48px;
    font-size: 28px;
    width: 48px;
    line-height: 48px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    color: #fff;
    background: #fbbd08;
}

.menuOpen {
    animation: menuO 0.3s both;
}

.menuClose {
    animation: menuC 0.3s both;
}

@keyframes menuO {
    0% {
        transform: rotate(0deg);
        color: #fff;
        background: #fbbd08;
    }

    100% {
        transform: rotate(45deg);
        color: #000;
        background: #fff;
    }
}

@keyframes menuC {
    0% {
        transform: rotate(45deg);
        color: #000;
        background: #fff;
    }

    100% {
        transform: rotate(-0deg);
        color: #fff;
        background: #fbbd08;
    }
}

.menu-container {
    position: fixed;
    width: 100%;
    z-index: 1002;
    bottom: 0rpx;
}

.add_menu {
    padding-bottom: calc(48px + 40rpx + env(safe-area-inset-bottom));
}

.menu-list {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    width: 100%;
    padding-bottom: 15rpx;
}

.menu-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    animation: bounceInDown 0.45s linear both;
}

.menu-icon {
    width: 110rpx;
    height: 110rpx;
    margin-bottom: 15rpx;
}

.menu-name {
    color: #333;
    font-size: 25rpx;
    font-weight: bold;
    margin-bottom: 10rpx;
    letter-spacing: 1px;
}

@keyframes bounceInDown {
    0% {
        opacity: 0;
        transform: translateY(100%);
    }

    60% {
        transform: translateY(-10%);
    }

    80% {
        transform: translateY(10%);
    }

    100% {
        opacity: 1;
        transform: translateY(0%);
    }
}

/* 底部导航栏样式 */
.bottom-tabbar {
    position: fixed;
    bottom: 0;
    width: 100%;
    z-index: 2000;
    background: linear-gradient(135deg, #ffffff 0%, #f8fbff 100%);
    border-top: 1rpx solid #e3f2fd;
    box-shadow: 0 -8rpx 25rpx rgba(33, 150, 243, 0.1);
    display: flex;
    align-items: center;
    padding: 15rpx 0 calc(15rpx + env(safe-area-inset-bottom));
}

.tab-action {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.tab-active {
    color: #2196f3;
}

.tab-inactive {
    color: #95a5a6;
}

.tab-icon {
    font-size: 48rpx;
    margin-bottom: 8rpx;
    line-height: 1;
}

.tab-text {
    font-size: 24rpx;
    font-weight: 500;
    line-height: 1;
}

.add-action-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.add-btn {
    width: 100rpx;
    height: 100rpx;
    border-radius: 50rpx;
    background: linear-gradient(135deg, #2196f3 0%, #21cbf3 100%);
    color: white;
    border: none;
    font-size: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8rpx 25rpx rgba(33, 150, 243, 0.4);
    position: relative;
    top: -25rpx;
    transition: all 0.3s ease;
}

.add-btn:active {
    transform: translateY(3rpx);
    box-shadow: 0 5rpx 20rpx rgba(33, 150, 243, 0.5);
}
</style>

<template>
    <view>
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
            <view slot="backText"></view>
            <view slot="content" style="color: #000000; font-weight: 600; font-size: 36rpx">{{ info.at_name }}</view>
        </cu-custom>
        <view style="padding: 20px 20px 280rpx 20px">
            <rich-text :nodes="info.introduction"></rich-text>
        </view>
        <view style="text-align: center; padding: 40px 0px; position: fixed; bottom: 3%; z-index: 100; margin: 0 auto; left: 0; right: 0">
            <button @tap="open_form" class="cu-btn lg bg-orange shadow">我已知晓，开始认证</button>
            <!-- <button wx:if="{{info.check==1}}" class="cu-btn block lg bg-grey" style="width: 70%;margin: 0 auto;">申请超限</button> -->
        </view>
    </view>
</template>

<script>
const app = getApp();
var http = require('../../../util/http.js');

export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            info: [],
            id: 0
        }
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        uni.hideShareMenu();
        this.id = options.id;
        this.get_rz_info();
    },
    methods: {
        linktap(e) {
            console.log(e);
            var key = e.detail.jump_type;
            if (key == 0) {
                return;
            } else if (key == 1) {
                uni.navigateToMiniProgram({
                    appId: e.detail.data_appid,
                    path: e.detail.href
                });
            } else {
                uni.navigateTo({
                    url: '/yl_welore/pages/web/index?url=' + encodeURIComponent(e.detail.href)
                });
            }
        },
        get_rz_info() {
            var b = app.globalData.api_root + 'Ranking/get_rz_info';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.id;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    that.info = res.data.info;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: () => {}
                    });
                }
            });
        },
        open_form() {
            uni.redirectTo({
                url: '/yl_welore/pages/packageE/certification_form/index?id=' + this.id
            });
        }
    }
}
</script>
<style>
page {
    background-color: #ffffff;
}
rich-text {
    line-height: 50rpx;
}
</style>

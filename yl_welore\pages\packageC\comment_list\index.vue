<template>
    <view>
        <cu-custom :isCustom="true" bgColor="bg-white" :isSearch="false">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">内容点评</view>
        </cu-custom>
        <view class="bg-white" style="padding: 10px" v-if="open_user_dianping && list">
            <block v-for="(item, rr_index) in list" :key="rr_index">
                <view :class="'cu-list menu-avatar dasheds ' + (item.is_show == 0 ? 'border-orange' : 'border-blue')">
                    <view class="cu-item" @tap="open_comment_info" :data-index="rr_index">
                        <view class="cu-avatar round" :style="'width: 70rpx;height: 70rpx;background-image:url(' + item.user_head_sculpture + ');'"></view>
                        <view class="content" style="left: 120rpx; width: 70%">
                            <view class="text-grey">
                                <text class="text_num_1">{{ item.user_nick_name }}</text>
                                <text
                                    :class="'cicon-star ' + (item.assess_score > sss_index ? 'text-yellow' : 'text-grey')"
                                    style="margin-left: 5px; font-size: 20px; vertical-align: middle"
                                    v-for="(k_item, sss_index) in 5"
                                    :key="sss_index"
                                ></text>
                            </view>
                            <view class="text-gray text-sm flex">
                                <view class="text-cut">
                                    <text
                                        :class="'cu-tag ' + (item.is_show == 0 ? 'bg-orange' : 'bg-blue') + ' sm round'"
                                        style="vertical-align: middle; line-height: 30rpx; margin-left: 0rpx"
                                    >
                                        {{ item.is_show == 0 ? '私密点评' : '公开点评' }}
                                    </text>
                                    <text style="padding-left: 10rpx; vertical-align: middle">{{ item.assess_content }}</text>
                                </view>
                            </view>
                        </view>
                        <view class="action">
                            <view class="cicon-angle" style="font-size: 18px"></view>
                        </view>
                    </view>
                </view>
            </block>
            <view :class="'cu-load ' + (!di_msg ? 'loading' : 'over')"></view>
        </view>
        <view :class="'cu-modal ' + (CommentInfoMod ? 'show' : '')">
            <view class="cu-dialog">
                <view class="cu-bar bg-white justify-end">
                    <view class="content">
                        <text
                            :class="'cicon-star ' + (CommentInfo.assess_score > ss_index ? 'text-yellow' : 'text-grey')"
                            style="font-size: 25px; vertical-align: middle"
                            v-for="(item, ss_index) in 5"
                            :key="ss_index"
                        ></text>
                        <text style="text-align: center; font-size: 15px; vertical-align: middle; margin-left: 5px">{{ CommentName[CommentInfo.assess_score - 1] }}</text>
                    </view>
                    <view class="action" @tap="hideModal">
                        <text class="cuIcon-close text-red"></text>
                    </view>
                </view>
                <view class="padding">
                    <view class="padding-sm" style="letter-spacing: 0.5px; line-height: 25px">
                        {{ CommentInfo.assess_content }}
                    </view>
                    <view class="flex p-xs margin-bottom-sm mb-sm">
                        <view class="flex-sub">
                            <view @tap="OkComment" class="bg-green padding-sm margin-xs radius text-center shadow-blur">
                                <view class="text-lg text-white">通过</view>
                            </view>
                        </view>
                        <view class="flex-sub">
                            <view @tap="refuseModal" class="bg-yellow padding-sm margin-xs radius text-center shadow-blur">
                                <view class="text-lg text-white">拒绝</view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view :class="'cu-modal ' + (refuMod ? 'show' : '')">
            <view class="cu-dialog">
                <view class="cu-bar bg-white justify-end">
                    <view class="content">未通过审核</view>
                    <view class="action" @tap="hideModalRef">
                        <text class="cuIcon-close text-red"></text>
                    </view>
                </view>
                <view v-if="refuMod" class="padding-xl">
                    <textarea style="min-height: 4em; text-align: left" @input="get_shenqing_text" :auto-height="true" value="" maxlength="300" placeholder="未通过原因" />
                </view>
                <view class="cu-bar bg-white justify-end">
                    <view class="action">
                        <button class="cu-btn line-green text-green" @tap="hideModalRef">取消</button>
                        <button class="cu-btn bg-green margin-left" @tap="shenqing_submit">确定</button>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import http from '../../../util/http.js';
const app = getApp();
export default {
    data() {
        return {
            id: 0,
            page: 1,
            list: [],
            open_user_dianping: false,
            CommentInfoMod: false,
            CommentInfo: {},
            shenqing_text: '',
            di_msg: false,
            refuMod: false,
            CommentName: ['非常差', '差', '一般', '好', '非常好']
        };
    },

    onLoad(options) {
        this.get_v_list();
    },

    onShow() {
        var dianping = getApp().globalData.__PlugUnitScreen('6f7f2e87a00f58c6d00817b72dd332c9');
        this.open_user_dianping = dianping;
    },

    onReachBottom() {
        this.page = this.page + 1;
        this.get_v_list();
    },

    methods: {
        OkComment() {
            var that = this;
            uni.showModal({
                title: '提示',
                content: '确定要通过审核吗？',
                success(res) {
                    if (res.confirm) {
                        that.OkCommentDo();
                    }
                }
            });
        },

        OkCommentDo() {
            var info = this.CommentInfo;
            uni.showLoading({
                title: '提交中...'
            });
            var b = getApp().globalData.api_root + 'Polls/ok_comment';
            var that = this;
            var e = getApp().globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.com_id = info.id;
            http.POST(b, {
                params: params,
                success: function (res) {
                    console.log(res);
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none',
                        duration: 2000
                    });

                    if (res.data.status == 'success') {
                        that.CommentInfoMod = false;
                        that.CommentInfo = '';
                    }

                    that.list = [];
                    that.CommentInfoMod = false;
                    that.page = 1;
                    that.CommentInfo = {};
                    that.get_v_list();
                    uni.hideLoading();
                },
                fail: function () {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },

        hideModal() {
            this.CommentInfoMod = false;
        },

        hideModalRef() {
            this.refuMod = false;
        },

        refuseModal() {
            this.refuMod = true;
        },

        get_shenqing_text(d) {
            this.shenqing_text = d.detail.value;
        },

        shenqing_submit() {
            var b = getApp().globalData.api_root + 'Polls/shenqing_submit';
            var that = this;
            var e = getApp().globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.reason = this.shenqing_text;
            var info = this.CommentInfo;
            params.com_id = info.id;
            http.POST(b, {
                params: params,
                success: function (res) {
                    console.log(res);
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none',
                        duration: 2000
                    });
                    that.hideModal();
                    that.hideModalRef();
                    that.list = [];
                    that.page = 1;
                    that.CommentInfo = {};
                    that.get_v_list();
                },
                fail: function () {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },

        open_comment_info(d) {
            console.log(d);
            var index = d.currentTarget.dataset.index;
            this.CommentInfoMod = true;
            this.CommentInfo = this.list[index];
        },

        get_v_list() {
            var that = this;
            var e = getApp().globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.page = this.page;
            var b = getApp().globalData.api_root + 'Whisper/get_s_list';
            http.POST(b, {
                params: params,
                success: function (res) {
                    console.log(res);

                    if (res.data.info.length == 0 || res.data.info.length < 10) {
                        that.di_msg = true;
                    }

                    var list = that.list;
                    list.push(...res.data.info);
                    that.list = list;
                },
                fail: function () {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        }
    }
};
</script>
<style>
page {
    background-color: #ffffff;
}
.dasheds {
    border: 3rpx dashed rgba(119, 119, 119, 0.25);
    border-radius: 10rpx;
    background-color: #fff;
    margin-top: 10px;
}

.border-orange {
    border-color: #fbbd08 !important;
}

.border-blue {
    border-color: #37c0fe !important;
}
</style>

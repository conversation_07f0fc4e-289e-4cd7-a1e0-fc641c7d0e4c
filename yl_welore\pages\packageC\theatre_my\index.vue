<template>
    <view>
        <view class="page"></view>
        <cu-custom bgColor="transparent" :isSearch="false" :isBack="true">
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">我的剧集</view>
        </cu-custom>
        <view class="flex align-center justify-between" style="text-align: right; padding: 30rpx">
            <view>
                <text>共 {{ list.length }} 部</text>
                <text>{{ count }} 集</text>
            </view>
            <view>
                <button
                    v-if="config.is_allow_user_upload == 1"
                    @tap="open_url"
                    data-type="1"
                    class="cu-btn round bg-orange shadow"
                    style="font-size: 24rpx; height: 55rpx; line-height: 55rpx; padding: 0 20rpx"
                >
                    新增剧集
                </button>
            </view>
        </view>
        <view style="padding: 15rpx">
            <view class="grid col-3">
                <view @tap="open_url" data-type="2" :data-id="item.id" style="padding: 0rpx 10rpx; margin-bottom: 40rpx" v-for="(item, index) in list" :key="index">
                    <image :lazy-load="true" :src="item.poster_url" style="width: 100%; max-height: 350rpx; border-radius: 10rpx"></image>

                    <view class="text_num_1" style="margin-top: 10rpx">{{ item.title }}</view>

                    <view style="margin-top: 10rpx; font-size: 24rpx; color: #999999">全{{ item.total_episodes }}集</view>
                </view>
            </view>
        </view>
        <!-- <view style="position: fixed;bottom: 100rpx;width: 100%;text-align: center;">
    <button class="cu-btn icon shadow">
        <text class="_icon-add-round" style="font-size: 100rpx;color: orange;"></text>
  </button>
</view> -->
    </view>
</template>

<script>
var app = getApp();
import http from '../../../util/http.js';
export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            list: [],
            count: 0,
            config: {}
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.getList();
    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {},

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {},

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {},
    methods: {
        open_url(d) {
            console.log(d);
            var type = d.currentTarget.dataset.type;
            if (type == 1) {
                uni.navigateTo({
                    url: '/yl_welore/pages/packageC/theatre_add/index'
                });
            }
            if (type == 2) {
                var id = d.currentTarget.dataset.id;
                uni.navigateTo({
                    url: '/yl_welore/pages/packageC/theatre_my_series/index?id=' + id
                });
            }
        },

        getList() {
            var b = app.globalData.api_root + 'Microseries/getMyseriesList';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.openid = e.openid;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    this.list = res.data.list;
                    this.count = res.data.count;
                    this.config = res.data.config;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        }
    }
};
</script>
<style>
.page {
    background-image: linear-gradient(to bottom, #ffedc7 0%, #fffdf9 50%, #fffdf8 100%);
    height: 100vh;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: -1;
}
</style>

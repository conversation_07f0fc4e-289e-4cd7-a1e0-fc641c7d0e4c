<template>
<view>

<cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
  <view slot="backText">返回</view>
  <view slot="content" style="color: #2c2b2b;font-weight: 600; font-size: 36rpx;">零钱明细</view>
</cu-custom>
<view class="one">
  <view style="font-size:14px;margin-left:20px;padding-top:10px;float:left;">余额：</view>
  <navigator v-if="setting.open_withdrawals==1" url="/yl_welore/pages/packageC/withdrawal/index" hover-class="none">
    <view v-if="copyright.recharge_arbor==1" style="float:right;font-size:14px;margin-right:17px;margin-top:15px;">
      <text class="cicon-sponsor"></text>
      <text style="margin-left: 10rpx;">提现</text>
    </view>
  </navigator>
  <view style="clear:both;height:0"></view>
  <view style="width:100%;height:50px;margin-top:15px;">
    <view style="font-size:14px;float:left;margin-left:20px;">
      <text style="font-size:28px;">{{user_info.conch}}</text>
      <text>{{design.currency}}</text>
    </view>
    <view v-if="fraction_scale.currency_redemption_channel==1" @tap="dh_confer" style="margin-top:5px;line-height:37px;float:right;width:90px;border-radius:50px;text-align:center;color:#000000;font-size:12px;margin-right:20px;background-color:#fff;">
      兑换{{design.confer}}
    </view>
  </view>
  <view v-if="copyright.recharge_arbor==1" @tap="tab_tos" style="float:left;font-size:12px;margin-left:17px;margin-top:15px;">
    <text class="cuIcon-question"></text>请您授权手机后进行操作
  </view>
  <view v-if="copyright.recharge_arbor==0" style="float:left;font-size:12px;margin-left:17px;margin-top:15px;">
    <text class="cuIcon-question"></text>{{design.currency}}可以兑换会员和福利
  </view>
  <view v-if="copyright.recharge_arbor==1" @tap="get_pay" style="float:right;font-size:14px;margin-right:17px;margin-top:15px;">
    <text class="cicon-recharge"></text>
    <text style="margin-left: 10rpx;">获取{{design.currency}}</text>
  </view>
  <view style="clear:both;height:0"></view>
</view>
<view class="two">
  <view style="font-size:14px;margin-left:20px;padding-top:10px;">余额：</view>
  <view style="width:100%;height:50px;margin-top:15px;">
    <view style="font-size:14px;float:left;margin-left:20px;">
      <text style="font-size:28px;">{{user_info.fraction}}</text>
      <text>{{design.confer}}</text>
    </view>
    <view v-if="fraction_scale.fraction_redemption_channel==1" @tap="yes_mod_show" style="margin-top:5px;line-height:37px;float:right;width:90px;border-radius:50px;text-align:center;color:#005bea;font-size:12px;margin-left:10px;margin-right:20px;background-color:#fff;">
      兑换{{design.currency}}
    </view>
  </view>
  <view @tap="tab_tos" style="font-size:12px;margin-left:17px;margin-top:15px;">
    <text class="cuIcon-question"></text>{{design.confer}}可以兑换{{design.currency}}
  </view>
  <view style="clear:both;height:0"></view>
</view>


<scroll-view scroll-x class="bg-white nav text-center">
  <view :class="'cu-item '+(current=='tab1'?'text-blue cur':'')" @tap="handleChange" data-key="tab1" style="margin: 0 60rpx;">
    <text :class="current=='tab1'?'_this':''" style="font-weight:700;">{{design.currency}}明细</text>
  </view>
  <view :class="'cu-item '+(current=='tab2'?'text-blue cur':'')" @tap="handleChange" data-key="tab2" style="margin: 0 60rpx;">
    <text :class="current=='tab2'?'_this':''" style="font-weight:700;">{{design.confer}}明细</text>
  </view>
</scroll-view>

<view style="margin:0px 17px;width:87%;height:auto;border-radius:10px;padding:10px;font-size:14px;">
  <view style="margin:20px 0px;" v-for="(item,index) in (amount_list)" :key="index">
    <view style="float:left">
      <view class="">{{item.solution}}</view>
      <view style="color:#999999;font-size:13px;">{{item.ruins_time}}</view>
    </view>
    <view style="float:right;line-height:50px;">
      <view v-if="item.finance>0" style="font-size:18px;color:#54B835">
        +{{item.finance}}
      </view>
      <view v-if="item.finance < 0" style="font-size:18px;color:#000000">
        {{item.finance}}
      </view>
      <view v-if="item.finance==0" style="font-size:18px;color:#000000">
        -{{item.finance}}
      </view>
    </view>
    <view style="clear:both;height:0"></view>
  </view>
  <view :class="'cu-load '+(!di_msg?'loading':'over')"></view>
</view>





<view :class="'cu-modal bottom-modal '+(pay?'show':'')" @tap="no_pay">
  <view class="cu-dialog" catchtap>
    <view class="cu-bar bg-white">
      <view class="action text-blue" @tap="no_pay">取消</view>
      <view class="action text-green" @tap="pay_submit">确定</view>
    </view>
    <view class="grid col-3 padding-sm">
      <view @tap="get_pay_money" :data-index="m_index" :class="'padding '+(money_index==m_index?'select':'')" v-for="(item,m_index) in (pay_money)" :key="m_index">
        <view v-if="m_index!=0" style="font-size:14px;font-weight:600;">{{item.money}}{{design.currency}}</view>
        <view v-if="m_index==0" style="font-size:14px;font-weight:600;">自定义</view>
        <view v-if="m_index!=0" style="font-size:30px;color:#F3CB5A;">
          <text style="font-size:14px;">￥</text>
          <text>{{item.money}}</text>
        </view>
        <view v-if="m_index==0" style="font-size:30px;color:#F3CB5A;">
          <input @input="set_this_money" :value="item.money" placeholder="金额" type="digit" style="display: inline-block;width:70%;font-size:30px;color:#F3CB5A;text-align:left;"/>
        </view>
      </view>
    </view>
  </view>
</view>
<view :class="'cu-modal '+(dh_confer_t?'show':'')">
  <view class="cu-dialog">
    <view class="cu-bar bg-white justify-end">
      <view class="content">兑换确认</view>
      <view class="action" @tap="hideModal">
        <text class="cuIcon-close text-red"></text>
      </view>
    </view>
    <view class="padding-xl">
      <input v-if="dh_confer_t" @input="get_num" :value="bei_money" type="digit" style="border:1px solid var(--green);height:40px;font-size:20px;border-radius:3px;"/>
      <view style="text-align:center;font-size:14px;">
        将获得：
        <text style="font-size:30px;">{{ji_money}}</text> {{design.confer}}
      </view>
    </view>
    <view class="cu-bar bg-white justify-end">
      <view class="action">
        <button class="cu-btn line-green text-green" @tap="hideModal">取消</button>
        <button class="cu-btn bg-green margin-left" @tap="add_bei_ji">确定</button>
      </view>
    </view>
  </view>
</view>

<view :class="'cu-modal '+(dh_confer_j?'show':'')">
  <view class="cu-dialog">
    <view class="cu-bar bg-white justify-end">
      <view class="content">兑换确认</view>
      <view class="action" @tap="hideModal">
        <text class="cuIcon-close text-red"></text>
      </view>
    </view>
    <view class="padding-xl">
      <input v-if="dh_confer_j" @input="get_num_b" :value="bei_money_b" type="digit" style="border:1px solid var(--green);height:40px;font-size:20px;border-radius:3px;"/>
      <view style="text-align:center;font-size:14px;">
        将获得：
        <text style="font-size:30px;">{{ji_money_b}}</text> {{design.currency}}
      </view>
    </view>
    <view class="cu-bar bg-white justify-end">
      <view class="action">
        <button class="cu-btn line-green text-green" @tap="hideModal">取消</button>
        <button class="cu-btn bg-green margin-left" @tap="get_ji_bei">确定</button>
      </view>
    </view>
  </view>
</view>
</view>
</template>
<!-- <script module="filters" lang="wxs" src="@/yl_welore/pages/packageC/user_details/tofix.wxs"></script> -->
<script>
var app = getApp();
import http from "../../../util/http.js";
import md5 from "../../../util/md5.js";
export default {
  /**
   * 页面的初始数据
   */
  data() {
    return {
      current: 'tab1',
      fraction_scale: {},
      user_info: {},
      pay_list: [],
      pay_index: 0,
      pay_info: {},
      pay: false,
      animationPay: {},
      pay_money: [{
        money: 1
      }, {
        money: 6
      }, {
        money: 30
      }, {
        money: 68
      }, {
        money: 168
      }, {
        money: 328
      }, {
        money: 648
      }],
      money_index: 0,
      amount_list: [],
      page: 1,
      di_msg: false,
      yes_mod: false,
      withdraw: false,
      withdraw_number: '',
      dh_confer_t: false,
      dh_confer_j: false,
      ji_money: '0.00',
      bei_money: '',
      ji_money_b: '0.00',
      bei_money_b: '',
      ios: false,
      scale: 10,
      jibei_button: true,
      beiji_button: true,
      chenck_phone: false,
      copyright: {},
      design: {},
      dd_fraction: 0,
      bei_ji: 0,
      bei_ji_b: 0
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    var e = app.globalData.getCache("userinfo");
    var that = this;
    if (!e) {
      uni.login({
        success(res) {
          var params = new Object();
          params.code = res.code;
          http.POST(app.globalData.api_root + 'Login/index', {
            params: params,
            success: function (open) {
              console.log(open);
              var data = new Object();
              data.openid = open.data.info.openid;
              data.session_key = open.data.info.session_key;
              http.POST(app.globalData.api_root + 'Login/add_tourist', {
                params: data,
                success: function (d) {
                  app.globalData.setCache("userinfo", d.data.info);
                  that.get_user_info();
                  that.get_user_amount();
                }
              });
            }
          });
        }
      });
    } else {
      that.get_user_info();
      that.get_user_amount();
    }
    this.page = 1;
    this.copyright = getApp().globalData.store.getState().copyright;
    var design = uni.getStorageSync('is_diy');
    if (design) {
      this.design = design;
    } else {
      this.get_diy();
    }
    if (e.user_phone) {
      this.chenck_phone = true;
    }
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    var that = this;
    var e = app.globalData.getCache("userinfo");
    //获取系统信息
    uni.getSystemInfo({
      success(res) {
        var copyright = getApp().globalData.store.getState().copyright;
        console.log(res.platform);
        if (res.platform == "ios" && copyright.ios_pay_arbor == 0) {
          that.ios = false;
        }
        if (res.platform == "ios" && copyright.ios_pay_arbor == 1) {
          that.ios = true;
        }
        if (res.platform != "ios") {
          that.ios = true;
        }
      }
    });
    if (e.user_phone) {
      this.chenck_phone = true;
    }
  },
  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    //wx.showNavigationBarLoading() //在标题栏中显示加载
    //模拟加载
    setTimeout(function () {
      uni.hideNavigationBarLoading(); //完成停止加载
      uni.stopPullDownRefresh(); //停止下拉刷新
    }, 1500);
    this.amount_list = [];
    this.page = 1;
    this.get_user_info();
    this.get_user_amount();
  },
  /**
   * 加载下一页
   */
  onReachBottom() {
    this.page = this.page + 1;
    this.get_user_amount();
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    var forward = app.globalData.forward;
    console.log(forward);
    if (forward) {
      return {
        title: forward.title,
        path: '/yl_welore/pages/index/index',
        imageUrl: forward.reis_img
      };
    } else {
      return {
        title: '您的好友给您发了一条信息',
        path: '/yl_welore/pages/index/index'
      };
    }
  },
  methods: {
    /**
    * 获取手机号
    */
    getPhoneNumber(c) {
      console.log(c);
      if (c.detail.errMsg == 'getPhoneNumber:ok') {
        var b = app.globalData.api_root + 'User/get_user_phone';
        var that = this;
        var e = app.globalData.getCache("userinfo");
        console.log(e);
        var params = new Object();
        params.token = e.token;
        params.openid = e.openid;
        params.uid = e.uid;
        params.encryptedData = c.detail.encryptedData;
        params.iv = c.detail.iv;
        params.sessionKey = e.session_key;
        http.POST(b, {
          params: params,
          success: function (res) {
            console.log(res);
            if (res.data.status == 'success') {
              var e = app.globalData.getCache("userinfo");
              e.user_phone = res.data.phone;
              console.log(e);
              app.globalData.setCache("userinfo", e);
            }
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
            that.chenck_phone = true;
          },
          fail: function () {
            uni.showModal({
              title: '提示',
              content: '网络繁忙，请稍候重试！',
              showCancel: false,
              success: function (res) {}
            });
          }
        });
      } else {
        uni.showModal({
          title: '提示',
          content: c.detail.errMsg,
          showCancel: false,
          success: function (res) {}
        });
      }
    },
    /**
     * 当前兑换比例
     */
    tab_tos() {
      uni.navigateTo({
        url: '/yl_welore/pages/packageC/service_centre/index'
      });
      // wx.showToast({
      //   title: '当前兑换比例 1：' + this.data.scale,
      //   icon: 'none',
      //   duration: 2000
      // })
    },
    /**
     * 兑换积分
     */
    dh_confer() {
      this.dh_confer_t = true;
    },
    /**
     * 获取输入框
     */
    get_num(d) {
      var money = d.detail.value;
      if (money == '') {
        this.ji_money = '0.00';
        this.bei_money = '';
        return;
      }
      var reg = /^(\.*)(\d+)(\.?)(\d{0,2}).*$/g;
      if (reg.test(money)) {
        //正则匹配通过，提取有效文本
        money = money.replace(reg, '$2$3$4');
      } else {
        //正则匹配不通过，直接清空
        money = '';
      }
      console.log(this.user_info['conch']);
      console.log(money);
      if (parseFloat(money) > parseFloat(this.user_info['conch'])) {
        money = this.user_info['conch'];
      }
      this.ji_money = (money * this.scale).toFixed(2);
      this.bei_money = money;
    },
    /**
     * 获取积分兑换贝壳输入框
     */
    get_num_b(d) {
      var money = d.detail.value;
      console.log(money);
      if (money == '') {
        this.ji_money_b = '0.00';
        this.bei_money_b = '';
        return;
      }
      var reg = /^(\.*)(\d+)(\.?)(\d{0,2}).*$/g;
      if (reg.test(money)) {
        //正则匹配通过，提取有效文本
        money = money.replace(reg, '$2$3$4');
      } else {
        //正则匹配不通过，直接清空
        money = '';
      }
      console.log(this.user_info['fraction']);
      console.log(money);
      if (parseFloat(money) > parseFloat(this.user_info['fraction'])) {
        money = this.user_info['fraction'];
      }
      var b_money = (money / this.scale).toFixed(3);
      var basic = b_money.substr(0, b_money.length - 1);
      this.ji_money_b = basic;
      this.bei_money_b = money;
    },
    /**
     * 贝壳兑换积分
     */
    add_bei_ji() {
      var that = this;
      if (this.beiji_button == false) {
        return;
      }
      if (that.bei_money == '' || that.bei_money <= 0) {
        uni.showToast({
          title: '请填写正确兑换的数量',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      this.beiji_button = false;
      var b = app.globalData.api_root + 'User/add_bei_ji';
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.uid = e.uid;
      params.bei_money = that.bei_money;
      http.POST(b, {
        params: params,
        success: function (res) {
          console.log(res);
          if (res.data.status == 'success') {
            that.page = 1;
            that.beiji_button = true;
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
            that.hideModal();
            that.get_user_info();
            that.get_user_amount();
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
            that.beiji_button = true;
          }
        },
        fail: function () {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) {}
          });
        }
      });
    },
    /**
     * 自定义金额
     */
    set_this_money(e) {
      var money = e.detail.value;
      var list = this.pay_money;
      list[0]['money'] = money;
      this.pay_money = list;
    },
    hideModal() {
      this.dh_confer_j = false;
      this.withdraw = false;
      this.withdraw_card = false;
      this.dh_confer_t = false;
      this.ji_money = '0.00';
      this.bei_money = '';
      this.ji_money_b = '0.00';
      this.bei_money_b = '';
    },
    yes_mod_show() {
      this.dh_confer_j = true;
    },
    /**
     * 积分兑换贝壳
     */
    get_ji_bei() {
      if (this.jibei_button == false) {
        return;
      }
      var that = this;
      if (that.bei_money_b == '' || that.bei_money_b <= 0) {
        uni.showToast({
          title: '请填写正确兑换的数量',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      this.jibei_button = false;
      var b = app.globalData.api_root + 'User/get_ji_bei';
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.uid = e.uid;
      params.bei_money_b = that.ji_money_b;
      http.POST(b, {
        params: params,
        success: function (res) {
          console.log(res);
          if (res.data.status == 'success') {
            that.hideModal();
            that.page = 1;
            that.jibei_button = true;
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
            that.get_user_info();
            that.get_user_amount();
          } else {
            that.yes_mod = false;
            that.jibei_button = true;
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: function () {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) {}
          });
        }
      });
    },
    handleChange(detail) {
      this.amount_list = [];
      this.page = 1;
      this.current = detail.currentTarget.dataset.key;
      this.get_user_amount();
    },
    /**
     * 充值贝壳
     */
    get_pay() {
      var that = this;
      // 创建一个动画实例
      var animation = uni.createAnimation({
        // 动画持续时间
        duration: 150,
        // 定义动画效果，当前是匀速
        timingFunction: 'linear'
      });
      // 将该变量赋值给当前动画
      that.animation = animation;
      // 先在y轴偏移，然后用step()完成一个动画
      animation.translateY(230).step();
      // 用setData改变当前动画
      that.animationPay = animation.export();
      that.pay = true;
      // 设置setTimeout来改变y轴偏移量，实现有感觉的滑动
      setTimeout(function () {
        animation.translateY(0).step();
        that.animationPay = animation.export();
      }, 100);
    },
    /**
     * 关闭
     */
    no_pay() {
      this.pay = false;
    },
    /**
     * 获取零钱明细
     */
    get_user_amount() {
      var b = app.globalData.api_root + 'User/get_user_amount';
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.uid = e.uid;
      params.page = this.page;
      params.evaluate = this.current;
      var allMsg = that.amount_list;
      http.POST(b, {
        params: params,
        success: function (res) {
          console.log(res);
          if (res.data.status == 'success') {
            if (res.data.info.length == 0) {
              that.di_msg = true;
            }
            for (var i = 0; i < res.data.info.length; i++) {
              allMsg.push(res.data.info[i]);
            }
            that.amount_list = allMsg;
            that.setting = res.data.setting;
            that.scale = res.data.scale;
            that.fraction_scale = res.data.fraction_scale;
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: function () {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) {}
          });
        }
      });
    },
    /**
     * 获取会员信息
     */
    get_user_info() {
      var b = app.globalData.api_root + 'User/get_user_info';
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      http.POST(b, {
        params: params,
        success: function (res) {
          if (res.data.status == 'success') {
            var fraction = (res.data.info['fraction'] / 10).toFixed(3);
            that.user_info = res.data.info;
            that.dd_fraction = fraction.substring(0, fraction.length - 1);
            that.bei_ji = that.copyright['conch_convert'] - res.data.info['bei_ji'] < 0 ? 0 : that.copyright['conch_convert'] - res.data.info['bei_ji'];
            that.bei_ji_b = that.copyright['fraction_convert'] - res.data.info['ji_bei'] < 0 ? 0 : that.copyright['fraction_convert'] - res.data.info['ji_bei'];
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: function () {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) {}
          });
        }
      });
    },
    /**
     * 充值金额
     */
    get_pay_money(op) {
      var index = op.currentTarget.dataset.index;
      this.money_index = index;
    },
    /**
     * 充值
     */
    pay_submit() {
      var money = this.pay_money[this.money_index]['money'];
      console.log(money);
      if (!money) {
        uni.showToast({
          title: '充值金额错误！',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.uid = e.uid;
      params.money = money;
      var b = app.globalData.api_root + 'Pay/do_pay';
      http.POST(b, {
        params: params,
        success: function (res) {
          console.log(res);
          if (res.data.return_msg == "OK") {
            var timeStamp = (Date.parse(new Date()) / 1000).toString();
            var pkg = 'prepay_id=' + res.data.prepay_id;
            var nonceStr = res.data.nonce_str;
            var paySign = md5.hexMD5('appId=' + res.data.appid + '&nonceStr=' + nonceStr + '&package=' + pkg + '&signType=MD5&timeStamp=' + timeStamp + "&key=" + res.data.app_info['app_key']).toUpperCase(); //此处用到hexMD5插件
            //发起支付
            uni.requestPayment({
              'timeStamp': timeStamp,
              'nonceStr': nonceStr,
              'package': pkg,
              'signType': 'MD5',
              'paySign': paySign,
              success: function (res) {
                uni.showToast({
                  title: '充值成功！',
                  icon: 'none',
                  duration: 2000
                });
                that.page = 1;
                that.amount_list = [];
                //支付成功之后的操作
                that.get_user_info();
                that.no_pay();
                that.get_user_amount();
              },
              complete: function () {
                that.page = 1;
                that.amount_list = [];
                //支付成功之后的操作
                that.get_user_info();
                that.no_pay();
                that.get_user_amount();
              }
            });
          } else {
            uni.showModal({
              title: '提示',
              content: res.data.return_msg,
              showCancel: false,
              success: function (res) {}
            });
            that.get_pay();
          }
        },
        fail: function () {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) {}
          });
        }
      });
    },
    get_diy() {
      var b = app.globalData.api_root + 'User/get_diy';
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      // params.uid = e.uid;
      params.token = e.token;
      params.openid = e.openid;
      http.POST(b, {
        params: params,
        success: function (res) {
          if (res.data.status) {
            return;
          } else {
            that.design = res.data;
            uni.setStorageSync("is_diy", res.data);
          }
        },
        fail: function () {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) {}
          });
        }
      });
    }
  }
};
</script>
<style >
 page {
  background-color: #fff;
}

.one {
  width: 90%;
  min-height: 140px;
  border-radius: 8px;
  margin: 10px auto;
  color: #ffffff;
  background-image: linear-gradient(to right, #434343 0%, black 100%);

}

.two {
  width: 90%;
  height: 140px;
  border-radius: 8px;
  margin: 10px auto;
  color: #ffffff;
  background-image: linear-gradient(to top, #00c6fb 0%, #005bea 100%);
  
}

.i-modal-actions {
  height: 10px !important;
}

.zan_style_test {
  float: right;
  margin-top: 26rpx;
  margin-right: 20rpx;
  font-size: 12px;
}

.yes_pos {
  position: relative;
}

.zan_style {
  float: right;
  margin-top: 17rpx;
  margin-right: 4rpx;
}

.weui-tabbar {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  position: fixed;
  z-index: 500;
  bottom: 0;
  width: 100%;
  background-color: #f7f7fa;
}

.liwu_col {
  box-sizing: border-box;
  text-align: center;
  margin-top: 30rpx;
  display: inline-block;
  width: 25%;
  height: 7.5em;
  border-radius: 10px;
  border: 1px solid #f1f1f1;
  margin-right: 10px;
  margin-left: 10px;
  position: relative;
}

.select {
  border: 1px solid #f3cb5a;
  background-color: #fdf3da;
}

.user_col {
  box-sizing: border-box;
  text-align: center;
  display: inline-block;
  width: 33%;
  height: 7.5em;
  border-radius: 10px;
}

/**
     * 弹窗
     */

.show-btn {
  margin-top: 100rpx;
  color: #2c2;
}

.modal-mask {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: #000;
  opacity: 0.3;
  overflow: hidden;
  z-index: 999;
  color: #fff;
}

.modal-dialog {
  width: 540rpx;
  overflow: hidden;
  position: fixed;
  top: 45%;
  left: 0;
  z-index: 1000;
  background: #f9f9f9;
  margin: -180rpx 105rpx;
  border-radius: 5px;
}

.modal-title {
  padding-top: 50rpx;
  font-size: 14px;
  color: #030303;
  text-align: center;
}

.modal-content {
  padding: 20rpx 32rpx;
}

.modal-input {
  display: flex;
  background: #fff;
  border: 2rpx solid #ddd;
  border-radius: 4rpx;
  font-size: 28rpx;
}

.input {
  width: 100%;
  height: 82rpx;
  font-size: 28rpx;
  line-height: 28rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  color: #333;
}

input-holder {
  color: #666;
  font-size: 28rpx;
}

.modal-footer {
  display: flex;
  flex-direction: row;
  height: 86rpx;
  border-top: 1px solid #dedede;
  font-size: 14px;
  line-height: 86rpx;
}

.btn-cancel {
  width: 50%;
  color: #666;
  text-align: center;
  border-right: 1px solid #dedede;
}

.btn-confirm {
  width: 50%;
  color: #c33;
  text-align: center;
}  
</style>
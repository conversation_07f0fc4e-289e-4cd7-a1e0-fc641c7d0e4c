<template>
    <!-- component/privacy/privacy.wxml -->
    <view class="privacy" v-if="showPrivacy" @touchmove.stop.prevent="handleCatchtouchMove">
        <view class="content">
            <view class="title">隐私保护指引</view>
            <view class="des">
                在使用当前小程序服务之前，请仔细阅读
                <text class="link" @tap="openPrivacyContract">{{ privacyContractName }}</text>
                。如你同意{{ privacyContractName }}，请点击“同意”开始使用。
            </view>
            <view class="btns">
                <button class="item reject" @tap="exitMiniProgram">拒绝</button>
                <button id="agree-btn" class="item agree" open-type="agreePrivacyAuthorization" @agreeprivacyauthorization="handleAgreePrivacyAuthorization">同意</button>
            </view>
        </view>
    </view>
</template>

<script>
// component/privacy/privacy.js
export default {
    data() {
        return {
            privacyContractName: '',
            showPrivacy: false
        };
    },
    /**
     * 组件的方法列表
     */
    methods: {
        handlePageShow() {
            const that = this;
            const version = uni.getAppBaseInfo().SDKVersion;
            if (that.compareVersion(version, '2.32.3') >= 0) {
                uni.getPrivacySetting({
                    success(res) {
                        console.log(res);
                        if (res.errMsg == 'getPrivacySetting:ok') {
                            that.setData({
                                privacyContractName: res.privacyContractName,
                                showPrivacy: res.needAuthorization
                            });
                        }
                    }
                });
            }
        },

        // 打开隐私协议页面
        openPrivacyContract() {
            const that = this;
            uni.openPrivacyContract({
                fail: () => {
                    uni.showToast({
                        title: '遇到错误',
                        icon: 'error'
                    });
                }
            });
        },

        // 拒绝隐私协议
        exitMiniProgram() {
            uni.showToast({
                title: '必须同意后才可以继续使用当前小程序',
                icon: 'none'
            });
        },

        // 同意隐私协议
        handleAgreePrivacyAuthorization() {
            const that = this;
            that.setData({
                showPrivacy: false
            });
        },

        // 比较版本号
        compareVersion(v1, v2) {
            v1 = v1.split('.');
            v2 = v2.split('.');
            const len = Math.max(v1.length, v2.length);
            while (v1.length < len) {
                v1.push('0');
            }
            while (v2.length < len) {
                v2.push('0');
            }
            for (let i = 0; i < len; i++) {
                const num1 = parseInt(v1[i]);
                const num2 = parseInt(v2[i]);
                if (num1 > num2) {
                    return 1;
                } else if (num1 < num2) {
                    return -1;
                }
            }
            return 0;
        },

        // 通过绑定空事件禁止滑动事件的穿透
        handleCatchtouchMove() {
            return;
        }
    },
    created: function () {}
};
</script>
<style>
/* component/privacy/privacy.wxss */
.privacy {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9999999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.content {
    width: 632rpx;
    padding: 48rpx;
    box-sizing: border-box;
    background: #fff;
    border-radius: 16rpx;
}

.content .title {
    text-align: center;
    color: #333;
    font-weight: bold;
    font-size: 32rpx;
}

.content .des {
    font-size: 26rpx;
    color: #666;
    margin-top: 40rpx;
    text-align: justify;
    line-height: 1.6;
}

.content .des .link {
    color: #07c160;
    text-decoration: underline;
}

.btns {
    margin-top: 48rpx;
    display: flex;
}

.btns .item {
    justify-content: space-between;
    width: 244rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 16rpx;
    box-sizing: border-box;
    border: none;
}

.btns .reject {
    background: #f4f4f5;
    color: #909399;
}

.btns .agree {
    background: #07c160;
    color: #fff;
}
</style>

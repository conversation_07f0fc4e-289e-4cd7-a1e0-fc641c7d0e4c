<template>
    <view>
        <view style="width: 100%; margin: 0px auto">
            <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true" style="color: #fff">
                <view slot="content" style="color: #000000; font-weight: 600; font-size: 36rpx">审核</view>
                <view slot="backText">返回</view>
            </cu-custom>
        </view>
        <scroll-view scroll-x class="bg-white nav text-center" style="margin-bottom: 20px">
            <view :class="'cu-item ' + (0 == TabCur ? 'text-blue cur' : '')" @tap="tabSelect" data-id="0">内容</view>
            <view :class="'cu-item ' + (1 == TabCur ? 'text-blue cur' : '')" @tap="tabSelect" data-id="1">回复</view>
        </scroll-view>
        <view v-if="TabCur == 0" style="padding: 20rpx 40rpx 40rpx 40rpx" v-for="(item, index) in list" :key="index">
            <view class="solid-bottom" @tap="open_info" :data-id="item.id">
                <view class="flex justify-start align-start">
                    <view class="">
                        <image style="width: 40px; height: 40px; border-radius: 50%" :src="item.reply_head"></image>
                    </view>
                    <view style="margin-left: 10px">
                        <view style="font-size: 14px; letter-spacing: 1px">{{ item.temp_name }}</view>
                        <view style="margin-top: 5px; letter-spacing: 1px; color: #acadb4">
                            <text style="font-size: 12px">{{ item.send_time }}</text>
                        </view>
                    </view>
                </view>
                <view style="margin-top: 15px; color: #333333; letter-spacing: 1px">
                    <view class="text_num_3">
                        <view v-if="item.user_id != 0" @tap.stop.prevent="open_user" :data-id="item.user_id" class="text-blue" style="margin-bottom: 20rpx">
                            <text class="cicon-at-line"></text>
                            <text>{{ item.user_name }}</text>
                        </view>
                        <rich-text :nodes="item.content"></rich-text>
                    </view>
                    <view style="margin-top: 20rpx">
                        <!-- 1 -->
                        <view style="width: 30%" v-if="item.image_part.length == 1 && img != ''" v-for="(img, img_index) in item.image_part" :key="img_index">
                            <image
                                class="now_level"
                                :lazy-load="true"
                                :src="img"
                                @tap.stop.prevent="Preview"
                                :data-src="img"
                                :data-index="index"
                                style="border-radius: 3px; width: 100%"
                                mode="widthFix"
                            ></image>
                        </view>
                        <!-- 1 -->
                        <!-- 2 -->
                        <view style="width: 30%; float: left; text-align: center" v-if="item.image_part.length == 2" v-for="(img, img_index) in item.image_part" :key="img_index">
                            <image
                                class="now_level"
                                :lazy-load="true"
                                v-if="img_index == 0"
                                @tap.stop.prevent="Preview"
                                :data-src="img"
                                :data-index="index"
                                :src="img"
                                style="width: 100%; border-radius: 5px"
                                mode="widthFix"
                            ></image>

                            <image
                                class="now_level"
                                :lazy-load="true"
                                v-if="img_index == 1"
                                @tap.stop.prevent="Preview"
                                :data-src="img"
                                :data-index="index"
                                :src="img"
                                style="width: 100%; border-radius: 5px; padding-left: 5px"
                                mode="widthFix"
                            ></image>
                        </view>
                        <!-- 2 -->
                        <!-- 3 -->
                        <view class="grid col-3 text-center">
                            <block v-if="item.image_part.length > 2" v-for="(img, img_index) in item.image_part" :key="img_index">
                                <view style="text-align: center" v-if="img_index == 0">
                                    <image
                                        class="now_level"
                                        :lazy-load="true"
                                        :src="img"
                                        @tap.stop.prevent="Preview"
                                        :data-src="img"
                                        :data-index="index"
                                        style="border-radius: 5px; width: 100%"
                                        mode="widthFix"
                                    ></image>
                                </view>

                                <view style="text-align: center; padding: 0px 5px" v-if="img_index == 1">
                                    <image
                                        class="now_level"
                                        :lazy-load="true"
                                        :src="img"
                                        @tap.stop.prevent="Preview"
                                        :data-src="img"
                                        :data-index="index"
                                        style="border-radius: 5px; width: 100%"
                                        mode="widthFix"
                                    ></image>
                                </view>

                                <view style="text-align: center" v-if="img_index == 2">
                                    <image
                                        class="now_level"
                                        :lazy-load="true"
                                        :src="img"
                                        @tap.stop.prevent="Preview"
                                        :data-src="img"
                                        :data-index="index"
                                        style="border-radius: 5px; width: 100%"
                                        mode="widthFix"
                                    ></image>
                                </view>
                            </block>
                        </view>
                    </view>
                </view>
                <view style="clear: both; height: 0"></view>
                <view class="flex justify-between align-center" style="padding: 20px 0px">
                    <view style="font-weight: 400; color: #acadb4">
                        <text>{{ item.reply_content }} 评论</text>
                        <text style="margin-left: 10px">{{ item.praise_number }} 赞</text>
                    </view>
                    <view>
                        <view style="font-size: 16px; background-color: #f0f0f0; line-height: 2em; border-radius: 25px; text-align: center; padding: 0px 10px">
                            <view @tap.stop.prevent="shenhe" data-key="2" :data-id="item.id" class="cicon-close-round text-red" style="margin: 0px 5px"></view>
                            <view class="_icon-move" style="margin: 0px 5px; transform: rotate(90deg)"></view>
                            <view @tap.stop.prevent="shenhe" data-key="1" :data-id="item.id" class="cicon-check-round text-green" style="margin: 0px 5px"></view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view v-if="TabCur == 1" class="cu-list menu-avatar comment">
            <view class="cu-item" v-for="(item, index) in list" :key="index">
                <view class="cu-avatar round index5" :style="'background-image:url(' + item.reply_head + ');background-color: #FFFFFF;'"></view>

                <view class="content bg-gray" style="padding: 10px; border-radius: 5px">
                    <view class="text-orange" style="font-weight: 700">{{ item.temp_name }}</view>
                    <view class="text-block text-content text-df margin-top-sm" style="letter-spacing: 1px; white-space: normal; word-break: break-all">
                        <rich-text :nodes="item.re_content"></rich-text>
                    </view>
                    <view class="margin-top-sm flex justify-between align-center">
                        <view class="text-gray text-df">{{ item.reply_time }}</view>
                        <view>
                            <view style="font-size: 16px; background-color: #ffffff; line-height: 2em; border-radius: 25px; text-align: center; padding: 0px 10px">
                                <view @tap.stop.prevent="shenhe" data-key="2" :data-id="item.id" class="cicon-close-round text-red" style="margin: 0px 5px"></view>
                                <view class="_icon-move" style="margin: 0px 5px; transform: rotate(90deg)"></view>
                                <view @tap.stop.prevent="shenhe" data-key="1" :data-id="item.id" class="cicon-check-round text-green" style="margin: 0px 5px"></view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view :class="'cu-load ' + (!di_msg ? 'loading' : 'over')" style="padding: 0px 0px 80px 0px"></view>

        <view :class="'cu-modal ' + (check_admin_mod == true ? 'show' : '')" @touchmove.stop.prevent="preventTouchMove">
            <view class="cu-dialog">
                <view class="cu-bar bg-white justify-end">
                    <view class="content">提示</view>
                    <view class="action" @tap="hideModal">
                        <text class="cuIcon-close text-red"></text>
                    </view>
                </view>
                <view class="padding-xl">
                    <view>{{ this_key == 1 ? '要通过审核吗？' : '' }}</view>
                    <textarea v-if="this_key == 2" maxlength="50" @input="textareaAInput" placeholder="填写拒绝理由" style="padding: 10px; width: 100%; height: 100px"></textarea>
                </view>
                <view class="cu-bar bg-white justify-end">
                    <view class="action">
                        <button class="cu-btn line-green text-green" @tap="hideModal">取消</button>
                        <button class="cu-btn bg-green margin-left" @tap="status_ok">确定</button>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp();
const http = require('../../../util/http.js');

export default {
    data() {
        return {
            TabCur: 0,
            list: [],
            page: 1,
            check_admin_mod: false,
            id: 0,
            text: '',
            di_msg: false,
            this_key: 0
        }
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.page = 1;
        this.list = [];
        this.get_secret();
    },

    /**
     * 加载下一页
     */
    onReachBottom() {
        this.page = this.page + 1;
        if (this.TabCur == 0) {
            this.get_secret();
        } else {
            this.get_reply();
        }
    },

    methods: {
        tabSelect(d) {
            const id = d.currentTarget.dataset.id;
            this.TabCur = id;
            this.page = 1;
            this.list = [];
            this.di_msg = false;

            if (id == 0) {
                this.get_secret();
            } else {
                this.get_reply();
            }
        },

        get_reply() {
            const that = this;
            const b = app.globalData.api_root + 'Whisper/get_reply_all';
            const e = app.globalData.getCache('userinfo');
            const params = {};
            params.token = e.token;
            params.openid = e.openid;
            params.page = this.page;

            http.POST(b, {
                params: params,
                success: (res) => {
                    if (res.data.length == 0 || res.data.length < 4) {
                        that.di_msg = true;
                    }
                    const list = that.list;
                    list.push(...res.data);
                    that.list = list;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },

        textareaAInput(e) {
            this.text = e.detail.value;
        },

        status_ok() {
            const b = app.globalData.api_root + 'Whisper/status_ok';
            const that = this;
            const e = app.globalData.getCache('userinfo');
            const params = {};
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.id;
            params.key = this.this_key;
            params.reject_reason = this.text;
            params.tab_cur_tow = this.TabCur;

            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res.data);
                    if (res.data.code == 0) {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                        that.page = 1;
                        that.check_admin_mod = false;
                        that.list = [];

                        if (that.TabCur == 0) {
                            that.get_secret();
                        } else {
                            that.get_reply();
                        }
                    } else {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: (res) => {}
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },

        hideModal() {
            this.check_admin_mod = false;
        },

        shenhe(d) {
            this.id = d.currentTarget.dataset.id;
            this.check_admin_mod = true;
            this.this_key = d.currentTarget.dataset.key;
        },

        open_info(item) {
            const id = item.currentTarget.dataset.id;
            uni.navigateTo({
                url: '/yl_welore/pages/packageD/whisper_info/index?id=' + id
            });
        },

        open_user(e) {
            const id = e.currentTarget.dataset.id;
            // 这里可以添加打开用户信息的逻辑
        },

        /**
         * 获取全部内容
         */
        get_secret() {
            const b = app.globalData.api_root + 'Whisper/get_secret_admin';
            const that = this;
            const e = app.globalData.getCache('userinfo');
            const params = {};
            params.token = e.token;
            params.openid = e.openid;
            params.page = this.page;
            const list = that.list;

            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res.data);
                    if (res.data.list.length == 0 || res.data.list.length < 4) {
                        that.di_msg = true;
                    }
                    list.push(...res.data.list);
                    that.list = list;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },

        // 预览图片
        Preview(e) {
            const src = e.currentTarget.dataset.src;
            uni.previewImage({
                current: src,
                //当前预览的图片
                urls: [src] //所有要预览的图片数组
            });
        },

        // 阻止触摸移动
        preventTouchMove() {
            return false;
        }
    }
}
</script>
<style>
page {
    background-color: #ffffff;
}
</style>

<template>
    <view class="dial-container">
        <!-- 电话按钮模式 -->
        <view v-if="Type == 1" class="phone-button" @tap="get_bh">
            <view class="phone-icon-wrapper">
                <text class="phone-icon">☎️</text>
            </view>
            <view class="phone-label">电话</view>
        </view>

        <!-- 输入框模式 -->
        <view v-if="Type == 2" class="input-container">
            <view class="form-group">
                <view class="form-title">
                    <text class="title-icon">📞</text>
                    <text class="title-text">联系方式</text>
                </view>
                <view class="input-wrapper">
                    <input
                        class="phone-input"
                        @input="get_phone"
                        :value="phone"
                        type="number"
                        maxlength="20"
                        placeholder="请输入电话号码"
                    />
                </view>
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp();
export default {
    /**
     * 组件的对外属性
     */
    props: {
        Type: {
            type: String,
            default: ''
        }
    },
    /**
     * 组件的初始数据
     */
    data() {
        return {
            http_root: app.globalData.http_root,
            bh: false,
            phone: ''
        }
    },
    /**
     * 生命周期函数
     */
    mounted() {
        const that = this;
    },
    /**
     * 组件的方法列表
     */
    methods: {
        get_bh() {
            this.bh = !this.bh;
            this.$emit('SetPhone', {
                detail: {
                    phone: '',
                    bh: this.bh
                }
            });
        },
        get_phone(d) {
            const v = d.detail.value;
            this.$emit('Phone', {
                detail: {
                    phone: v,
                    bh: this.bh
                }
            });
        }
    }
}
</script>
<style scoped>
.dial-container {
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 电话按钮样式 */
.phone-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 20rpx;
    border-radius: 20rpx;
}

.phone-button:hover {
    transform: translateY(-2rpx);
}

.phone-button:active {
    transform: translateY(0);
    opacity: 0.8;
}

.phone-button:hover .phone-icon-wrapper {
    box-shadow: 0 12rpx 30rpx rgba(76, 175, 80, 0.4);
    transform: scale(1.05);
}

.phone-icon {
    font-size: 40rpx;
    line-height: 1;
}

.phone-label {
    font-size: 28rpx;
    color: #666;
    font-weight: 500;
    margin-top: 8rpx;
}

/* 输入框容器样式 */
.input-container {
    width: 100%;
    background: #fff;
    border-radius: 20rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
    padding: 40rpx 30rpx;
}

.form-group {
    width: 100%;
}

.form-title {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;
    padding-bottom: 16rpx;
    border-bottom: 2rpx solid #f0f0f0;
}

.title-icon {
    font-size: 32rpx;
    margin-right: 12rpx;
}

.title-text {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
}

.input-wrapper {
    position: relative;
}

.phone-input {
    width: 100%;
    height: 88rpx;
    padding: 0 24rpx;
    font-size: 30rpx;
    color: #333;
    background: #f8f9fa;
    border: 2rpx solid #e9ecef;
    border-radius: 12rpx;
    box-sizing: border-box;
    transition: all 0.3s ease;
}

.phone-input:focus {
    background: #fff;
    border-color: #4CAF50;
    box-shadow: 0 0 0 6rpx rgba(76, 175, 80, 0.1);
    outline: none;
}

.phone-input::placeholder {
    color: #999;
    font-size: 28rpx;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
    .input-container {
        margin: 0 20rpx;
        padding: 30rpx 20rpx;
    }

    .phone-icon-wrapper {
        width: 100rpx;
        height: 100rpx;
    }

    .phone-icon {
        font-size: 40rpx;
    }
}
</style>

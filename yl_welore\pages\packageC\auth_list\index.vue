<template>
    <view>
        <cu-custom :isCustom="true" bgColor="bg-white" :isSearch="false">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">认证审核</view>
        </cu-custom>
        <view class="cu-card case" style="padding-bottom: 30rpx">
            <view class="cu-item shadow" v-for="(item, l_index) in list" :key="l_index">
                <view class="image" style="text-align: center">
                    <image class="now_level" :src="item.attest.at_icon" style="width: 150rpx" mode="widthFix"></image>
                </view>

                <view style="text-align: center; margin-top: 10px">
                    <image :src="item.user_head_sculpture" style="width: 60rpx; height: 60rpx; border-radius: 50%; vertical-align: middle"></image>
                    <text style="margin-left: 10px">{{ item.user_nick_name }}</text>
                </view>

                <view class="cu-list menu-avatar">
                    <view class="cu-item" style="justify-content: center">
                        <view class="flex justify-between align-center" style="width: 75%">
                            <view style="font-weight: 15px; font-weight: 700">{{ item.attest.at_name }}</view>
                            <view @tap="open_info" :data-id="l_index" class="text-gray" style="font-weight: 500">查看用户认证详情</view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view :class="'cu-modal ' + (mode ? 'show' : '')">
            <view class="cu-dialog">
                <view class="cu-bar bg-white justify-end">
                    <view class="content">认证详情</view>
                    <view class="action" @tap="hideModal">
                        <text class="cuIcon-close text-red"></text>
                    </view>
                </view>
                <view class="padding">
                    <scroll-view :scroll-y="true" class="padding-sm" style="height: 1000rpx; letter-spacing: 0.5px; line-height: 25px">
                        <block v-for="(item, index) in info.postback_data" :key="index">
                            <block v-if="item.dataType == 'text'">
                                <view class="flex solid-bottom padding align-center">
                                    <view>{{ item.text }}：</view>
                                    <view style="margin-left: 20rpx">{{ item.value }}</view>
                                </view>
                            </block>

                            <block v-if="item.dataType == 'textarea'">
                                <view class="flex solid-bottom padding align-center">
                                    <view>{{ item.text }}：</view>
                                    <view style="margin-left: 20rpx">{{ item.value }}</view>
                                </view>
                            </block>

                            <block v-if="item.dataType == 'checkbox'">
                                <view class="flex solid-bottom padding align-center">
                                    <view>{{ item.text }}：</view>
                                    <view style="margin-left: 20rpx" v-for="(d, index1) in item.value" :key="index1">{{ d }}</view>
                                </view>
                            </block>

                            <block v-if="item.dataType == 'select'">
                                <view class="flex solid-bottom padding align-center">
                                    <view>{{ item.text }}：</view>
                                    <view style="margin-left: 20rpx">{{ item.value }}</view>
                                </view>
                            </block>

                            <block v-if="item.dataType == 'radio'">
                                <view class="flex solid-bottom padding align-center">
                                    <view>{{ item.text }}：</view>
                                    <view style="margin-left: 20rpx">{{ item.value }}</view>
                                </view>
                            </block>

                            <block v-if="item.dataType == 'image'">
                                <view class="flex solid-bottom padding align-center">
                                    <view>{{ item.text }}：</view>
                                    <image
                                        @tap="open_img"
                                        :data-src="d"
                                        class="now_level"
                                        mode="widthFix"
                                        :src="d"
                                        style="width: 100px; margin-left: 20rpx"
                                        v-for="(d, index1) in item.value"
                                        :key="index1"
                                    ></image>
                                </view>
                            </block>
                        </block>
                    </scroll-view>
                    <view class="flex p-xs margin-bottom-sm mb-sm">
                        <view class="flex-sub">
                            <view @tap="ok_demo" class="bg-green padding-sm margin-xs radius text-center shadow-blur">
                                <view class="text-lg text-white">通过</view>
                            </view>
                        </view>
                        <view class="flex-sub">
                            <view @tap="no_demo" class="bg-yellow padding-sm margin-xs radius text-center shadow-blur">
                                <view class="text-lg text-white">拒绝</view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view :class="'cu-modal ' + (ok_demo_show ? 'show' : '')">
            <view class="cu-dialog">
                <view class="cu-bar bg-white justify-end">
                    <view class="content">用户认证成功后的备注</view>
                    <view class="action" @tap="hideModalRef">
                        <text class="cuIcon-close text-red"></text>
                    </view>
                </view>
                <view v-if="ok_demo_show" class="padding-xl">
                    <input class="shadow radius bg-white" @input="get_input" style="height: 50px; padding: 0px 10px" placeholder="填写备注" />
                </view>
                <view class="cu-bar bg-white justify-end">
                    <view class="action">
                        <button class="cu-btn line-green text-green" @tap="ok_demo">取消</button>
                        <button class="cu-btn bg-green margin-left" @tap="ok_submit_do" data-key="1">确定</button>
                    </view>
                </view>
            </view>
        </view>
        <view :class="'cu-modal ' + (no_demo_show ? 'show' : '')">
            <view class="cu-dialog">
                <view class="cu-bar bg-white justify-end">
                    <view class="content">审核未通过原因</view>
                    <view class="action" @tap="hideModalRef">
                        <text class="cuIcon-close text-red"></text>
                    </view>
                </view>
                <view v-if="no_demo_show" class="padding-xl">
                    <input class="shadow radius bg-white" @input="get_input" style="height: 50px; padding: 0px 10px" placeholder="未通过原因" />
                </view>
                <view class="cu-bar bg-white justify-end">
                    <view class="action">
                        <button class="cu-btn line-green text-green" @tap="no_demo">取消</button>
                        <button class="cu-btn bg-green margin-left" @tap="ok_submit_do" data-key="2">确定</button>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import http from '../../../util/http.js';
export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            list: [],
            page: 1,
            mode: false,
            ok_demo_show: false,
            no_demo_show: false,
            ut_inject: '',
            info: null,
            di_msg: false
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.get_list();
    },

    /**
     * 加载下一页
     */
    onReachBottom() {
        this.page = this.page + 1;
        this.get_list();
    },

    methods: {
        open_img(d) {
            uni.previewImage({
                current: d.currentTarget.dataset.src,
                // 当前显示图片
                urls: [d.currentTarget.dataset.src] // 需要预览的图片http链接列表
            });
        },

        hideModal() {
            this.mode = false;
        },

        get_list() {
            var b = getApp().globalData.api_root + 'Whisper/acquire';
            var e = getApp().globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.page = this.page;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.length == 0 || res.data.length < 10) {
                        this.di_msg = true;
                    }
                    this.list.push(...res.data);
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },

        open_info(d) {
            console.log(d);
            var index = d.currentTarget.dataset.id;
            this.info = this.list[index];
            this.mode = true;
        },

        ok_submit_do(d) {
            var key = d.currentTarget.dataset.key;
            var b = getApp().globalData.api_root + 'Whisper/ok_submit_do';
            var e = getApp().globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.process = key;
            params.acid = this.info.id;
            params.ut_inject = this.ut_inject;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.code == 1) {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                        this.ut_inject = '';
                        this.ok_demo_show = false;
                        this.no_demo_show = false;
                        this.list = [];
                        this.page = 1;
                        this.mode = false;
                        this.get_list();
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },

        get_input(d) {
            this.ut_inject = d.detail.value;
        },

        no_demo() {
            this.ut_inject = '';
            this.no_demo_show = !this.no_demo_show;
        },

        ok_demo() {
            this.ut_inject = '';
            this.ok_demo_show = !this.ok_demo_show;
        }
    }
};
</script>
<style>
page {
    background-color: #f1f1f1;
}
</style>

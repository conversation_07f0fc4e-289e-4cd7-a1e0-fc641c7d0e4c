<template>
    <view>
        <cu-custom bgColor="bg-white" :isBack="true" :isSearch="false">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">麦卡营地</view>
        </cu-custom>
        <map
            id="myMap"
            style="width: 100vw; height: 100vh"
            :enable-satellite="satellite"
            :latitude="latitude"
            :longitude="longitude"
            @markertap="markertap"
            @callouttap="callouttap"
            @labeltap="labeltap"
            :markers="markers"
            scale="13"
            show-location
        >
            <cover-view @tap="set_sate" style="position: fixed; right: 10px; top: 40%; background-color: #fff; padding: 5rpx; border-radius: 50%">
                <cover-image class="right-icon" src="/static/yl_welore/style/icon/ceng.png"></cover-image>
            </cover-view>
            <cover-view @tap="set_loc" style="position: fixed; right: 10px; top: 46%; background-color: #fff; padding: 5rpx; border-radius: 50%">
                <cover-image class="right-icon" src="/static/yl_welore/style/icon/pos.png"></cover-image>
            </cover-view>
        </map>
    </view>
</template>

<script>
var app = getApp();
var http = require('../../util/http.js');
export default {
    data() {
        return {
            latitude: 0,
            longitude: 0,
            markers: [],
            satellite: false
        };
    },
    onShow() {
        if (this.latitude == 0 && this.longitude == 0) {
            var that = this;
            uni.getLocation({
                type: 'wgs84',
                success(res) {
                    console.log(res);
                    that.setData({
                        latitude: res.latitude,
                        longitude: res.longitude
                    });
                },
                fail(d) {
                    uni.showModal({
                        title: '提示',
                        content: '请点击右上角三个点-设置-允许使用位置',
                        success(res) {}
                    });
                }
            });
        }
    },
    onLoad() {
        this.get_mak_list();
    },
    onReady (e) {
        this.mapCtx = uni.createMapContext('myMap');
    },
    methods: {
        set_sate() {
            this.setData({
                satellite: !this.satellite
            });
        },

        set_loc() {
            var mapCtx = uni.createMapContext('myMap');
            mapCtx.moveToLocation();
        },

        get_mak_list() {
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            var b = app.globalData.api_root + 'Scanning/index';
            http.POST(b, {
                params: params,
                success: (res)=> {
                    console.log(res);
                    that.setData({
                        markers: res.data
                    });
                },
                fail: ()=> {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res)=> {}
                    });
                }
            });
        },

        markertap(e) {
            console.log('@@@ markertap', e);
            var index = e.detail.markerId;
            var info = this.markers[index];
            uni.navigateTo({
                url: '/yl_welore/pages/packageA/article/index?id=' + info.ids + '&type=' + info.study_type
            });
        },

        callouttap(e) {
            console.log('@@@ callouttap', e);
        },

        labeltap(e) {
            console.log('@@@ labeltap', e);
        }
    }
};
</script>
<style>
page {
    background-color: #ffffff;
}
.right-icon {
    width: 50rpx;
    height: 50rpx;
}
</style>

<template>
    <view class="page-container">
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
            <view slot="backText">返回</view>
            <view slot="content" class="header-title">我的{{ design.landgrave }}</view>
        </cu-custom>

        <!-- 优化后的导航栏 -->
        <view class="nav-container">
            <view class="nav-tabs">
                <view
                    :class="'nav-tab ' + (current == 'tab1' ? 'active' : '')"
                    @tap="handleChange"
                    data-key="tab1"
                >
                    <text class="nav-text">我加入的</text>
                </view>
                <view
                    :class="'nav-tab ' + (current == 'tab2' ? 'active' : '')"
                    @tap="handleChange"
                    data-key="tab2"
                >
                    <text class="nav-text">我管理的</text>
                </view>
            </view>
            <view class="nav-indicator" :class="current == 'tab2' ? 'right' : 'left'"></view>
        </view>

        <!-- 优化后的圈子列表 - 我加入的 -->
        <view class="circle-grid" v-if="current == 'tab1'">
            <view
                class="circle-card"
                @tap="open_url"
                :data-id="item.id"
                v-for="(item, index) in info"
                :key="index"
            >
                <view class="card-content">
                    <view class="image-container">
                        <image
                            class="circle-image"
                            :src="item.realm_icon"
                            mode="aspectFill"
                        ></image>
                    </view>
                    <view class="circle-info">
                        <view class="follow-count">{{ item.concern }}关注</view>
                        <view class="circle-name">{{ item.realm_name }}</view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 优化后的圈子列表 - 我管理的 -->
        <view class="circle-grid" v-if="current == 'tab2'">
            <view
                class="circle-card"
                @tap="open_url"
                :data-id="item.id"
                v-for="(item, index) in user_list"
                :key="index"
            >
                <view class="card-content">
                    <view class="image-container">
                        <image
                            class="circle-image"
                            :src="item.realm_icon"
                            mode="aspectFill"
                        ></image>
                        <!-- 管理标签 -->
                        <view class="role-badge" v-if="item.is_type">
                            <view v-if="item.is_type == 'xiao'" class="badge admin-badge">管理员</view>
                            <view v-if="item.is_type == 'da'" class="badge owner-badge">圈主</view>
                        </view>
                    </view>
                    <view class="circle-info">
                        <view class="circle-name">{{ item.realm_name }}</view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
var app = getApp();
import http from '../../../util/http.js';
export default {
    data() {
        return {
            user_list: [],
            info: [],
            current: 'tab1',
            page: 1,
            info_dsg: false,
            uid: '',

            design: {
                landgrave: ''
            },

            user_info: '',
            da_qq: '',
            xiao_qq: ''
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        var e = app.globalData.getCache('userinfo');
        var design = uni.getStorageSync('is_diy');
        this.uid = e.uid;
        this.design = design;
        this.user_mastert();
        this.get_my_trailing();
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {
        //this.get_qq_info();
    },
    onReachBottom() {
        this.page = this.page + 1;
        this.get_my_trailing();
    },
    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {
        var forward = app.globalData.forward;
        console.log(forward);
        if (forward) {
            return {
                title: forward.title,
                path: '/yl_welore/pages/index/index',
                imageUrl: forward.reis_img
            };
        } else {
            return {
                title: '您的好友给您发了一条信息',
                path: '/yl_welore/pages/index/index'
            };
        }
    },
    methods: {
        open_url(d) {
            var id = d.currentTarget.dataset.id;
            uni.navigateTo({
                url: '/yl_welore/pages/packageA/circle_info/index?id=' + id
            });
        },

        handleChange(detail) {
            this.current = detail.currentTarget.dataset.key;
            this.page = 1;
            this.info_dsg = false;
        },

        /**
         * 我加入的圈子
         */
        get_my_trailing() {
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            params.get_id = -1;
            params.page = this.page;
            var b = app.globalData.api_root + 'User/get_right_needle';
            var allMsg = this.info;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.info.length == 0) {
                        this.info_dsg = true;
                    }
                    if (res.data.status == 'success') {
                        for (var i = 0; i < res.data.info.length; i++) {
                            allMsg.push(res.data.info[i]);
                        }
                        this.info = allMsg;
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },

        /**
         * 我管理的
         */
        user_mastert() {
            var b = app.globalData.api_root + 'User/user_mastert';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        this.user_list = res.data.info;
                        this.user_info = res.data.user_info;
                        if (res.data.info.length == 0) {
                            this.info_dsg = true;
                        }
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },

        /**
         * 获取会员信息
         */
        get_qq_info() {
            var b = app.globalData.api_root + 'User/get_qq_info';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.id;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        this.info = res.data.info;
                        this.da_qq = res.data.info.da_qq;
                        this.xiao_qq = res.data.info.xiao_qq;
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        }
    }
};
</script>
<style>
page {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

.page-container {
    background: transparent;
}

/* 头部标题样式 */
.header-title {
    color: #2c2b2b;
    font-weight: 600;
    font-size: 36rpx;
}

/* 导航栏容器 */
.nav-container {
    background: #ffffff;
    margin: 20rpx;
    border-radius: 50rpx;
    box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
}

.nav-tabs {
    display: flex;
    position: relative;
    z-index: 2;
}

.nav-tab {
    flex: 1;
    padding: 30rpx 0;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
}

.nav-tab.active .nav-text {
    color: #ffffff;
    font-weight: 700;
}

.nav-text {
    font-size: 28rpx;
    font-weight: 600;
    color: #666666;
    transition: all 0.3s ease;
}

/* 导航指示器 */
.nav-indicator {
    position: absolute;
    top: 0;
    left: 0;
    width: 50%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50rpx;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1;
}

.nav-indicator.right {
    transform: translateX(100%);
}

/* 圈子网格布局 */
.circle-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20rpx;
    padding: 30rpx 20rpx;
}

/* 圈子卡片样式 */
.circle-card {
    background: #ffffff;
    border-radius: 24rpx;
    overflow: hidden;
    box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    transform: translateY(0);
}

.circle-card:active {
    transform: translateY(4rpx) scale(0.98);
    box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.12);
}

.card-content {
    padding: 20rpx;
}

/* 图片容器 */
.image-container {
    position: relative;
    margin-bottom: 20rpx;
}

.circle-image {
    width: 100%;
    height: 160rpx;
    border-radius: 16rpx;
    background: linear-gradient(45deg, #f0f2f5, #e1e5e9);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 角色标签 */
.role-badge {
    position: absolute;
    top: 8rpx;
    right: 8rpx;
}

.badge {
    padding: 8rpx 16rpx;
    border-radius: 20rpx;
    font-size: 20rpx;
    font-weight: 600;
    color: #ffffff;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.admin-badge {
    background: linear-gradient(135deg, #36d1dc 0%, #5b86e5 100%);
}

.owner-badge {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

/* 圈子信息 */
.circle-info {
    text-align: center;
}

.follow-count {
    font-size: 22rpx;
    color: #999999;
    margin-bottom: 8rpx;
    font-weight: 500;
}

.circle-name {
    font-size: 26rpx;
    color: #333333;
    font-weight: 600;
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 响应式优化 */
@media (max-width: 750rpx) {
    .circle-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 24rpx;
        padding: 30rpx 24rpx;
    }

    .circle-image {
        height: 180rpx;
    }
}

/* 占位符样式保留 */
.placeholder {
    margin: 5px;
    text-align: center;
    box-shadow: 0px 0px 5px 1px #cfcfcf;
    border-radius: 3px;
}
</style>

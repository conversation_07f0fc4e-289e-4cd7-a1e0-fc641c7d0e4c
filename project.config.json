{"appid": "wx5751e2981a30c314", "projectname": "yl_welore", "compileType": "miniprogram", "libVersion": "3.4.7", "packOptions": {"ignore": [], "include": []}, "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "uglifyFileName": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "ignoreUploadUnusedFiles": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "condition": true}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 4}, "projectArchitecture": "multiPlatform", "simulatorPluginLibVersion": {"wxext14566970e7e9f62": "3.0.2-8"}}
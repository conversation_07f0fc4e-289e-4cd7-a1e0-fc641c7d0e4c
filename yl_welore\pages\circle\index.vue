<template>
  <view>
    <cu-custom v-if="mod.plaza != 'e5bee474-578a-e4f2-b633-d38011b878f8'" bgColor="none" :isSearch="true" id="custom">
      <view slot="content" style="color: #2c2b2b;font-weight: 600; font-size: 36rpx;">
        {{ design.pattern_data.plaza.title }}</view>
    </cu-custom>
    <Index1 v-if="mod.plaza == 0" :parentData="currentInstance" @open_ph="open_ph" @nex_my_qq="nex_my_qq"
      @this_url="this_url" @quan_url="quan_url"></Index1>
    <Index2 v-if="mod.plaza == 'b9b7e9a9-7b02-c6fc-568d-a300e6c036bd'" :parentData="currentInstance" @open_ph="open_ph"
      @nex_my_qq="nex_my_qq" @this_url="this_url" @quan_url="quan_url"></Index2>
    <Index3 v-if="mod.plaza == '8d4dc0cd-b5d1-5b78-8d21-ee5d5a1dd210'" :data="currentInstance" @open_ph="open_ph"
      @mod_tab_change="mod_tab_change" @cardSwiper="cardSwiper" @open_gambit="open_gambit" @home_url="home_url"
      @prev_img="prev_img" @dian_option="dian_option" @vote_do="vote_do" @play="play" @stop="stop"
      @sliderChange="sliderChange" @home_pl="home_pl"></Index3>
    <Index4 v-if="mod.plaza == 'e5bee474-578a-e4f2-b633-d38011b878f8'" :parentData="currentInstance" @open_ph="open_ph"
      @nex_my_qq="nex_my_qq" @this_url="this_url" @quan_url="quan_url"></Index4>
    <tabbar id="tabbar" :tabbar="tabbar"></tabbar>
  </view>
</template>

<script>
import tabbar from "@/yl_welore/util/tabbarComponent/tabbar";
import uiTab from "@/yl_welore/colorui/ui-tab/ui-tab";
import Index1 from "@/yl_welore/pages/circle/index1.vue";
import Index2 from "@/yl_welore/pages/circle/index2.vue";
import Index3 from "@/yl_welore/pages/circle/index3.vue";
import Index4 from "@/yl_welore/pages/circle/index4.vue";
var app = getApp();
var http = require("../../util/http.js");
const innerAudioContext = uni.getBackgroundAudioManager();
export default {
  components: {
    tabbar,
    uiTab,
    Index1,
    Index2,
    Index3,
    Index4
  },
  /**
   * 页面的初始数据
   */
  data() {
    return {
      currentInstance: this, // 通过data属性中转 
      http_root: app.globalData.http_root,
      tabbar: {},
      user_info: {},
      plaza_current: 'plaza',
      diy: '',
      needle: [],
      info: [],
      page: 1,
      tj_page: 1,
      mod_page: 1,
      mod_list_page: 1,
      gc_page: 1,
      tj_list: [],
      mod_list: [],
      mod_my_list: [],
      is_show: true,
      not_jia: false,
      di_msg: false,
      isPopping: false,
      //是否已经弹出
      animPlus: {},
      //旋转动画
      animCollect1: {},
      animCollect: {},
      //item位移,透明度
      animTranspond: {},
      //item位移,透明度
      animInput: {},
      //item位移,透明
      animBack: {},
      version: 0,
      mod: '',
      gc_msg: true,
      rank: [],
      mod_list_id: 0,
      gambit_list: [],
      cardCur: 0,
      tab_show: true,
      design: {}
    };
  },
  onPageScroll(e) {
    let scrollTop = e.scrollTop;
    if (scrollTop >= 180) {
      this.tab_show = false;
    }
    if (scrollTop < 130) {
      this.tab_show = true;
    }
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.info = [];
    this.tj_list = [];
    this.page = 1;
    this.tj_page = 1;
    this.gc_page = 1;
    this.not_jia = false;
    this.needle = [];
    var dd = uni.getStorageSync('is_diy');
    console.log(dd);
    this.get_guanchang();
    this.get_my_trailing();
    this.get_ph_list();
    this.mod_list = [];
    this.mod_list_page = 1;
    this.mod_my_list = [];
    this.mod_page = 1;
    this.get_tj_list();
    this.get_mod_list();
    if (dd.mod.plaza == '8d4dc0cd-b5d1-5b78-8d21-ee5d5a1dd210') {
      this.get_my_attr_list();
      this.get_gambit();
    }
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    uni.hideTabBar();
    app.globalData.editTabbar();
    //this.selectComponent('#tabbar').get_user();
    if (this.is_show == false) {
      return;
    }
    var copyright = getApp().globalData.store.getState().copyright;
    console.log(getApp().globalData.store.getState().copyright);
    if (Object.keys(copyright).length === 0) {
      this.authority();
    } else {
      this.copyright = copyright;
    }
    var dd = uni.getStorageSync('is_diy');

    if (Object.keys(dd).length === 0) {
      this.get_diy();
    } else {
      this.mod = dd.mod;
      this.design = dd;
    }
  },
  /**
   * 加载下一页
   */
  onReachBottom() {
    this.tj_page = this.tj_page + 1;
    this.mod_list_page = this.mod_list_page + 1;
    this.get_tj_list();
    this.get_my_attr_list();
  },
  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    //模拟加载
    setTimeout(() => {
      uni.hideNavigationBarLoading(); //完成停止加载
      uni.stopPullDownRefresh(); //停止下拉刷新
    }, 1500);
    this.onLoad();
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage(d) {
    console.log(d);
    var key = d.target.dataset.key;
    var info = this.mod_my_list[key];
    console.log(info);
    let img;
    if (info.image_part) {
      if (info.image_part.length > 0) {
        img = info.image_part[0];
      }
    }
    return {
      title: info.study_title == '' ? info.study_content : info.study_title,
      path: '/yl_welore/pages/packageA/article/index?id=' + info.id + '&type=' + info.study_type,
      imageUrl: img
    };
  },
  methods: {
    dian_option() {
      console.log('dian_option');
    },
    vote_do() {
      console.log('vote_do');
    },
    home_pl() {
      console.log('home_pl');
    },
    prev_img(d) {
      console.log(d);
      var img = d.currentTarget.dataset.src;
      var index = d.currentTarget.dataset.key;
      var info = this.mod_my_list[index];
      uni.previewImage({
        current: img,
        // 当前显示图片
        urls: info['image_part'] // 需要预览的图片http链接列表  
      });
    },
    /**
     * 点赞
     */
    add_zan(data) {
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var id = data.currentTarget.dataset.id;
      var key = data.currentTarget.dataset.key;
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.id = id;
      params.uid = e.uid;
      params.applaud_type = 0;
      params.zan_type = this.mod_my_list[key]['is_info_zan'] == true ? 1 : 0;
      var list = that.mod_my_list;
      uni.vibrateShort();
      var list_a = 'mod_my_list[' + key + '].is_info_zan';
      var list_c = 'mod_my_list[' + key + '].info_zan_count_this';
      if (list[key]['is_info_zan'] == false) {
        this.$set(this.mod_my_list[key], 'is_info_zan', true);
        this.$set(this.mod_my_list[key], 'info_zan_count_this', list[key]['info_zan_count_this'] + 1);
      } else {
        this.$set(this.mod_my_list[key], 'is_info_zan', false);
        this.$set(this.mod_my_list[key], 'info_zan_count_this', list[key]['info_zan_count_this'] - 1 < 0 ? 0 : list[key]['info_zan_count_this'] - 1);
      }
      var b = app.globalData.api_root + 'User/add_user_zan';
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          if (res.data.status == "success") {
            //wx.vibrateShort();
            // list[key]['is_info_zan'] = res.data.info_zan;
            //that.rotate3d(key);
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => { }
          });
        }
      });
    },
    /**
     * 首页跳转链接
     */
    home_url(dd) {
      var warrant_arbor = getApp().globalData.store.getState().copyright['warrant_arbor'];
      var that = this;
      var key = dd.currentTarget.dataset.k; //跳转类型
      var uid = dd.currentTarget.dataset.user_id;
      if (key == 1) {
        //头像跳转
        if (uid == 0) {
          uni.showToast({
            title: '身份已隐藏',
            icon: 'none',
            duration: 2000
          });
          return;
        }
        uni.navigateTo({
          url: '/yl_welore/pages/packageB/my_home/index?id=' + uid
        });
        return;
      }
      if (key == 2) {
        //圈子跳转
        uni.navigateTo({
          url: '/yl_welore/pages/packageA/circle_info/index?id=' + dd.currentTarget.dataset.id
        });
        return;
      }
      var e = app.globalData.getCache("userinfo");
      if (e.tourist == 1 && warrant_arbor == 1) {
        this.check_user_login = true;
        return;
      }
      if (key == 3) {
        //内容跳转
        var douyin = app.globalData.__PlugUnitScreen('5fb4baf1f25fe251685b526dc8c30b8f');
        var info = this.mod_my_list[dd.currentTarget.dataset.index];
        if (dd.currentTarget.dataset.type == 2 && info.is_buy == 0 && e.user_phone && douyin) {
          uni.navigateTo({
            url: '/yl_welore/pages/packageF/full_video/index?id=' + dd.currentTarget.dataset.id
          });
          return;
        }
        uni.navigateTo({
          url: '/yl_welore/pages/packageA/article/index?id=' + dd.currentTarget.dataset.id + '&type=' + dd.currentTarget.dataset.type
        });
        return;
      }
    },
    //播放声音
    play(e) {
      var that = this;
      var warrant_arbor = getApp().globalData.store.getState().copyright['warrant_arbor'];
      var user = app.globalData.getCache("userinfo");
      if (user.tourist == 1 && warrant_arbor == 1) {
        this.check_user_login = true;
        return;
      }
      var index = e.currentTarget.dataset.key;
      var nuw = this.mod_my_list;
      var key = 1;
      var info = this.mod_my_list[index];
      if (info['check_look'] == 0 && info['is_buy'] == 1) {
        innerAudioContext.stop();
        uni.navigateTo({
          url: '/yl_welore/pages/packageA/article/index?id=' + info['id'] + '&type=' + info['study_type']
        });
        return;
      }
      uni.getBackgroundAudioPlayerState({
        success(res) {
          console.log(res);
          const status = res.status;
          key = res.status;
        }
      });
      for (var i = 0; i < nuw.length; i++) {
        nuw[i]['is_voice'] = false;
      }
      this.mod_my_list = nuw;
      console.log('播放');
      innerAudioContext.src = e.currentTarget.dataset.vo;
      innerAudioContext.title = nuw[index]['study_title'] ? nuw[index]['study_title'] : '暂无标题';
      innerAudioContext.onTimeUpdate(() => {
        //console.log(innerAudioContext.currentTime)
        var duration = innerAudioContext.duration;
        var offset = innerAudioContext.currentTime;
        var currentTime = parseInt(innerAudioContext.currentTime);
        var min = "0" + parseInt(currentTime / 60);
        var sec = currentTime % 60;
        if (sec < 10) {
          sec = "0" + sec;
        }
        ;
        var starttime = min + ':' + sec; /*  00:00  */

        nuw[index]['starttime'] = starttime;
        nuw[index]['offset'] = offset;
        that.mod_my_list = nuw;
      });
      // innerAudioContext.play();

      nuw[index]['is_voice'] = true;
      this.mod_my_list = nuw;
      this.new_list_index = index;
      //播放结束
      innerAudioContext.onEnded(() => {
        var nuw = this.mod_my_list;
        nuw[index]['is_voice'] = false;
        that.starttime = '00:00';
        that.offset = 0;
        that.mod_my_list = nuw;
        console.log("音乐播放结束");
      });
      innerAudioContext.play();
    },
    /**
     * 停止
     */
    stop(e) {
      innerAudioContext.pause();
      console.log('暂停');
      var index = e.currentTarget.dataset.key;
      var nuw = this.mod_my_list;
      nuw[index]['is_voice'] = false;
      this.mod_my_list = nuw;
    },
    // 进度条拖拽
    sliderChange(e) {
      var that = this;
      var index = e.currentTarget.dataset.key;
      var nuw = this.mod_my_list;
      var info = this.mod_my_list[index];
      if (info['check_look'] == 0 && info['is_buy'] == 1) {
        innerAudioContext.stop();
        uni.navigateTo({
          url: '/yl_welore/pages/packageA/article/index?id=' + info['id'] + '&type=' + info['study_type']
        });
        return;
      }
      var offset = parseInt(e.detail.value);
      innerAudioContext.play();
      innerAudioContext.seek(offset);
      nuw[index]['is_voice'] = true;
      this.mod_my_list = nuw;
    },
    cardSwiper(e) {
      this.cardCur = e.detail.current;
    },
    open_gambit(item) {
      var id = item.currentTarget.dataset.id;
      uni.navigateTo({
        url: '/yl_welore/pages/gambit/index?id=' + id
      });
    },
    mod_tab_change(item) {
      var id = item.detail.data.id;
      this.mod_list_id = id;
      this.mod_list_page = 1;
      this.mod_my_list = [];
      this.get_my_attr_list();
    },
    open_ph(item) {
      console.log(item);
      var id = item.currentTarget.dataset.id;
      uni.navigateTo({
        url: '/yl_welore/pages/packageD/leaderboard/index?id=' + id
      });
    },
    page_next() {
      this.gc_page = this.gc_page + 1;
      this.get_guanchang();
    },
    this_url(d) {
      var id = d.currentTarget.dataset.id;
      uni.navigateTo({
        url: '/yl_welore/pages/packageA/circle_info/index?id=' + id
      });
    },
    //获取用户信息
    get_user_info() {
      var b = app.globalData.api_root + 'User/get_user_info';
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.uid = e.uid;
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          if (res.data.status == 'success') {
            that.user_info = res.data.info;
            if (res.data.info.status == 0) {
              uni.navigateTo({
                url: '/yl_welore/pages/black_house/index'
              });
              return;
            }
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => { }
          });
        }
      });
    },
    get_gambit() {
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      var b = app.globalData.api_root + 'Home/get_gambit';
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          that.gambit_list = res.data;
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => { }
          });
        }
      });
    },
    get_my_attr_list() {
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.page = that.mod_list_page;
      params.mod_list_id = this.mod_list_id;
      var b = app.globalData.api_root + 'Home/get_my_attr_list';
      var allMsg = that.mod_my_list;
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          if (res.data.info.length == 0 || res.data.info.length < 7) {
            that.di_msg = true;
          }
          for (var i = 0; i < res.data.info.length; i++) {
            allMsg.push(res.data.info[i]);
          }
          that.mod_my_list = allMsg;
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => { }
          });
        }
      });
    },
    /**
     * 获取推荐圈子
     */
    get_mod_list() {
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.uid = e.uid;
      params.page = that.mod_page;
      var b = app.globalData.api_root + 'Home/get_mod_list';
      var allMsg = that.mod_list;
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          if (res.data.status == "success") {
            if (res.data.info.length == 0) {
              that.di_msg = true;
            }
            for (var i = 0; i < res.data.info.length; i++) {
              allMsg.push(res.data.info[i]);
            }
            that.mod_list = allMsg;
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => { }
          });
        }
      });
    },
    get_ph_list() {
      var b = app.globalData.api_root + 'Ranking/get_ph_list';
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.openid = e.openid;
      http.POST(b, {
        params: params,
        success: (res) => {
          that.rank = res.data.list;
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => { }
          });
        }
      });
    },
    quan_url(data) {
      var id = data.currentTarget.dataset.id;
      uni.navigateTo({
        url: '/yl_welore/pages/square/index?curNav=' + id
      });
    },
    get_diy() {
      var b = app.globalData.api_root + 'User/get_diy';
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.uid = e.uid;
      http.POST(b, {
        params: params,
        success: (res) => {
          that.mod = res.data.mod;
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => { }
          });
        }
      });
    },
    /**
     * 信息站点
     */
    authority() {
      var b = app.globalData.api_root + 'User/get_authority';
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      http.POST(b, {
        params: params,
        success: (res) => {
          that.copyright = res.data;
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => { }
          });
        }
      });
    },
    /**
     * 获取推荐圈子
     */
    get_tj_list() {
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.uid = e.uid;
      params.page = that.tj_page;
      var b = app.globalData.api_root + 'User/get_tj_list';
      var allMsg = that.tj_list;
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          if (res.data.status == "success") {
            if (res.data.info.length == 0) {
              that.di_msg = true;
            }
            for (var i = 0; i < res.data.info.length; i++) {
              allMsg.push(res.data.info[i]);
            }
            that.tj_list = allMsg;
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => { }
          });
        }
      });
    },
    /**
     * 我加入的圈子
     */
    get_my_trailing() {
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.uid = e.uid;
      params.get_id = -1;
      params.page = that.page;
      var b = app.globalData.api_root + 'User/get_right_needle';
      var allMsg = that.info;
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          if (res.data.status == "success") {
            if (res.data.info.length == 0) {
              that.not_jia = true;
            }
            for (var i = 0; i < res.data.info.length; i++) {
              allMsg.push(res.data.info[i]);
            }
            that.info = allMsg;
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => { }
          });
        }
      });
    },
    /**
     * 圈子下一页
     */
    nex_my_qq() {
      this.page = this.page + 1;
      this.get_my_trailing();
    },
    /**
     * 圈子广场
     */
    get_guanchang() {
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.page = that.gc_page;
      var b = app.globalData.api_root + 'User/get_all_needle';
      var allMsg = that.needle;
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          if (res.data.status == "success") {
            if (res.data.info.length == 0 || res.data.info.length < 12) {
              that.gc_msg = false;
            }
            for (var i = 0; i < res.data.info.length; i++) {
              allMsg.push(res.data.info[i]);
            }
            that.needle = allMsg;
            // that.setData({
            //   needle: res.data.info
            // })
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => { }
          });
        }
      });
    },
    /**
     * 图片预览
     */
    previewImage(e) {
      this.is_show = false;
      var current = e.target.dataset.src;
      var id = e.target.dataset.id;
      var key = e.target.dataset.key;
      uni.previewImage({
        current: current,
        // 当前显示图片的http链接  
        urls: this.tj_list[key]['img'] // 需要预览的图片http链接列表  
      });
    }
  }
};
</script>
<style>
.ph_txt {
  position: absolute;
  top: 35%;
  margin: 0 auto;
  text-align: center;
  left: 0;
  right: 0;
  color: #ffffff;
  font-size: 15px;
  font-weight: 300;
  font-family: PingFangSC-Semibold, sans-serif;
  z-index: 100;
}

.ph_img {
  display: inline-block;
  height: 110rpx;
  border-radius: 5px;
}

button::after {
  line-height: normal;
  font-size: 30rpx;
  width: 0;
  height: 0;
  top: 0;
  left: 0;
}

button {
  line-height: normal;
  display: block;
  padding-left: 0px;
  padding-right: 0px;
  background-color: rgba(255, 255, 255, 0);
  font-size: 30rpx;
}

page {
  background: #fff;
}

.tag_tag {
  vertical-align: text-top;
  margin-left: 15rpx;
}

.classify_list {
  display: flex;
  font-size: 15px;
  width: 100%;
  white-space: nowrap;
  background-color: #fff;
  overflow-x: scroll;
}

.classify {
  height: 100%;
  margin: 25rpx;
  text-align: center;
  background-color: #fff;
  border-radius: 25rpx;
  box-sizing: border-box;
  display: inline-block;
  position: relative;
}

.classify_nex {
  margin: 25rpx;
  text-align: center;
  background-color: #fff;
  display: inline-block;
}

.class_img {
  width: 130rpx;
  height: 130rpx;
  border-radius: 100%;
}

.chang_img {
  min-width: 288rpx;
  height: 150rpx;
  border-radius: 10rpx;
  border-radius: 10rpx;
}

.class_border {
  width: 130rpx;
  height: 130rpx;
  border-radius: 100%;
  border: 2rpx #dbdbdb solid;
  padding: 5rpx;
  margin: 0 auto;
}

.bod_qu {
  height: 46rpx;
  border-radius: 34rpx;
  background-color: #f8f8f8;
  color: #2e77ed;
  font-size: 24rpx;
  font-weight: 600;
  line-height: 47rpx;
  width: 60px;
  margin: 0 auto;
  margin-top: 5px;
}

.demo-col {
  border: solid;
  line-height: 90rpx;
}

.placeholder {
  margin: 5px;
}

.img-style {
  height: 0rpx;
  width: 0rpx;
  position: absolute;
  right: 50%;
  opacity: 0;
  bottom: 0px;
}

.weui-tabbar_boo {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  position: fixed;
  bottom: 0;
  width: 100%;
  border-top-left-radius: 30rpx;
  border-top-right-radius: 30rpx;
}

.weui-tabbar_boo_no {
  /* display: -webkit-box;
  display: -webkit-flex; */
  position: fixed;
  bottom: 23%;
  right: 0;
  /* width: 100%; */
}

/*Tab*/
.bind_check {
  font-size: 18px;
  color: #000000;
  font-weight: 700;
}

.container {
  overflow-y: scroll;
  background-color: rgba(226, 226, 226, 0.452);
}

.list {
  column-count: 2;
  margin: 10rpx;
  column-gap: 10rpx;
}

.list-item {
  break-inside: avoid;
  border-radius: 5px;
  overflow: hidden;
}

.item-img {
  width: 100%;
  height: 140rpx;
}

.content {
  width: 94%;
  margin-left: 3%;
  font-size: 26rpx;
  word-break: break-all;
}

.gamb_dian {
  color: #E0E1E5;
  font-size: 13px;
  margin-left: -2px;
}

.gamb_dian_dian {
  color: #7C7E86;
  font-size: 13px;
  margin-left: -2px;
}

/**index.wxss**/
.audiosBox {
  width: 92%;
  margin: auto;
  height: 130rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f6f7f7;
  border-radius: 10rpx;
}

/*按钮大小  */
.audioOpen {
  width: 70rpx;
  height: 70rpx;
  border: 2px solid #4c9dee;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
}

.image2 {
  margin-left: 10%;
}

/*进度条长度  */
.slid {
  flex: 1;
  position: relative;
}

.slid view {
  display: flex;
  justify-content: space-between;
}

.slid view>text:nth-child(1) {
  color: #4c9dee;
  margin-left: 6rpx;
}

.slid view>text:nth-child(2) {
  margin-right: 6rpx;
}

slider {
  width: 520rpx;
  margin: 0;
  margin-left: 35rpx;
}

.sticky {
  position: fixed !important;
  top: 9%;
}
</style>
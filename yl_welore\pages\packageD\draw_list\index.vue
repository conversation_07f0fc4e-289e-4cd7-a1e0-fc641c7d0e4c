<template>
    <view class="page-container">
        <cu-custom bgColor="none" :isSearch="false" :isBack="true" class="custom-header" >
            <view slot="backText">返回</view>
            <view slot="content" class="header-title">{{ info.custom_title ? info.custom_title : '幸运抽奖' }}</view>
        </cu-custom>
        <view class="tab-container">
            <view class="modern-nav">
                <view :class="'nav-item ' + (TabCur == 0 ? 'active' : '')" @tap="tabSelect" data-id="0">
                    <text class="nav-text">可参与</text>
                </view>
                <view :class="'nav-item ' + (TabCur == 1 ? 'active' : '')" @tap="tabSelect" data-id="1">
                    <text class="nav-text">已开奖</text>
                </view>
                <view :class="'nav-item ' + (TabCur == 2 ? 'active' : '')" @tap="tabSelect" data-id="2">
                    <text class="nav-text">我参与的</text>
                </view>
            </view>
        </view>
        <view class="lottery-card" @tap="open_info" :data-id="item.id" v-for="(item, index) in list" :key="index">
            <view class="card-background"></view>
            <view class="card-pattern"></view>
            <view class="card-content">
                <view class="card-header">
                    <view class="prize-info">
                        <text class="prize-label">奖品</text>
                        <text class="prize-name">{{ item.prizeName }} x {{ item.prizeQuantity }}</text>
                    </view>
                    <view class="status-badge">
                        <view v-if="item.is_winning == 0" class="status-tag pending">
                            <text class="status-icon">⏰</text>
                            <text class="status-text">未开奖</text>
                        </view>
                        <view v-if="item.is_winning == 1" class="status-tag completed">
                            <text class="status-icon">🎉</text>
                            <text class="status-text">已开奖</text>
                        </view>
                    </view>
                </view>

                <view class="activity-section">
                    <view class="section-title">活动标题</view>
                    <view class="activity-name">{{ item.lottery_name }}</view>
                </view>

                <view class="tickets-section">
                    <view class="section-title">我的奖券</view>
                    <view class="tickets-container">
                        <view v-if="item.user_cos.length > 0" class="ticket-item" v-for="(cos, index1) in item.user_cos" :key="index1">
                            {{ cos.lucky_number }}
                        </view>
                        <view v-if="item.user_cos.length == 0" class="ticket-item empty">暂无</view>
                    </view>
                </view>

                <view class="time-section">
                    <view class="time-item">
                        <text class="time-label">开始时间</text>
                        <view class="time-tag start">{{ item.start_time }}</view>
                    </view>
                    <view class="time-item">
                        <text class="time-label">结束时间</text>
                        <view class="time-tag end">{{ item.end_time }}</view>
                    </view>
                    <view class="time-item">
                        <text class="time-label">开奖时间</text>
                        <view class="time-tag draw">{{ item.draw_time }}</view>
                    </view>
                </view>
            </view>
        </view>
        <view :class="'cu-load ' + (!di_msg ? 'loading' : 'over')" class="load-indicator"></view>
    </view>
</template>

<script>
const app = getApp();
import http from '../../../util/http.js';
import regeneratorRuntime from '../../../util/runtime';
export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            TabCur: 0,
            list: [],
            page: 1,
            di_msg: false,
            info: {},
            uid: null
        };
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.doIt();
    },

    onShow() {
        const check = app.globalData.__PlugUnitScreen('7e1d82d91e04523ae2825c1d2991d5d6');
        if (!check) {
            uni.showToast({
                title: '未开通插件',
                icon: 'none',
                duration: 2000
            });
            return;
        }
    },

    /**
     * 加载下一页
     */
    onReachBottom() {
        this.page = this.page + 1;
        this.get_my_take();
    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {
        return {
            title: this.info.custom_title,
            path: '/yl_welore/pages/packageD/draw_list/index'
        };
    },

    methods: {
        tabSelect(e) {
            this.TabCur = e.currentTarget.dataset.id;
            this.list = [];
            this.page = 1;
            this.get_my_take();
        },

        open_info(d) {
            console.log(d);
            uni.navigateTo({
                url: '/yl_welore/pages/packageD/draw_info/index?id=' + d.currentTarget.dataset.id
            });
        },

        async doIt() {
            const e = app.globalData.getCache('userinfo');
            const do0 = await this.checkToken();
            if (!e || (do0 && do0.data && do0.data.status == 'no')) {
                //缓存为空执行登陆
                await this.getLogin();
            } else {
                //字段为空执行登陆
                if (typeof e.token_impede == 'undefined') {
                    await this.getLogin();
                } else {
                    //字段0或者小于当前时间执行登陆
                    const t = parseInt(+new Date() / 1000);
                    if (e.token_impede == 0 || t >= e.token_impede) {
                        await this.getLogin();
                    }
                }
            }
            await this.get_my_take();
        },

        getLogin() {
            return new Promise((resolve, reject) => {
                uni.login({
                    success: (res) => {
                        let params = new Object();
                        params.code = res.code;
                        http.POST(app.globalData.api_root + 'Login/index', {
                            params: params,
                            success: (open) => {
                                console.log(open);
                                let data = new Object();
                                data.openid = open.data.info.openid;
                                data.session_key = open.data.info.session_key;
                                http.POST(app.globalData.api_root + 'Login/add_tourist', {
                                    params: data,
                                    success: (d) => {
                                        console.log(d);
                                        app.globalData.setCache('userinfo', d.data.info);
                                        this.uid = d.data.info.uid;
                                        resolve(d);
                                    }
                                });
                            }
                        });
                    }
                });
            });
        },

        checkToken() {
            return new Promise((resolve, reject) => {
                const e = app.globalData.getCache('userinfo');
                if (!e) {
                    // 为了与原始逻辑保持一致，当缓存为空时，我们解析一个特定结构的对象
                    resolve({ data: { status: 'no' } });
                    return;
                }
                const b = app.globalData.api_root + 'Check/check_token';
                let params = new Object();
                params.token = e.token;
                params.openid = e.openid;
                http.POST(b, {
                    params: params,
                    success: (res) => {
                        resolve(res);
                    },
                    fail: () => {
                        uni.showModal({
                            title: '提示',
                            content: '网络繁忙，请稍候重试！',
                            showCancel: false,
                            success: function (res) {}
                        });
                        reject('网络繁忙，请稍候重试！');
                    }
                });
            });
        },

        get_my_take() {
            const b = app.globalData.api_root + 'Retrieval/index';
            const e = app.globalData.getCache('userinfo');
            if (!e) {
                // 如果没有用户信息，可能需要提示用户先登录
                console.log('获取抽奖列表失败，用户信息不存在');
                return;
            }
            let params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.page = this.page;
            params.tab = this.TabCur;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.code == 0) {
                        // 使用扩展运算符创建新数组以触发视图更新
                        this.list = [...this.list, ...res.data.list];
                        this.info = res.data.info;
                        if (res.data.list.length == 0 || res.data.list.length < 3) {
                            this.di_msg = true;
                        }
                    } else {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: function (res) {}
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        }
    }
};
</script>
<style scoped>
/* 页面整体样式 */
.page-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 30%, #ffffff 100%);
    position: relative;
}

/* 自定义标题栏 */
.custom-header {
    position: relative;
    z-index: 10;
    color: #2c3e50;
}

.header-title {
    color: #2c3e50;
    font-weight: 700;
    font-size: 36rpx;
    text-shadow: 0 2rpx 8rpx rgba(255, 255, 255, 0.3);
}

/* Tab导航容器 */
.tab-container {
    padding: 20rpx 30rpx;
    position: relative;
    z-index: 5;
}

/* 现代化导航栏 */
.modern-nav {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20rpx);
    border-radius: 50rpx;
    padding: 8rpx;
    display: flex;
    box-shadow: 0 8rpx 32rpx rgba(135, 206, 250, 0.15);
    border: 1rpx solid rgba(135, 206, 250, 0.3);
    width: 100%;
}

.nav-item {
    flex: 1;
    text-align: center;
    padding: 20rpx 30rpx;
    border-radius: 40rpx;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 0;
}

.nav-item.active {
    background: linear-gradient(135deg, #ffffff 0%, rgba(255, 255, 255, 0.95) 100%);
    box-shadow: 0 4rpx 20rpx rgba(135, 206, 250, 0.4);
    transform: translateY(-2rpx);
}

.nav-text {
    font-size: 28rpx;
    font-weight: 600;
    color: #5a6c7d;
    transition: all 0.3s ease;
}

.nav-item.active .nav-text {
    color: #4a90e2;
    font-weight: 700;
}

/* 抽奖卡片样式 */
.lottery-card {
    margin: 30rpx;
    position: relative;
    background: #ffffff;
    border-radius: 32rpx;
    overflow: hidden;
    box-shadow: 0 20rpx 60rpx rgba(135, 206, 250, 0.12);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1rpx solid rgba(135, 206, 250, 0.2);
}

.lottery-card:active {
    transform: translateY(-4rpx);
    box-shadow: 0 25rpx 80rpx rgba(135, 206, 250, 0.2);
}

/* 卡片背景装饰 */
.card-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #87ceeb 0%, #b0e0e6 100%);
    opacity: 0.04;
}

.card-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(circle at 20% 50%, rgba(135, 206, 250, 0.06) 2rpx, transparent 2rpx),
                      radial-gradient(circle at 80% 50%, rgba(176, 224, 230, 0.06) 2rpx, transparent 2rpx);
    background-size: 60rpx 60rpx;
}

/* 卡片内容 */
.card-content {
    position: relative;
    z-index: 2;
    padding: 40rpx;
}

/* 卡片头部 */
.card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30rpx;
    padding-bottom: 20rpx;
    border-bottom: 1rpx solid rgba(135, 206, 250, 0.15);
}

.prize-info {
    flex: 1;
}

.prize-label {
    display: block;
    font-size: 24rpx;
    color: #999999;
    margin-bottom: 8rpx;
    font-weight: 500;
}

.prize-name {
    font-size: 32rpx;
    font-weight: 700;
    color: #333333;
    line-height: 1.4;
}

/* 状态徽章 */
.status-badge {
    margin-left: 20rpx;
}

.status-tag {
    display: flex;
    align-items: center;
    padding: 12rpx 20rpx;
    border-radius: 50rpx;
    font-size: 24rpx;
    font-weight: 600;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.status-tag.pending {
    background: linear-gradient(135deg, #ff9a56 0%, #ff6b6b 100%);
    color: #ffffff;
}

.status-tag.completed {
    background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
    color: #ffffff;
}

.status-icon {
    margin-right: 8rpx;
    font-size: 20rpx;
}

.status-text {
    font-size: 24rpx;
}

/* 活动信息区域 */
.activity-section {
    margin-bottom: 30rpx;
}

.section-title {
    font-size: 24rpx;
    color: #999999;
    margin-bottom: 12rpx;
    font-weight: 500;
}

.activity-name {
    font-size: 28rpx;
    color: #333333;
    font-weight: 600;
    line-height: 1.5;
    word-break: break-all;
}

/* 奖券区域 */
.tickets-section {
    margin-bottom: 30rpx;
}

.tickets-container {
    display: flex;
    flex-wrap: wrap;
    gap: 12rpx;
    margin-top: 12rpx;
}

.ticket-item {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
    padding: 12rpx 20rpx;
    border-radius: 20rpx;
    font-size: 24rpx;
    font-weight: 600;
    box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.2);
    transition: all 0.3s ease;
}

.ticket-item.empty {
    background: linear-gradient(135deg, #e0e0e0 0%, #bdbdbd 100%);
    color: #757575;
}

.ticket-item:active {
    transform: scale(0.95);
}

/* 时间信息区域 */
.time-section {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
}

.time-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.time-label {
    font-size: 26rpx;
    color: #666666;
    font-weight: 500;
    min-width: 140rpx;
}

.time-tag {
    padding: 12rpx 20rpx;
    border-radius: 20rpx;
    font-size: 24rpx;
    font-weight: 600;
    color: #ffffff;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
    flex: 1;
    text-align: center;
    margin-left: 20rpx;
}

.time-tag.start {
    background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
}

.time-tag.end {
    background: linear-gradient(135deg, #ff9a56 0%, #ff6b6b 100%);
}

.time-tag.draw {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 加载指示器 */
.load-indicator {
    margin: 40rpx 0;
    opacity: 0.7;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
    .lottery-card {
        margin: 20rpx;
    }

    .card-content {
        padding: 30rpx;
    }

    .time-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8rpx;
    }

    .time-tag {
        margin-left: 0;
        width: 100%;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30rpx);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.lottery-card {
    animation: fadeInUp 0.6s ease-out;
}

.lottery-card:nth-child(2) { animation-delay: 0.1s; }
.lottery-card:nth-child(3) { animation-delay: 0.2s; }
.lottery-card:nth-child(4) { animation-delay: 0.3s; }
</style>

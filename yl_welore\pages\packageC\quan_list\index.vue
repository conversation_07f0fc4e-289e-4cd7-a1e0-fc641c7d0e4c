<template>
    <view>
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">圈子审核</view>
        </cu-custom>
        <view class="bg-gray" style="padding: 10px">
            <block v-for="(item, rr_index) in list" :key="rr_index">
                <view class="cu-list menu-avatar dasheds">
                    <view class="cu-item shadow" @tap="open_comment_info" :data-index="rr_index">
                        <view class="cu-avatar round" :style="'width: 70rpx;height: 70rpx;background-image:url(' + item.realm_icon + ');'"></view>
                        <view class="content" style="left: 120rpx; width: 70%">
                            <view class="text-grey">
                                <text class="text_num_1">{{ item.realm_name }}</text>
                            </view>
                            <view class="text-gray text-sm flex">
                                <view class="text-cut">
                                    <text style="vertical-align: middle">{{ item.solicit_origin }}</text>
                                </view>
                            </view>
                        </view>
                        <view class="action">
                            <view class="cicon-angle" style="font-size: 18px"></view>
                        </view>
                    </view>
                </view>
            </block>
            <view :class="'cu-load ' + (!di_msg ? 'loading' : 'over')"></view>
        </view>
        <view :class="'cu-modal ' + (info_mod ? 'show' : '')">
            <view class="cu-dialog">
                <view class="cu-bar bg-white justify-end">
                    <view class="content">申请详情</view>
                    <view class="action" @tap="hideModal">
                        <text class="cuIcon-close text-red"></text>
                    </view>
                </view>
                <view class="padding">
                    <scroll-view :scroll-y="true" class="padding-sm" style="height: 1000rpx; letter-spacing: 0.5px; line-height: 25px">
                        <view class="flex solid-bottom padding align-center">
                            <view>圈子名称：</view>
                            <view style="margin-left: 20rpx">{{ info.realm_name }}</view>
                        </view>
                        <view class="flex solid-bottom padding align-center">
                            <view>圈子头像：</view>
                            <view style="margin-left: 20rpx">
                                <image @tap="open_img" :data-src="info.realm_icon" style="width: 50px; height: 50px; border-radius: 50%" :src="info.realm_icon"></image>
                            </view>
                        </view>
                        <view class="flex solid-bottom padding align-center">
                            <view>圈子类型：</view>
                            <view style="margin-left: 20rpx">
                                <view style="margin-left: 20rpx">{{ info.needle_id }}</view>
                            </view>
                        </view>
                        <view class="flex solid-bottom padding align-center">
                            <view>圈子简介：</view>
                            <view style="margin-left: 20rpx">
                                <view style="margin-left: 20rpx">{{ info.realm_synopsis }}</view>
                            </view>
                        </view>
                        <view class="flex solid-bottom padding align-center">
                            <view>申请用户：</view>
                            <view style="margin-left: 20rpx">
                                <view style="margin-left: 20rpx">{{ info.user_nick_name }}</view>
                            </view>
                        </view>
                        <view class="flex solid-bottom padding align-center">
                            <view>申请圈主：</view>
                            <view style="margin-left: 20rpx">
                                <view style="margin-left: 20rpx">{{ info.is_gnaw_qulord == 1 ? '是' : '否' }}</view>
                            </view>
                        </view>
                        <view class="flex solid-bottom padding align-center">
                            <view>申请原因：</view>
                            <view style="margin-left: 20rpx">
                                <view style="margin-left: 20rpx">{{ info.solicit_origin }}</view>
                            </view>
                        </view>
                    </scroll-view>
                    <view class="flex p-xs margin-bottom-sm mb-sm">
                        <view class="flex-sub">
                            <view @tap="ok_demo" data-key="1" class="bg-green padding-sm margin-xs radius text-center shadow-blur">
                                <view class="text-lg text-white">通过</view>
                            </view>
                        </view>
                        <view class="flex-sub">
                            <view @tap="ok_demo" data-key="2" class="bg-yellow padding-sm margin-xs radius text-center shadow-blur">
                                <view class="text-lg text-white">拒绝</view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
var app = getApp();
var http = require('../../../util/http.js');
export default {
    data() {
        return {
            list: [],
            page: 1,
            info_mod: false,

            info: {
                realm_name: '',
                realm_icon: '',
                needle_id: '',
                realm_synopsis: '',
                user_nick_name: '',
                is_gnaw_qulord: 0,
                solicit_origin: ''
            },

            di_msg: false,
            rr_index: 0
        };
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.get_list();
    },

    /**
     * 加载下一页
     */
    onReachBottom() {
        this.page = this.page + 1;
        this.get_list();
    },

    methods: {
        ok_demo(d) {
            const key = d.currentTarget.dataset.key;
            uni.showModal({
                title: '操作提示',
                content: key == 1 ? '审核通过吗？' : '拒绝通过吗？',
                success: (res) => {
                    if (res.confirm) {
                        this.ok_demo_do(key);
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                }
            });
        },

        ok_demo_do(pical) {
            const b = app.globalData.api_root + 'Whisper/circle_status';
            const e = app.globalData.getCache('userinfo');
            const params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uplid = this.info.id;
            params.pical = pical;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.code == 1) {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                        this.info_mod = false;
                        this.list = [];
                        this.page = 1;
                        this.get_list();
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },

        hideModal() {
            this.info_mod = false;
        },

        open_img(d) {
            uni.previewImage({
                current: d.currentTarget.dataset.src,
                // 当前显示图片
                urls: [d.currentTarget.dataset.src] // 需要预览的图片http链接列表
            });
        },

        open_comment_info(d) {
            const index = d.currentTarget.dataset.index;
            this.info = this.list[index];
            this.info_mod = true;
        },

        get_list() {
            const b = app.globalData.api_root + 'Whisper/circle_s_list';
            const e = app.globalData.getCache('userinfo');
            const params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.page = this.page;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.length == 0 || res.data.length < 10) {
                        this.di_msg = true;
                    }
                    this.list.push(...res.data);
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        }
    }
};
</script>
<style>
/* yl_welore/pages/packageC/quan_list/index.wxss */
</style>

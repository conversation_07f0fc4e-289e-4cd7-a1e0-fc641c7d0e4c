<template>
    <view>
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">详情</view>
        </cu-custom>

        <view style="padding: 20px">
            <view class="">
                <view class="" style="font-size: 20px; font-weight: 700">{{ info.trouble }}</view>
                <view class="" style="margin-top: 20px">
                    <text>{{ info.answer }}</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
var app = getApp();
import http from '../../../util/http.js';
export default {
    data() {
        return {
            info: [],
            page: 1,
            di_msg: false,
            id: ''
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.id = options.id;
        this.get_help_info();
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow () {},
    /**
     * 用户点击右上角分享
     */
    onShareAppMessage () {
        var forward = app.globalData.forward;
        console.log(forward);
        if (forward) {
            return {
                title: forward.title,
                path: '/yl_welore/pages/index/index',
                imageUrl: forward.reis_img
            };
        } else {
            return {
                title: '您的好友给您发了一条信息',
                path: '/yl_welore/pages/index/index'
            };
        }
    },
    methods: {
        /**
         * 获取会员信息
         */
        get_help_info() {
            var b = app.globalData.api_root + 'User/get_help_info_desc';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.id;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        this.info = res.data.info;
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () =>{
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) =>{}
                    });
                }
            });
        }
    }
};
</script>
<style>
page {
    background-color: #fff;
}
</style>

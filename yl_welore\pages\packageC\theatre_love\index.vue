<template>
    <view>
        <view class="page"></view>
        <cu-custom bgColor="transparent" :isSearch="false" :isBack="true">
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">我的喜欢</view>
        </cu-custom>
        <view class="flex align-center justify-end" style="padding: 0rpx 30rpx">
            <view>
                <button @tap="openDel" class="cu-btn round bg-orange shadow" style="font-size: 24rpx; height: 55rpx; line-height: 55rpx; padding: 0 20rpx">编辑</button>
            </view>
        </view>
        <view style="padding: 15rpx">
            <view class="flex align-center" style="margin-bottom: 40rpx; position: relative" v-for="(item, index) in list" :key="index">
                <view style="width: 200rpx">
                    <image :lazy-load="true" :src="item.poster_url" style="width: 200rpx; max-height: 350rpx; border-radius: 20rpx"></image>
                </view>

                <view style="margin-left: 20rpx">
                    <view>{{ item.title }}</view>
                    <view style="margin-top: 30rpx; font-size: 24rpx; color: #999999">全{{ item.total_episodes }}集</view>
                    <view style="margin-top: 30rpx; color: #e7cb4d">{{ item.msc }}</view>
                    <view class="text_num" style="margin-top: 30rpx; font-size: 26rpx; color: #999999">{{ item.plot_summary }}</view>
                </view>

                <view v-if="del" @tap.stop.prevent="delDo" :data-id="item.id" style="position: absolute; top: 20rpx; right: 20rpx">
                    <button class="cu-btn icon bg-red">
                        <text class="_icon-delete"></text>
                    </button>
                </view>
            </view>
        </view>
        <view :class="'cu-load ' + (!di_msg ? 'loading' : 'over')" style="padding-bottom: 100rpx"></view>
        <!-- <view style="position: fixed;bottom: 100rpx;width: 100%;text-align: center;">
    <button class="cu-btn icon shadow">
        <text class="_icon-add-round" style="font-size: 100rpx;color: orange;"></text>
  </button>
</view> -->
    </view>
</template>

<script>
import http from '../../../util/http.js';
const app = getApp();
export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            list: [],
            page: 1,
            di_msg: false,
            del: false
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.getMyLove();
    },
    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {
        this.page = this.page + 1;
        this.getMyLove();
    },
    methods: {
        openDel() {
            this.del = !this.del;
        },
        delDo(d) {
            console.log(d);
            const b = app.globalData.api_root + 'Microseries/delDo';
            const e = app.globalData.getCache('userinfo');
            const params = new Object();
            params.openid = e.openid;
            params.id = d.currentTarget.dataset.id;
            params.type = 0;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'error') {
                        uni.showToast({
                            title: res.data.msg,
                            duration: 2000
                        });
                    }
                    this.page = 1;
                    this.list = [];
                    this.getMyLove();
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },
        getMyLove() {
            const b = app.globalData.api_root + 'Microseries/getMyLove';
            const e = app.globalData.getCache('userinfo');
            const params = new Object();
            params.openid = e.openid;
            params.page = this.page;
            params.type = 0;
            http.POST(b, {
                params: params,
                success: (res) => {
                    const list = this.list;
                    if (res.data.list.length < 10) {
                        this.di_msg = true;
                    }
                    list.push(...res.data.list);
                    this.list = list;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        }
    }
};
</script>
<style>
.page {
    background-image: linear-gradient(to bottom, #ffedc7 0%, #fffdf9 50%, #fffdf8 100%);
    height: 100vh;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: -1;
}
</style>

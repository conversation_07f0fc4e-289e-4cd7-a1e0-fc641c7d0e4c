{"description": "项目私有配置文件。此文件中的内容将覆盖 project.config.json 中的相同字段。项目的改动优先同步到此文件中。详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "condition": {"miniprogram": {"list": [{"name": "yl_welore/pages/packageA/article/index", "pathName": "yl_welore/pages/packageA/article/index", "query": "id=4214&type=1", "launchMode": "default", "scene": null}, {"name": "yl_welore/pages/packageA/add/index", "pathName": "yl_welore/pages/packageA/add/index", "query": "type=2&fa_class=0&name=&gambit_name=&gambit_id=0", "launchMode": "default", "scene": null}, {"name": "yl_welore/pages/packageC/theatre/index", "pathName": "yl_welore/pages/packageC/theatre/index", "query": "", "launchMode": "default", "scene": null}, {"name": "yl_welore/pages/packageC/theatre/index", "pathName": "yl_welore/pages/packageC/theatre/index", "query": "", "launchMode": "default", "scene": null}, {"name": "yl_welore/pages/packageA/good_info/index", "pathName": "yl_welore/pages/packageA/good_info/index", "query": "id=19", "launchMode": "default", "scene": null}, {"name": "yl_welore/pages/packageE/employment_list/index", "pathName": "yl_welore/pages/packageE/employment_list/index", "query": "", "launchMode": "default", "scene": null}, {"name": "yl_welore/pages/packageC/theatre_video/index", "pathName": "yl_welore/pages/packageC/theatre_video/index", "query": "id=3", "launchMode": "default", "scene": null}, {"name": "yl_welore/pages/packageA/article/index", "pathName": "yl_welore/pages/packageA/article/index", "query": "id=4155&type=0", "launchMode": "default", "scene": null}, {"name": "yl_welore/pages/packageC/theatre_video/index", "pathName": "yl_welore/pages/packageC/theatre_video/index", "query": "id=1", "launchMode": "default", "scene": null}, {"name": "yl_welore/pages/packageC/theatre_video/index", "pathName": "yl_welore/pages/packageC/theatre_video/index", "query": "id=6", "launchMode": "default", "scene": null}, {"name": "yl_welore/pages/packageA/article/index", "pathName": "yl_welore/pages/packageA/article/index", "query": "id=736&type=0", "launchMode": "default", "scene": null}, {"name": "yl_welore/pages/packageC/theatre_add/index", "pathName": "yl_welore/pages/packageC/theatre_add/index", "query": "", "launchMode": "default", "scene": null}, {"name": "yl_welore/pages/packageC/theatre/index", "pathName": "yl_welore/pages/packageC/theatre/index", "query": "", "launchMode": "default", "scene": null}, {"name": "yl_welore/pages/packageC/theatre_history/index", "pathName": "yl_welore/pages/packageC/theatre_history/index", "query": "", "launchMode": "default", "scene": null}, {"name": "yl_welore/pages/packageC/theatre_love/index", "pathName": "yl_welore/pages/packageC/theatre_love/index", "query": "", "launchMode": "default", "scene": null}, {"name": "yl_welore/pages/packageC/theatre_video/index", "pathName": "yl_welore/pages/packageC/theatre_video/index", "query": "id=1", "launchMode": "default", "scene": null}, {"name": "yl_welore/pages/packageC/theatre/index", "pathName": "yl_welore/pages/packageC/theatre/index", "query": "", "launchMode": "default", "scene": null}, {"name": "yl_welore/pages/packageC/theatre_video/index", "pathName": "yl_welore/pages/packageC/theatre_video/index", "query": "id=3", "launchMode": "default", "scene": null}, {"name": "yl_welore/pages/packageC/theatre/index", "pathName": "yl_welore/pages/packageC/theatre/index", "query": "", "launchMode": "default", "scene": null}, {"name": "yl_welore/pages/packageC/theatre_my_series/index", "pathName": "yl_welore/pages/packageC/theatre_my_series/index", "query": "id=2", "launchMode": "default", "scene": null}, {"name": "yl_welore/pages/packageC/theatre_add/index", "pathName": "yl_welore/pages/packageC/theatre_add/index", "query": "", "launchMode": "default", "scene": null}, {"name": "yl_welore/pages/packageC/theatre_video/index", "pathName": "yl_welore/pages/packageC/theatre_video/index", "query": "", "launchMode": "default", "scene": null}, {"name": "yl_welore/pages/packageC/theatre_add/index", "pathName": "yl_welore/pages/packageC/theatre_add/index", "query": "", "launchMode": "default", "scene": null}, {"name": "yl_welore/pages/packageC/theatre_my/index", "pathName": "yl_welore/pages/packageC/theatre_my/index", "query": "", "launchMode": "default", "scene": null}, {"name": "", "pathName": "yl_welore/pages/packageE/used_goods_list/index", "query": "", "launchMode": "default", "scene": null}, {"name": "", "pathName": "yl_welore/pages/packageE/used_goods_info/index", "query": "id=34", "launchMode": "default", "scene": null}, {"name": "", "pathName": "yl_welore/pages/packageA/add/index", "query": "type=3", "launchMode": "default", "scene": null}, {"name": "", "pathName": "yl_welore/pages/packageE/employment_list/index", "query": "", "launchMode": "default", "scene": null}, {"name": "", "pathName": "yl_welore/pages/packageB/circle_info/index", "query": "id=1", "launchMode": "default", "scene": null}, {"name": "", "pathName": "yl_welore/pages/packageF/full_video/index", "query": "id=212", "launchMode": "default", "scene": null}, {"name": "", "pathName": "yl_welore/pages/packageA/article/index", "query": "id=4031&type=0", "launchMode": "default", "scene": null}, {"name": "", "pathName": "yl_welore/pages/packageE/convenience_info/index", "query": "", "launchMode": "default", "scene": null}, {"name": "", "pathName": "yl_welore/pages/packageD/whisper_info/index", "query": "id=318", "launchMode": "default", "scene": null}, {"name": "", "pathName": "yl_welore/pages/packageD/whisper_info/index", "query": "id=321", "launchMode": "default", "scene": null}, {"name": "", "pathName": "yl_welore/pages/packageD/top_ten/index", "query": "", "launchMode": "default", "scene": null}, {"name": "", "pathName": "yl_welore/pages/packageA/article/index", "query": "id=289&type=0", "launchMode": "default", "scene": null}, {"name": "", "pathName": "yl_welore/pages/packageA/article/index", "query": "id=4046&type=0", "launchMode": "default", "scene": null}, {"name": "", "pathName": "yl_welore/pages/activity_list/index", "query": "", "launchMode": "default", "scene": null}, {"name": "", "pathName": "yl_welore/pages/packageA/article/index", "query": "id=1619&type=2", "launchMode": "default", "scene": null}, {"name": "", "pathName": "yl_welore/pages/packageE/employment_list/index", "query": "", "launchMode": "default", "scene": null}, {"name": "", "pathName": "yl_welore/pages/packageD/draw_list/index", "query": "", "launchMode": "default", "scene": null}, {"name": "", "pathName": "yl_welore/pages/packageD/draw_info/index", "query": "", "launchMode": "default", "scene": null}, {"name": "", "pathName": "yl_welore/pages/packageD/draw_info/index", "query": "id=1", "launchMode": "default", "scene": null}, {"name": "", "pathName": "yl_welore/pages/packageD/draw_prize/index", "query": "id=1", "launchMode": "default", "scene": null}, {"name": "", "pathName": "yl_welore/pages/packageA/circle_info/index", "query": "id=27", "launchMode": "default", "scene": null}, {"name": "", "pathName": "yl_welore/pages/packageA/circle_info/index", "query": "id=27", "launchMode": "default", "scene": null}, {"name": "", "pathName": "yl_welore/pages/packageA/article/index", "query": "id=6756&type=0", "launchMode": "default", "scene": null}, {"name": "", "pathName": "yl_welore/pages/packageA/article/index", "query": "id=3928&type=2", "launchMode": "default", "scene": null}, {"name": "", "pathName": "yl_welore/pages/packageE/certification_list/index", "query": "", "launchMode": "default", "scene": null}, {"name": "", "pathName": "yl_welore/pages/packageC/theatre/index", "query": "", "launchMode": "default", "scene": null}]}}, "libVersion": "3.8.8", "setting": {"urlCheck": true, "compileHotReLoad": true}, "projectname": "yl_welore"}
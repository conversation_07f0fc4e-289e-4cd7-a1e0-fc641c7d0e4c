<template>
    <view>
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">内容审核</view>
        </cu-custom>
        <view class="grid col-3 margin-top text-center">
            <view @tap="open_url" data-id="1" class="padding-sm text-center pos">
                <image style="width: 100rpx; height: 100rpx" :src="http_root + 'addons/yl_welore/web/static/examine/ziliao.png'"></image>
                <view class="text_my">帖子审核</view>
                <view class="cu-tag badge my_dian">{{ info.tie }}</view>
            </view>
            <view @tap="open_url" data-id="2" class="padding-sm text-center pos">
                <image style="width: 100rpx; height: 100rpx" :src="http_root + 'addons/yl_welore/web/static/examine/xiaoxi.png'"></image>
                <view class="text_my">回复审核</view>
                <view class="cu-tag badge my_dian">{{ info.hui }}</view>
            </view>
            <view @tap="open_url" data-id="3" class="padding-sm text-center pos">
                <image style="width: 100rpx; height: 100rpx" :src="http_root + 'addons/yl_welore/web/static/examine/pinglun.png'"></image>
                <view class="text_my">评论回复</view>
                <view class="cu-tag badge my_dian">{{ info.ping }}</view>
            </view>
            <view v-if="open_user_dianping" @tap="open_url" data-id="4" class="padding-sm text-center pos">
                <image style="width: 100rpx; height: 100rpx" :src="http_root + 'addons/yl_welore/web/static/examine/dianzan.png'"></image>
                <view class="text_my">点评审核</view>
                <view class="cu-tag badge my_dian">{{ info.dian }}</view>
            </view>
            <view @tap="open_url" data-id="5" class="padding-sm text-center pos">
                <image style="width: 100rpx; height: 100rpx" :src="http_root + 'addons/yl_welore/web/static/examine/gerenzhongxin.png'"></image>
                <view class="text_my">认证审核</view>
                <view class="cu-tag badge my_dian">{{ info.ren }}</view>
            </view>
            <view @tap="open_url" data-id="6" class="padding-sm text-center pos">
                <image style="width: 100rpx; height: 100rpx" :src="http_root + 'addons/yl_welore/web/static/examine/fenlei.png'"></image>
                <view class="text_my">圈子审核</view>
                <view class="cu-tag badge my_dian">{{ info.quan }}</view>
            </view>
            <view @tap="open_url" data-id="7" class="padding-sm text-center pos">
                <image style="width: 100rpx; height: 100rpx" :src="http_root + 'addons/yl_welore/web/static/examine/shudong.png'"></image>
                <view class="text_my">树洞内容</view>
                <view class="cu-tag badge my_dian">{{ info.sprout }}</view>
            </view>
        </view>
    </view>
</template>

<script>
var app = getApp();
var http = require('../../../util/http.js');
export default {
    data() {
        return {
            http_root: app.globalData.http_root,

            info: {
                tie: '',
                hui: '',
                ping: '',
                dian: '',
                ren: '',
                quan: '',
                sprout: ''
            },

            open_user_dianping: ''
        };
    },
    onLoad(options) {
        var dianping = app.globalData.__PlugUnitScreen('6f7f2e87a00f58c6d00817b72dd332c9');
        this.open_user_dianping = dianping;
        this.get_quantity();
    },
    methods: {
        get_quantity() {
            var b = app.globalData.api_root + 'Nameplate/get_quantity';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    this.info = res.data;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },

        open_url(d) {
            var index = d.currentTarget.dataset.id;
            console.log(index);
            switch (index) {
                case '1':
                    uni.navigateTo({
                        url: '/yl_welore/pages/packageC/paper_service/index?id=0'
                    });
                    break;
                case '2':
                    uni.navigateTo({
                        url: '/yl_welore/pages/packageC/paper_service/index?id=1'
                    });
                    break;
                case '3':
                    uni.navigateTo({
                        url: '/yl_welore/pages/packageC/paper_service/index?id=2'
                    });
                    break;
                case '4':
                    uni.navigateTo({
                        url: '/yl_welore/pages/packageC/comment_list/index'
                    });
                    break;
                case '5':
                    uni.navigateTo({
                        url: '/yl_welore/pages/packageC/auth_list/index'
                    });
                    break;
                case '6':
                    uni.navigateTo({
                        url: '/yl_welore/pages/packageC/quan_list/index'
                    });
                    break;
                case '7':
                    uni.navigateTo({
                        url: '/yl_welore/pages/packageD/whisper_list/index'
                    });
                    break;
            }
        }
    }
};
</script>
<style>
page {
    background-color: #ffffff;
}

.text_my {
    font-size: 13px;
}

.pos {
    position: relative;
}

.my_dian {
    right: 60rpx !important;
    border-radius: 50% !important;
    font-size: 24rpx !important;
    padding: 10rpx !important;
    height: 35rpx !important;
    width: 35rpx !important;
}
</style>

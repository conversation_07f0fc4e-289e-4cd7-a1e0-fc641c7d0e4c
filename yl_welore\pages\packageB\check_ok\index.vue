<template>
    <view>
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">活动验证</view>
        </cu-custom>
        <view style="text-align: center; margin-top: 50px">
            <view style="margin-bottom: 50rpx">请输入活动验证码</view>

            <paySix
                @valueSix="valueSix"
                :input_value="input_value"
                :value_length="value_length"
                :isNext="inputData.isNext"
                :get_focus="inputData.get_focus"
                :focus_class="inputData.focus_class"
                :value_num="inputData.value_num"
                :height="inputData.height"
                :width="inputData.width"
                :see="inputData.see"
                :interval="inputData.interval"
            ></paySix>
            <button @tap="okClick" class="cu-btn bg-green lg" style="width: 80%">验证</button>
        </view>
        <view style="margin: 40px 0px 10px 20px">已验证用户</view>
        <scroll-view scroll-y style="height: 25em">
            <view style="padding: 15px" v-for="(item, index) in list" :key="index">
                <view style="border: 1rpx solid rgba(0, 0, 0, 0.1); border-radius: 5px">
                    <view class="flex solid-bottom justify-between" style="padding: 10px">
                        <view>
                            <image :src="item.user_head_sculpture" style="width: 35px; height: 35px; vertical-align: middle"></image>
                            <text style="vertical-align: middle">{{ item.user_nick_name }}</text>
                        </view>
                        <view>
                            <image src="" style="width: 35px; height: 35px; vertical-align: middle"></image>
                            <text style="vertical-align: middle">{{ item.write_off_time }}</text>
                        </view>
                    </view>
                    <view style="margin: 10px; text-align: center">
                        <text style="letter-spacing: 10px; font-weight: 700; font-size: 30px">{{ item.rand_captcha }}</text>
                    </view>
                </view>
            </view>
            <view :class="'cu-load ' + (list.length == 0 ? 'over' : '')"></view>
        </scroll-view>
    </view>
</template>

<script>
import paySix from '@/yl_welore/util/component/component';
import http from '@/yl_welore/util/http.js';
export default {
    components: {
        paySix
    },
    data() {
        return {
            input_value: '',

            //输入框的初始内容
            value_length: 0,

            //输入框密码位数
            // 输入框参数设置
            inputData: {
                isNext: true,
                //是否有下一步的按钮
                get_focus: true,
                //输入框的聚焦状态
                focus_class: true,
                //输入框聚焦样式
                value_num: [1, 2, 3, 4, 5, 6, 7, 8],
                //输入框格子数
                height: '45px',
                //输入框高度
                width: '340px',
                //输入框宽度
                see: true,
                //是否明文展示
                interval: true //是否显示间隔格子
            },

            list: [],
            height: '',
            isIpx: '',
            paper_id: '',
            check: ''
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.height = getApp().globalData.height;
        this.isIpx = getApp().globalData.isIpx;
        this.paper_id = options.id;
        this.get_verified();
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {},
    methods: {
        valueSix(d) {
            console.log(d);
            const t = d.detail;
            this.check = t;
        },

        /**
         * 验证
         */
        okClick() {
            const b = getApp().globalData.api_root + 'User/ok_click';
            const e = getApp().globalData.getCache('userinfo');
            const params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.check = this.check;
            params.paper_id = this.paper_id;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: (res) => {}
                        });
                        this.value_length = 0;
                        this.input_value = '';
                        this.get_verified();
                    } else {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: (res) => {}
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },

        /**
         * 获取已验证
         */
        get_verified() {
            const b = getApp().globalData.api_root + 'User/get_verified';
            const e = getApp().globalData.getCache('userinfo');
            const params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.paper_id = this.paper_id;
            http.POST(b, {
                params: params,
                success: (res) => {
                    this.list = res.data;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        }
    }
};
</script>
<style>
page {
    background-color: #fff;
}
</style>

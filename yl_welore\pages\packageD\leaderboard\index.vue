<template>
    <view>
        <div class="bg">
            <cu-custom :isSearch="false" :isBack="true" :ShowUid="false">
                <view slot="backText">返回</view>
                <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx"></view>
            </cu-custom>
            <scroll-view scroll-x class="nav text-center" style="color: #bbb0ff">
                <view v-if="jifen_check == 0" @tap="tabSelect"
                    :class="'cu-item ' + (0 == TabCur ? 'text-white cur' : '')" data-id="0">上周</view>
                <view v-if="jifen_check == 0" @tap="tabSelect"
                    :class="'cu-item ' + (1 == TabCur ? 'text-white cur' : '')" data-id="1">周榜</view>
                <view v-if="jifen_check == 0" @tap="tabSelect"
                    :class="'cu-item ' + (2 == TabCur ? 'text-white cur' : '')" data-id="2">月榜</view>
                <view v-if="jifen_check == 1" @tap="tabSelect" class="cu-item text-white cur" data-id="3">榜单</view>
            </scroll-view>
            <view style="text-align: center; position: relative">
                <image class="now_level" mode="widthFix" style="width: 90%; margin-top: 40rpx"
                    :src="http_root + 'addons/yl_welore/web/static/mineIcon/rank/ph_lt.png'"></image>
                <view style=" position: absolute; left: 37.5%; top: 13%; z-index: 100; overflow: initial">
                    <image @tap="open_user" :data-id="list[0].user_id" style="height: 190rpx; width: 190rpx;"
                        :src="http_root + 'addons/yl_welore/web/static/mineIcon/rank/di1.png'">
                    </image>
                    <image v-if="list[0].user_head_sculpture"
                        style="width: 110rpx; height: 110rpx; border-radius: 50%; position: absolute;left: 19%;top: 20%;z-index: 0;"
                        :src="list[0].user_head_sculpture"></image>
                    <view v-if="list[0].user_nick_name" class="text_num_1"
                        style="font-weight: 600; margin-top: 5px; color: #fb6f80; position: absolute; left: 0; right: 0">
                        {{ list[0].user_nick_name }}
                    </view>
                    <view v-if="!list[0].user_nick_name" class="text_num_1"
                        style="font-weight: 300; margin-top: 5px; color: #fb6f80; position: absolute; left: 0; right: 0">
                        [虚位以待]
                    </view>
                </view>
                <view style=" position: absolute; left: 9%; top: 21%; z-index: 100; overflow: initial">
                    <image @tap="open_user" :data-id="list[1].user_id" style="height: 160rpx; width: 160rpx;"
                        :src="http_root + 'addons/yl_welore/web/static/mineIcon/rank/di2.png'">
                    </image>
                    <image v-if="list[1].user_head_sculpture"
                        style="width: 95rpx; height: 95rpx; border-radius: 50%; position: absolute; left: 20%; top: 19%;z-index: 0;"
                        :src="list[1].user_head_sculpture"></image>
                    <view v-if="list[1].user_nick_name" class="text_num_1"
                        style="font-weight: 600; margin-top: 5px; color: #fb6f80; position: absolute; left: 0; right: 0">
                        {{ list[1].user_nick_name }}
                    </view>
                    <view v-if="!list[1].user_nick_name" class="text_num_1"
                        style="font-weight: 300; margin-top: 5px; color: #fb6f80; position: absolute; left: 0; right: 0">
                        [虚位以待]
                    </view>
                </view>
                <view style=" position: absolute; left: 71.5%; top: 24%; z-index: 100; overflow: initial">
                    <image @tap="open_user" :data-id="list[2].user_id"
                        style="height: 150rpx; width: 150rpx;"
                        :src="http_root + 'addons/yl_welore/web/static/mineIcon/rank/di3.png'">
                    </image>
                    <image v-if="list[2].user_head_sculpture"
                        style="width: 96rpx; height: 96rpx; border-radius: 50%; position: absolute;left: 20%;top: 17%;z-index: 0;"
                        :src="list[2].user_head_sculpture"></image>
                    <view v-if="list[2].user_nick_name" class="text_num_1"
                        style="font-weight: 600; margin-top: 5px; color: #fb6f80; position: absolute; left: 0; right: 0">
                        {{ list[2].user_nick_name }}
                    </view>
                    <view v-if="!list[2].user_nick_name" class="text_num_1"
                        style="font-weight: 300; margin-top: 5px; color: #fb6f80; position: absolute; left: 0; right: 0">
                        [虚位以待]
                    </view>

                </view>
                <view @tap="showModal" data-target="viewModal" style="
                        z-index: 100;
                        padding: 0px 40rpx;
                        height: 80rpx;
                        background-color: rgba(151, 129, 253, 0.9);
                        text-align: left;
                        position: absolute;
                        width: 100%;
                        bottom: 0;
                    ">
                    <text class="cuIcon-upstagefill lg text-white"
                        style="line-height: 80rpx; font-size: 32rpx; margin-right: 10rpx"></text>
                    <text style="color: #ffffff; line-height: 80rpx">当前榜单：{{ info }}</text>
                    <text  data-target="viewModal"
                        style="color: #ffffff; line-height: 80rpx; float: right">切换榜单</text>
                    <text class="cuIcon-order lg text-white re"
                        style="line-height: 80rpx; font-size: 32rpx; margin-right: 10rpx; float: right"></text>
                </view>
            </view>
            <view class="bg-white" style="width: 95%; border-radius: 10px; margin: 0 auto; margin-top: 30rpx">
                <view class="cu-list menu-avatar" style="border-radius: 10px">
                    <view v-if="index > 2" @tap="open_user" :data-id="item.user_id" class="cu-item"
                        style="justify-content: flex-start; margin: 10px 0px" v-for="(item, index) in list"
                        :key="index">
                        <view style="font-size: 20px; font-weight: 300; margin-left: 24px">{{ index + 1 }}</view>

                        <view class="cu-avatar round index5"
                            :style="'background-image:url(' + item.user_head_sculpture + ');left: 100rpx;'"></view>

                        <view class="content" style="left: 218rpx">
                            <view class="text-black">{{ item.user_nick_name }}</view>
                            <view class="text-gray text-sm flex">
                                <text class="text-cut">
                                    <text style="color: #fb6f80" class="cuIcon-hotfill lg"></text>
                                    <text>热度</text>
                                    <text style="margin-left: 10px">{{ item.finance }}</text>
                                </text>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </div>

        <view :class="'DrawerClose ' + (modalName == 'viewModal' ? 'show' : '')" @tap="hideModal">
            <text class="cuIcon-pullright"></text>
        </view>
        <scroll-view scroll-y :class="'DrawerWindow ' + (modalName == 'viewModal' ? 'show' : '')">
            <view class="cu-list menu card-menu margin-top-xl margin-bottom-xl shadow-lg">
                <view class="cu-item arrow" v-for="(item, index) in rank" :key="index">
                    <view @tap="open_new_ph" :data-id="item.id" class="content">
                        <text class="cuIcon-upstagefill text-yellow"></text>
                        <text class="text-black" style="font-weight: 300">{{ item.ranking_name }}</text>
                    </view>
                </view>
            </view>
        </scroll-view>
    </view>
</template>

<script>
const app = getApp();
import http from '../../../util/http.js';
export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            http_root: app.globalData.http_root,
            id: 0,
            list: [],
            info: '',
            TabCur: 1,
            rank: [],
            jifen_check: 0,
            modalName:null,
        };
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        uni.hideShareMenu();
        this.id = options.id;
        this.get_ph_info();
        this.get_ph_list();
    },

    methods: {
        open_user(item) {
            var id = item.currentTarget.dataset.id;
            if (id) {
                uni.navigateTo({
                    url: '/yl_welore/pages/packageB/my_home/index?id=' + id
                });
            }
        },
        get_ph_list() {
            const b = app.globalData.api_root + 'Ranking/get_ph_list';
            const e = app.globalData.getCache('userinfo');
            const params = new Object();
            params.openid = e.openid;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    this.rank = res.data.list;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) { }
                    });
                }
            });
        },
        tabSelect(e) {
            this.TabCur = e.currentTarget.dataset.id;
            this.get_ph_info();
        },
        showModal(e) {
            console.log(e);
            this.modalName = e.currentTarget.dataset.target;
        },
        hideModal(e) {
            this.modalName = null;
        },
        open_new_ph(item) {
            const id = item.currentTarget.dataset.id;
            this.TabCur = 1;
            this.id = id;
            this.get_ph_info();
            this.hideModal();
        },
        //获取用户信息
        get_ph_info() {
            const b = app.globalData.api_root + 'Ranking/get_ph_info';
            const e = app.globalData.getCache('userinfo');
            const params = new Object();
            params.openid = e.openid;
            params.id = this.id;
            params.type = this.TabCur;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    // 安全检查：确保数据路径存在且wont_sort是有效数组
                    const wont_sort = res.data?.name?.wont_sort;
                    if (Array.isArray(wont_sort) && wont_sort.length > 0 && wont_sort[0] == 'jifen' && wont_sort.length == 1) {
                        this.jifen_check = 1;
                    } else {
                        this.jifen_check = 0; // 重置为默认值
                    }
                    this.list = res.data.list;
                    this.info = res.data.info;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) { }
                    });
                }
            });
        }
    }
};
</script>
<style>
.bg {
    position: absolute;
    width: 100%;
    height: 120%;
    background-image: linear-gradient(to top, #5059ff 0%, #871cfe 118%);
}

.re {
    -moz-transform: rotate(-90deg);
    -webkit-transform: rotate(-90deg);
    display: inline-block;
}

.DrawerPage {
    position: fixed;
    width: 100vw;
    height: 100vh;
    left: 0vw;
    background-color: #f1f1f1;
    transition: all 0.4s;
}

.DrawerPage.show {
    transform: scale(0.9, 0.9);
    left: 85vw;
    box-shadow: 0 0 60rpx rgba(0, 0, 0, 0.2);
    transform-origin: 0;
}

.cu-list.menu>.cu-item {
    background-color: transparent;
}

.DrawerWindow {
    position: fixed;
    width: 50vw;
    height: 90vh;
    left: 0;
    top: 10%;
    transform: scale(0.9, 0.9) translateX(-100%);
    opacity: 0;
    pointer-events: none;
    transition: all 0.4s;
    z-index: 9999;
    background-color: rgba(248, 248, 255, 1);
    /* background-image: linear-gradient(to top, #c79081 0%, #dfa579 100%); */
    border-radius: 10px;
}

.DrawerWindow.show {
    transform: scale(1, 1) translateX(0%);
    opacity: 1;
    pointer-events: all;
}

.DrawerClose {
    position: absolute;
    width: 40vw;
    height: 100vh;
    right: 0;
    top: 0;
    color: transparent;
    padding-bottom: 30rpx;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    background-image: linear-gradient(90deg, rgba(0, 0, 0, 0.01), rgba(0, 0, 0, 0.6));
    letter-spacing: 5px;
    font-size: 50rpx;
    opacity: 0;
    pointer-events: none;
    transition: all 0.4s;
    z-index: 999;
}

.DrawerClose.show {
    opacity: 1;
    pointer-events: all;
    width: 50vw;
    color: #fff;
}

.DrawerPage .cu-bar.tabbar .action button.icon {
    width: 64rpx;
    height: 64rpx;
    line-height: 64rpx;
    margin: 0;
    display: inline-block;
}

.DrawerPage .cu-bar.tabbar .action .cu-avatar {
    margin: 0;
}

.DrawerPage .nav {
    flex: 1;
}

.DrawerPage .nav .cu-item.cur {
    border-bottom: 0;
    position: relative;
}

.DrawerPage .nav .cu-item.cur::after {
    content: '';
    width: 10rpx;
    height: 10rpx;
    background-color: currentColor;
    position: absolute;
    bottom: 10rpx;
    border-radius: 10rpx;
    left: 0;
    right: 0;
    margin: auto;
}

.DrawerPage .cu-bar.tabbar .action {
    flex: initial;
}
</style>

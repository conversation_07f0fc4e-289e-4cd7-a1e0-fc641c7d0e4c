<template>
    <view class="page-container">
        <!-- 全新极简头部设计 -->
        <cu-custom bgColor="none" :isSearch="false" :isBack="true">
            <view slot="backText">
                <text class="back-text">返回</text>
            </view>
            <view slot="content" class="header-title">
                <text class="gift-emoji">🎁</text>
                <text class="title-text">我的礼物</text>
            </view>
        </cu-custom>

        <!-- 清新统计卡片 -->
        <view class="stats-container">
            <view class="stats-card">
                <view class="stats-item">
                    <text class="stats-number">{{ getTotalGifts() }}</text>
                    <text class="stats-label">收到礼物</text>
                </view>
                <view class="stats-divider"></view>
                <view class="stats-item">
                    <text class="stats-number">{{ getUniqueSenders() }}</text>
                    <text class="stats-label">送礼好友</text>
                </view>
            </view>
        </view>

        <!-- 现代化礼物列表 -->
        <view class="gifts-container">
            <view class="year-section" v-for="(item, index) in my_list" :key="index">
                <!-- 简洁年份标签 -->
                <view class="year-tag">
                    <view class="year-line"></view>
                    <view class="year-badge">
                        <text class="year-text">{{ item.year }}</text>
                    </view>
                    <view class="year-line"></view>
                </view>

                <!-- 礼物卡片列表 -->
                <view class="gift-list">
                    <view
                        class="gift-card"
                        v-for="(kk, index1) in item.list"
                        :key="index1"
                        :style="{ 'animation-delay': (index1 * 0.1) + 's' }"
                    >
                        <!-- 日期标签 -->
                        <view class="date-tag">
                            <text class="date-text">{{ item.month }}.{{ item.day }}</text>
                        </view>

                        <!-- 用户信息 -->
                        <view class="user-section">
                            <view class="avatar-container">
                                <image
                                    class="user-avatar"
                                    :src="kk.user_head_sculpture"
                                    mode="aspectFill"
                                ></image>
                            </view>
                            <view class="user-info">
                                <text class="user-name">{{ kk.user_nick_name }}</text>
                                <text class="action-text">送了一份礼物</text>
                            </view>
                        </view>

                        <!-- 礼物信息 -->
                        <view class="gift-info">
                            <view class="gift-icon-wrapper">
                                <view class="gift-box-icon"></view>
                            </view>
                            <text class="gift-name">{{ kk.bute_name }}</text>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 简洁加载状态 -->
        <view class="load-container">
            <view v-if="!di_msg" class="loading-state">
                <view class="loading-spinner"></view>
                <text class="loading-text">加载更多礼物...</text>
            </view>
            <view v-else class="completed-state">
                <view class="completed-icon"></view>
                <text class="completed-text">所有礼物都在这里了</text>
                <text class="completed-sub">感谢每一份心意 ❤️</text>
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp();
var http = require('../../../util/http.js');
export default {
    data() {
        return {
            page: 1,
            my_list: [],
            di_msg: false,
            height: '',

            kk: {
                user_head_sculpture: '',
                user_nick_name: '',
                bute_name: ''
            }
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.height = app.globalData.height;
        this.page = 1;
        this.get_my_rec();
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {},
    /**
     * 加载下一页
     */
    onReachBottom() {
        this.page = this.page + 1;
        this.get_my_rec();
    },
    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {},
    methods: {
        /**
         * 获取礼物总数
         */
        getTotalGifts() {
            let total = 0;
            this.my_list.forEach(yearData => {
                total += yearData.list.length;
            });
            return total;
        },
        /**
         * 获取唯一送礼人数
         */
        getUniqueSenders() {
            const senders = new Set();
            this.my_list.forEach(yearData => {
                yearData.list.forEach(gift => {
                    senders.add(gift.user_nick_name);
                });
            });
            return senders.size;
        },
        /**
         * 我收到的礼物
         */
        get_my_rec() {
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            params.page = this.page;
            var b = app.globalData.api_root + 'User/get_my_rec';
            var allMsg = that.my_list;
            http.POST(b, {
                params: params,
                success: function (res) {
                    console.log(res);
                    if (res.data.status == 'success') {
                        for (var i = 0; i < res.data.info.length; i++) {
                            allMsg.push(res.data.info[i]);
                        }
                        that.my_list = allMsg;
                        if (res.data.info.length == 0) {
                            that.di_msg = true;
                        }
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: function () {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        }
    }
};
</script>
<style>
/* 全新色彩系统和页面基础样式 */
page {
    /* 清新色彩变量定义 */
    --primary-color: #00D4AA;
    --primary-light: #4ECDC4;
    --secondary-color: #FF6B9D;
    --accent-color: #4A90E2;
    --success-color: #00C896;
    --warning-color: #FFB800;

    /* 中性色系 */
    --white: #FFFFFF;
    --gray-50: #F8FAFC;
    --gray-100: #F1F5F9;
    --gray-200: #E2E8F0;
    --gray-300: #CBD5E1;
    --gray-400: #94A3B8;
    --gray-500: #64748B;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1E293B;
    --gray-900: #0F172A;

    /* 渐变色 */
    --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    --gradient-secondary: linear-gradient(135deg, var(--secondary-color), #FF8FB3);
    --gradient-accent: linear-gradient(135deg, var(--accent-color), #6BA3F5);

    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);

    /* 固定清新背景 */
    background: #F8FAFC;
    min-height: 100vh;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.page-container {
    background: transparent;
    padding-bottom: 60rpx;
    position: relative;
}
.header-title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12rpx;
}

.gift-emoji {
    font-size: 32rpx;
    animation: gentle-bounce 2s ease-in-out infinite;
}

@keyframes gentle-bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-4rpx); }
}

.title-text {
    color: var(--gray-800);
    font-weight: 700;
    font-size: 36rpx;
    letter-spacing: 0.5rpx;
}

/* 清新统计卡片 */
.stats-container {
    padding: 24rpx 32rpx;
    margin-top: 16rpx;
}

.stats-card {
    background: var(--white);
    border-radius: 32rpx;
    padding: 40rpx 32rpx;
    display: flex;
    justify-content: space-around;
    align-items: center;
    box-shadow: var(--shadow-lg);
    border: 1rpx solid var(--gray-100);
}

.stats-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
}

.stats-icon {
    width: 48rpx;
    height: 48rpx;
    border-radius: 16rpx;
    margin-bottom: 16rpx;
    position: relative;
}

.gift-count-icon {
    background: var(--gradient-primary);
}

.gift-count-icon::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 24rpx;
    height: 24rpx;
    background: var(--white);
    border-radius: 6rpx;
}

.friend-icon {
    background: var(--gradient-secondary);
}

.friend-icon::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 24rpx;
    height: 24rpx;
    background: var(--white);
    border-radius: 50%;
}

.stats-number {
    font-size: 48rpx;
    font-weight: 800;
    color: var(--gray-800);
    margin-bottom: 8rpx;
    line-height: 1;
}

.stats-label {
    font-size: 26rpx;
    color: var(--gray-500);
    font-weight: 500;
}

.stats-divider {
    width: 2rpx;
    height: 80rpx;
    background: var(--gray-200);
    margin: 0 24rpx;
}

/* 现代化礼物容器 */
.gifts-container {
    padding: 32rpx 24rpx;
}

.year-section {
    margin-bottom: 48rpx;
}

/* 简洁年份标签 */
.year-tag {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 32rpx;
    gap: 16rpx;
}

.year-line {
    flex: 1;
    height: 2rpx;
    background: var(--gray-200);
}

.year-badge {
    background: var(--white);
    border: 2rpx solid var(--primary-color);
    border-radius: 24rpx;
    padding: 12rpx 24rpx;
    box-shadow: var(--shadow-md);
}

.year-text {
    color: var(--primary-color);
    font-size: 28rpx;
    font-weight: 700;
    line-height: 1;
}

/* 礼物卡片列表 */
.gift-list {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
}

/* 现代礼物卡片 */
.gift-card {
    background: var(--white);
    border-radius: 24rpx;
    padding: 28rpx;
    position: relative;
    box-shadow: var(--shadow-md);
    border: 1rpx solid var(--gray-100);
    transition: all 0.3s ease;
    animation: slideInUp 0.5s ease-out both;
    overflow: hidden;
}

.gift-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3rpx;
    background: var(--gradient-primary);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(40rpx);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.gift-card:active {
    transform: translateY(2rpx);
    box-shadow: var(--shadow-sm);
}

/* 日期标签 */
.date-tag {
    display: inline-flex;
    align-items: center;
    background: var(--gradient-accent);
    color: var(--white);
    padding: 8rpx 16rpx;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    box-shadow: var(--shadow-sm);
}

.date-text {
    font-size: 22rpx;
    font-weight: 600;
    line-height: 1;
}

/* 用户信息区域 */
.user-section {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
}

.avatar-container {
    position: relative;
    margin-right: 16rpx;
}

.user-avatar {
    width: 64rpx;
    height: 64rpx;
    border-radius: 50%;
    background: var(--gray-100);
    box-shadow: var(--shadow-sm);
}

.avatar-border {
    position: absolute;
    top: -2rpx;
    left: -2rpx;
    right: -2rpx;
    bottom: -2rpx;
    border-radius: 50%;
    background: var(--gradient-primary);
    z-index: -1;
    opacity: 0.8;
}

.user-info {
    flex: 1;
}

.user-name {
    font-size: 28rpx;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: 4rpx;
    line-height: 1.2;
}

.action-text {
    font-size: 24rpx;
    color: var(--gray-500);
    font-weight: 500;
    line-height: 1.2;
}

/* 礼物信息区域 */
.gift-info {
    background: var(--gray-50);
    border-radius: 16rpx;
    padding: 20rpx;
    display: flex;
    align-items: center;
    gap: 12rpx;
    border: 1rpx solid var(--gray-200);
}

.gift-icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
}

.gift-box-icon {
    width: 32rpx;
    height: 32rpx;
    background: var(--gradient-secondary);
    border-radius: 8rpx;
    position: relative;
}

.gift-box-icon::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16rpx;
    height: 16rpx;
    background: var(--white);
    border-radius: 2rpx;
}

.gift-name {
    font-size: 26rpx;
    font-weight: 600;
    color: var(--gray-700);
    flex: 1;
    line-height: 1.3;
}

/* 简洁加载状态 */
.load-container {
    display: flex;
    justify-content: center;
    padding: 40rpx 32rpx;
}

.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16rpx;
}

.loading-spinner {
    width: 32rpx;
    height: 32rpx;
    border: 3rpx solid var(--gray-200);
    border-top: 3rpx solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 26rpx;
    color: var(--gray-500);
    font-weight: 500;
}

.completed-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12rpx;
    text-align: center;
}

.completed-icon {
    width: 48rpx;
    height: 48rpx;
    background: var(--gradient-primary);
    border-radius: 50%;
    position: relative;
    margin-bottom: 8rpx;
}

.completed-icon::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(45deg);
    width: 12rpx;
    height: 24rpx;
    border: 3rpx solid var(--white);
    border-top: none;
    border-left: none;
}

.completed-text {
    font-size: 28rpx;
    font-weight: 700;
    color: var(--gray-700);
    line-height: 1.3;
}

.completed-sub {
    font-size: 24rpx;
    color: var(--gray-500);
    font-weight: 500;
    line-height: 1.3;
}



/* 响应式优化 */
@media (max-width: 750rpx) {
    .stats-card {
        padding: 32rpx 24rpx;
    }

    .stats-number {
        font-size: 40rpx;
    }

    .gift-card {
        padding: 24rpx;
    }

    .user-name {
        font-size: 26rpx;
    }

    .gift-name {
        font-size: 24rpx;
    }
}
</style>

<template>
    <view>
        <view id="top_order"></view>
        <view
            :class="'flex justify-between align-center bg-white ' + (floorstatus ? 'animation-slide-right' : '') + ' ' + (floorstatus ? 'sticky' : '')"
            style="padding: 60rpx 40rpx 40rpx 40rpx; z-index: 100"
        >
            <view style="font-size: 14px; font-weight: 700">讨论列表</view>
            <view>
                <ui-tab @change="handleClickItem1" bg="bg-gray" :tab="['新发', '新回', '热门']" tpl="btn" ui="tab_home" mark="radius" inline />
            </view>
        </view>
    </view>
</template>

<script>
import uiTab from '@/yl_welore/colorui/ui-tab/ui-tab.vue';
export default {
    components: {
        uiTab
    },
    props: ['data', 'compName'],
    computed: {
        floorstatus() {
            return this.data && this.data.floorstatus;
        },
    },
    methods: {
        handleClickItem1(e) {
            this.$emit('handleClickItem1', e);
        }
    }
};
</script>
<style></style>

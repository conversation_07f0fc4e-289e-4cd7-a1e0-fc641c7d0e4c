<template>
    <view>
        <view class="page"></view>
        <cu-custom bgColor="transparent" :isSearch="false" :isBack="true">
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">观看历史</view>
        </cu-custom>
        <view class="flex align-center justify-end" style="padding: 0rpx 30rpx">
            <view>
                <button @tap="openDel" class="cu-btn round bg-orange shadow" style="font-size: 24rpx; height: 55rpx; line-height: 55rpx; padding: 0 20rpx">编辑</button>
            </view>
        </view>
        <view style="padding: 15rpx">
            <view @tap="openInfo" :data-index="index" class="flex align-center" style="margin-bottom: 40rpx; position: relative" v-for="(item, index) in list" :key="index">
                <view style="width: 200rpx">
                    <image :lazy-load="true" :src="item.info.poster_url" style="width: 200rpx; max-height: 350rpx; border-radius: 20rpx"></image>
                </view>

                <view style="margin-left: 20rpx">
                    <view>{{ item.info.title }}</view>
                    <view style="margin-top: 30rpx; font-size: 24rpx; color: #999999">全{{ item.info.total_episodes }}集</view>
                    <view style="margin-top: 30rpx; color: #e7cb4d">{{ item.msc }}</view>
                    <view class="text_num" style="margin-top: 30rpx; font-size: 26rpx; color: #999999">{{ item.info.plot_summary }}</view>
                    <view style="margin-top: 30rpx; font-size: 24rpx; color: #999999">观看至{{ item.info.msi_episode_number }}</view>
                </view>

                <view v-if="del" @tap.stop.prevent="delDo" :data-id="index" style="position: absolute; top: 20rpx; right: 20rpx">
                    <button class="cu-btn icon bg-red">
                        <text class="_icon-delete"></text>
                    </button>
                </view>
            </view>
        </view>
        <view :class="'cu-load ' + (!di_msg ? 'loading' : 'over')" style="padding-bottom: 100rpx"></view>
        <!-- <view style="position: fixed;bottom: 100rpx;width: 100%;text-align: center;">
    <button class="cu-btn icon shadow">
        <text class="_icon-add-round" style="font-size: 100rpx;color: orange;"></text>
  </button>
</view> -->
    </view>
</template>

<script>
import http from '../../../util/http.js';
const app = getApp();

export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            list: [],
            page: 1,
            di_msg: false,
            del: false,
            history: [],
            jj_list: []
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        //this.getMyLove();
        const res = uni.getStorageInfoSync();
        const msiItems = res.keys.filter((item) => item.startsWith('msi-'));
        var arr = [];
        msiItems.forEach(function (item) {
            var msi = uni.getStorageSync(item).value;
            arr.push(msi);
            //console.log(msi);
        });
        this.history = arr;
        this.getMyHistory();
    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {
        this.page = this.page + 1;
        this.getMyLove();
    },

    methods: {
        openInfo(d) {
            var info = this.list[d.currentTarget.dataset.index];
            console.log(info);
            const index = info.list.findIndex((item) => item.id == info.info.epis);
            console.log(index);
            uni.navigateTo({
                url: '/yl_welore/pages/packageC/theatre_video/index?id=' + info.info.id + '&index=' + index + '&epis=' + info.info.epis
            });
        },

        openDel() {
            this.del = !this.del;
        },

        delDo(d) {
            var index = d.currentTarget.dataset.id;
            var info = this.list[index];
            console.log(info);
            uni.removeStorageSync('msi-' + info.id + 'qtz');
            var list = this.list;
            list.splice(index, 1);
            this.list = list;
        },

        getMyHistory() {
            var b = app.globalData.api_root + 'Microseries/getMyHistory';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.openid = e.openid;
            params.list = JSON.stringify(this.history);
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.list.length < 10) {
                        this.di_msg = true;
                    }
                    this.list = res.data.list;
                },
                fail: function () {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        }
    }
};
</script>
<style>
.page {
    background-image: linear-gradient(to bottom, #ffedc7 0%, #fffdf9 50%, #fffdf8 100%);
    height: 100vh;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: -1;
}
</style>

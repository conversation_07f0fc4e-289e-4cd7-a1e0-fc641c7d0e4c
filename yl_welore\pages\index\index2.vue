<template>
    <view style="background-color: #f2f2f2; padding-top: 10px; padding-bottom: 80px; min-height: 1200rpx">
        <block v-for="(item, dataListindex) in new_list" :key="dataListindex">
            <view style="border-radius: 10px; margin: 10px; background-color: #fff; overflow: hidden; position: relative">
                <image
                    v-if="item.top_time"
                    :src="http_root + 'addons/yl_welore/web/static/mineIcon/index2/home_zhang.png'"
                    style="opacity: 0.8; width: 70px; height: 70px; position: absolute; left: 75%; top: 2%; z-index: 200"
                ></image>
                <view
                    @tap="home_url"
                    :data-index="dataListindex"
                    data-k="2"
                    :data-id="item.tory_id"
                    style="line-height: 90rpx; height: 90rpx; background-color: #fafbf9; color: #10b2fa; font-size: 15px; letter-spacing: 1px"
                >
                    <text class="cuIcon-discover lg text-gray" style="margin-left: 20px; color: #10b2fa; font-size: 18px; vertical-align: middle"></text>
                    <text style="margin-left: 5px; vertical-align: middle; font-size: 13px; font-weight: 300">
                        {{ item.realm_name }}
                    </text>
                </view>
                <view style="position: absolute; right: 7px; top: 20rpx">
                    <view>
                        <image
                            v-if="item.red == 1 && version == 0"
                            :src="http_root + 'addons/yl_welore/web/static/mineIcon/index2/fl.png'"
                            style="width: 50rpx; height: 50rpx; margin: 0px 5px"
                        ></image>
                        <image
                            v-if="item.study_type == 3"
                            :src="http_root + 'addons/yl_welore/web/static/mineIcon/index2/at.png'"
                            style="width: 50rpx; height: 50rpx; margin: 0px 5px"
                        ></image>
                        <image
                            v-if="item.is_buy == 1 && version == 0"
                            :src="http_root + 'addons/yl_welore/web/static/mineIcon/index2/ff.png'"
                            style="width: 50rpx; height: 50rpx; margin: 0px 5px"
                        ></image>
                    </view>
                </view>
                <view style="position: relative">
                    <view class="cu-list menu-avatar">
                        <view class="cu-item">
                            <view
                                @tap="home_url"
                                :data-index="dataListindex"
                                data-k="1"
                                :data-user_id="item.user_id"
                                class="cu-avatar round eight"
                                :style="'background-image:url(' + item.user_head_sculpture + ');'"
                            >
                                <view style="z-index: 100" :class="'cu-tag badge ' + (item.gender == 2 ? 'cuIcon-female bg-pink' : 'cuIcon-male bg-blue')"></view>
                                <image
                                    v-if="item.user_id != 0"
                                    class="now_level"
                                    style="height: 96rpx; width: 96rpx; position: absolute; max-width: initial"
                                    :src="item.avatar_frame"
                                ></image>
                            </view>
                            <view class="content flex-sub" style="left: 130rpx">
                                <view class="align-center">
                                    <view :class="item.user_id != 0 ? item.special : ''" style="font-size: 13px">
                                        {{ item.user_nick_name }}
                                    </view>
                                    <image v-if="item.attr != ''" class="now_level" style="height: 35rpx; width: 35rpx" :src="item.attr.attest.at_icon"></image>
                                    <image
                                        v-if="item.user_vip == 1 && item.user_id != 0"
                                        :src="http_root + 'addons/yl_welore/web/static/applet_icon/vip.png'"
                                        style="width: 30rpx; height: 30rpx; margin-left: 3px"
                                    ></image>
                                    <image
                                        v-if="item.user_id != 0"
                                        mode="heightFix"
                                        class="now_level"
                                        :data-index="dataListindex"
                                        :src="item.level"
                                        style="height: 13px; vertical-align: middle; margin-left: 3px"
                                    ></image>
                                    <image
                                        class="now_level"
                                        mode="heightFix"
                                        v-if="item.wear_merit && item.user_id != 0"
                                        :src="item.wear_merit"
                                        style="height: 13px; vertical-align: bottom; margin-left: 3px"
                                    ></image>
                                </view>
                                <view class="text-gray text-sm flex">
                                    <text
                                        v-if="item.check_qq == 'da' && item.user_id != 0"
                                        style="background-color: #9966ff; color: #fff; padding: 0px 4px; border-radius: 2px; font-size: 10px; margin-right: 5px"
                                    >
                                        {{ $state.diy.qq_name }}主
                                    </text>
                                    <text
                                        v-if="item.check_qq == 'xiao' && item.user_id != 0"
                                        style="background-color: #4facfe; color: #fff; padding: 0px 4px; border-radius: 2px; font-size: 10px; margin-right: 5px"
                                    >
                                        管理
                                    </text>
                                    <text v-if="!item.top_time && order_time == 'fatie'" style="font-size: 12px; color: #888888">
                                        {{ item.adapter_time }}
                                    </text>
                                    <text v-if="item.top_time" style="font-size: 12px; color: #888888">
                                        {{ item.adapter_time }}
                                    </text>
                                    <text v-if="!item.top_time && order_time == 'huifu' && item.huifu_time != null" style="font-size: 12px; color: #888888">
                                        回复于{{ item.huifu_time }}
                                    </text>
                                </view>
                            </view>
                        </view>
                    </view>
                    <!-- 内容 -->
                    <view>
                        <view style="padding: 0rpx 10px 10px 20px">
                            <view
                                @tap="home_url"
                                :data-index="dataListindex"
                                data-k="3"
                                :data-type="item.study_type"
                                :data-id="item.id"
                                :style="'word-break:break-all;position: relative;color:' + item.study_title_color + ';font-size:14px;padding: 10px 0px 0px 0px;'"
                            >
                                <block v-if="item.study_type == 0 || item.study_type == 1 || item.study_type == 2 || item.study_type == 3 || item.study_type == 6">
                                    <rich-text class="text_num" :nodes="item.study_title == '' ? item.study_content : item.study_title"></rich-text>
                                </block>
                            </view>
                        </view>
                        <view
                            @tap="home_url"
                            :data-index="dataListindex"
                            data-k="3"
                            :data-type="item.study_type"
                            :data-id="item.id"
                            v-if="item.study_type == 0 || item.study_type == 3 || item.study_type == 4 || item.study_type == 5"
                            style="overflow: hidden; padding: 0rpx 10px 10px 20px"
                        >
                            <!-- 1 -->
                            <view style="padding: 0px 15px" v-if="item.image_part.length == 1 && img != ''" v-for="(img, img_index) in item.image_part" :key="img_index">
                                <image :lazy-load="true" :src="img" style="border-radius: 3px; width: 100%; height: 190px" mode="aspectFill"></image>
                            </view>
                            <!-- 1 -->
                            <!-- 2 -->
                            <view
                                style="width: 50%; float: left; text-align: center"
                                v-if="item.image_part.length == 2"
                                v-for="(img, img_index) in item.image_part"
                                :key="img_index"
                            >
                                <image
                                    :lazy-load="true"
                                    v-if="img_index == 0"
                                    :src="img"
                                    style="border-radius: 3px 0px 0px 5px; height: 180px; width: 100%; padding-right: 2px"
                                    mode="aspectFill"
                                ></image>

                                <image
                                    :lazy-load="true"
                                    v-if="img_index == 1"
                                    :src="img"
                                    style="border-radius: 0px 5px 3px 0px; height: 180px; width: 100%; padding-left: 2px"
                                    mode="aspectFill"
                                ></image>
                            </view>
                            <!-- 2 -->
                            <!-- 3 -->
                            <view class="grid col-3 text-center">
                                <block v-if="item.image_part.length > 2" v-for="(img, img_index) in item.image_part" :key="img_index">
                                    <view style="text-align: center; padding-left: 8px" v-if="img_index == 0">
                                        <image :lazy-load="true" :src="img" style="border-radius: 3px 0px 0px 3px; width: 100%; height: 100px" mode="aspectFill"></image>
                                    </view>

                                    <view style="text-align: center; padding-left: 5px" v-if="img_index == 1">
                                        <image :lazy-load="true" :src="img" style="width: 100%; height: 100px" mode="aspectFill"></image>
                                    </view>

                                    <view style="text-align: center; padding-left: 5px; padding-right: 8px" v-if="img_index == 2">
                                        <image :lazy-load="true" :src="img" style="border-radius: 0px 3px 3px 0px; width: 100%; height: 100px" mode="aspectFill"></image>
                                    </view>
                                </block>
                            </view>
                            <!-- 投票 -->
                            <!-- 3 -->
                        </view>
                        <!-- 投票 -->
                        <view v-if="item.study_type == 4 || item.study_type == 5" class="shadow-warp" style="margin: 15px; background-color: #f8f8f8">
                            <view style="padding: 15px; text-align: center">
                                <view
                                    @tap.stop.prevent="home_url"
                                    :data-index="dataListindex"
                                    data-k="3"
                                    :data-type="item.study_type"
                                    :data-id="item.id"
                                    style="font-size: 15px; font-weight: 600"
                                >
                                    <text v-if="item.study_type == 4">（单选）</text>
                                    <text v-if="item.study_type == 5">（多选）</text>
                                    <rich-text class="text_num" v-if="item.study_title != ''" :nodes="item.study_title"></rich-text>
                                </view>
                                <view style="height: 10px"></view>
                                <view style="position: relative" v-if="vo_index < 3" v-for="(vo_item, vo_index) in item.vo" :key="vo_index">
                                    <view
                                        style="width: 95%; height: 40px; border-radius: 5px; line-height: 40px; margin: 5px auto"
                                        class="text_num bg-white"
                                        @tap.stop.prevent="dian_option"
                                        :data-id="vo_item.id"
                                        :data-key="dataListindex"
                                        :data-index="vo_index"
                                    >
                                        <view class="text-cut" style="z-index: 3; position: relative; width: 70%; margin: 0 auto">
                                            {{ vo_item.ballot_name }}
                                        </view>
                                        <text
                                            v-if="voi_item == vo_item.id"
                                            :style="'position: absolute;right: ' + (item.is_vo_check > 0 ? 90 : 7) + '%;z-index:3;top: 0;'"
                                            class="cuIcon-check lg text-green"
                                            v-for="(voi_item, index) in item.vo_id"
                                            :key="index"
                                        ></text>
                                        <text v-if="item.is_vo_check > 0" style="z-index: 3; position: absolute; right: 40rpx; top: 0">
                                            {{ vo_item.voters }}
                                        </text>
                                    </view>

                                    <view
                                        v-if="item.is_vo_check > 0"
                                        class="cu-progress radius sm"
                                        style="position: absolute; z-index: 1; left: 0; right: 0; top: 0; width: 95%; height: 40px; margin: 0 auto; background-color: #ffffff"
                                    >
                                        <view :style="'width:' + vo_item.ratio + '%;background-image: linear-gradient(120deg, #a1c4fd 0%, #c2e9fb 100%);'"></view>
                                    </view>
                                </view>
                                <view
                                    @tap.stop.prevent="home_url"
                                    :data-index="dataListindex"
                                    data-k="3"
                                    :data-type="item.study_type"
                                    :data-id="item.id"
                                    v-if="item.vo.length > 3"
                                    style="width: 95%; height: 40px; border-radius: 5px; line-height: 40px; margin: 5px auto"
                                    class="text_num bg-white"
                                >
                                    查看全部选项
                                    <text class="cuIcon-right lg text-gray"></text>
                                </view>
                            </view>
                            <view class="flex align-end" style="padding-bottom: 10px">
                                <view class="flex-sub">
                                    <view style="font-weight: 300; margin-left: 46rpx">参与人数：{{ item.vo_count }}</view>
                                </view>
                                <view class="flex-sub">
                                    <button
                                        @tap.stop.prevent="vote_do"
                                        :data-index="vo_index"
                                        :data-key="dataListindex"
                                        v-if="item.vo_id.length > 0 && item.is_vo_check == 0"
                                        style="font-weight: 300; float: right; margin-right: 46rpx"
                                        class="cu-btn bg-grey round sm"
                                    >
                                        投票
                                    </button>
                                </view>
                            </view>
                        </view>
                        <view
                            v-if="item.study_type == 1"
                            style="
                                margin: 0 auto;
                                overflow: hidden;
                                display: flex;
                                justify-content: space-between;
                                align-items: center;
                                height: 170rpx;
                                width: 90%;
                                background-color: #f6f7f7;
                                border: 1px solid #f0f0f0;
                                border-radius: 10rpx;
                            "
                        >
                            <view
                                :style="
                                    'background-image: url(' +
                                    item.user_head_sculpture +
                                    ');background-size: cover;background-position: center;width: 170rpx;background-color: #000;height: 170rpx;'
                                "
                            >
                                <view class="audioOpen" @tap="play" v-if="!item.is_voice" :data-key="dataListindex" :data-vo="item.study_voice">
                                    <text style="color: #ffffff; font-size: 15px" class="cicon-play-arrow"></text>
                                </view>
                                <view class="audioOpen" @tap="stop" v-if="item.is_voice" :data-key="dataListindex" :data-vo="item.study_voice">
                                    <text style="color: #ffffff; font-size: 15px" class="cicon-pause"></text>
                                </view>
                            </view>
                            <view style="width: 75%; padding: 20rpx">
                                <view style="display: flex; justify-content: space-between; align-items: center">
                                    <view style="font-size: 28rpx; color: #555555; font-weight: 600">{{ item.user_nick_name }}上传的音乐</view>
                                    <view class="times">{{ item.starttime }}</view>
                                </view>
                                <view style="display: flex; justify-content: space-between; align-items: center; margin-top: 20rpx">
                                    <view style="font-size: 24rpx; color: #999">{{ item.user_nick_name }}</view>
                                    <view>
                                        <slider
                                            style="width: 170rpx"
                                            @change="sliderChange"
                                            block-size="12px"
                                            step="1"
                                            :value="item.offset"
                                            :max="item.max"
                                            selected-color="#4c9dee"
                                        />
                                    </view>
                                </view>
                            </view>
                        </view>
                        <!-- <navigator url='/yl_welore/pages/packageA/article/index?id={{item.id}}&type={{item.study_type}}' hover-class="none"> -->
                        <view
                            @tap="home_url"
                            :data-index="dataListindex"
                            data-k="3"
                            :data-type="item.study_type"
                            :data-id="item.id"
                            style="overflow: hidden"
                            v-if="item.study_type == 1"
                        >
                            <!-- 1 -->
                            <view style="padding: 0px 15px" v-if="item.image_part.length == 1 && img != ''" v-for="(img, img_index) in item.image_part" :key="img_index">
                                <image :lazy-load="true" :src="img" style="border-radius: 5px; width: 100%; height: 190px" mode="aspectFill"></image>
                            </view>
                            <!-- 1 -->
                            <!-- 2 -->
                            <view
                                style="width: 50%; float: left; text-align: center"
                                v-if="item.image_part.length == 2"
                                v-for="(img, img_index) in item.image_part"
                                :key="img_index"
                            >
                                <image
                                    :lazy-load="true"
                                    v-if="img_index == 0"
                                    :src="img"
                                    style="border-radius: 5px 0px 0px 5px; height: 180px; width: 100%; padding-right: 2px"
                                    mode="aspectFill"
                                ></image>

                                <image
                                    :lazy-load="true"
                                    v-if="img_index == 1"
                                    :src="img"
                                    style="border-radius: 0px 5px 5px 0px; height: 180px; width: 100%; padding-left: 2px"
                                    mode="aspectFill"
                                ></image>
                            </view>
                            <!-- 2 -->
                            <!-- 3 -->
                            <block v-if="item.image_part.length > 2" v-for="(img, img_index) in item.image_part" :key="img_index">
                                <view style="width: 65%; float: left; text-align: center; padding-left: 8px" v-if="img_index == 0">
                                    <image :lazy-load="true" :src="img" style="border-radius: 5px 0px 0px 5px; width: 100%; height: 185px" mode="aspectFill"></image>
                                </view>

                                <view style="width: 35%; float: left; text-align: center; padding-left: 5px" v-if="img_index == 1">
                                    <image :lazy-load="true" :src="img" style="border-radius: 0px 5px 0px 0px; width: 100%; height: 91px" mode="aspectFill"></image>
                                </view>

                                <view style="width: 35%; float: left; text-align: center; padding-left: 5px" v-if="img_index == 2">
                                    <image :lazy-load="true" :src="img" style="border-radius: 0px 0px 5px 0px; width: 100%; height: 91px" mode="aspectFill"></image>
                                </view>
                            </block>
                            <!-- 3 -->
                        </view>
                        <!-- </navigator> -->
                        <view v-if="item.study_type == 2">
                            <view @tap="home_url" :data-index="dataListindex" data-k="3" :data-type="item.study_type" :data-id="item.id">
                                <view v-if="item.image_part.length > 0" class="grid flex-sub padding-lr col-1" style="position: relative">
                                    <image :src="item.image_part[0]" mode="aspectFill" style="height: 190px; margin: 0 auto; border-radius: 5px"></image>
                                    <text
                                        class="cuIcon-videofill lg text-white"
                                        style="z-index: 1; font-size: 40px; position: absolute; text-align: center; left: 44%; bottom: 37%"
                                    ></text>
                                </view>
                                <view
                                    v-if="item.image_part.length == null || item.image_part.length == 0"
                                    class="bg-black padding radius text-center shadow-blur"
                                    style="position: relative; margin: 0 auto; width: 80%; height: 180px; z-index: 100; overflow: hidden; border-radius: 5px; font-size: 16px"
                                >
                                    <text
                                        class="cuIcon-videofill lg text-white"
                                        style="z-index: 1; font-size: 40px; position: absolute; text-align: center; left: 44%; bottom: 37%"
                                    ></text>
                                </view>
                            </view>
                        </view>
                        <view
                            v-if="item.gambit_id"
                            @tap="gambit_list"
                            :data-id="item.gambit_id"
                            style="font-weight: 300; display: inline-block; background-color: #ededed; border-radius: 20px; padding: 2px 5px 2px 2px; font-size: 12px; margin: 15px"
                        >
                            <image
                                style="width: 15px; height: 15px; vertical-align: middle"
                                :src="http_root + 'addons/yl_welore/web/static/mineIcon/material/index_topic.png'"
                            ></image>
                            <text style="vertical-align: middle; margin-left: 5px; letter-spacing: 1px">
                                {{ item.gambit_name }}
                            </text>
                        </view>
                    </view>
                    <!-- 内容 -->
                    <!-- 位置 -->
                    <!-- <view bindtap='get_position' data-pos_name='{{item.address_name}}' data-latitude='{{item.address_latitude}}' data-longitude='{{item.address_longitude}}' style='padding-left:10px' wx:if="{{item.address_name!=null}}">
          <view class='course-name' style='margin-top:10px;float:left;'>
            <image src='/yl_welore/style/icon/index2/pos.png' style='margin-left:7px;vertical-align:middle;width:25px;height:25px;border-radius:50px;'></image>
            <text style='font-size:13px;padding-left:5px;vertical-align:middle;color:#999;'>{{item.address_name}}</text>
          </view>
        </view> -->
                    <!-- 位置 -->
                    <view style="clear: both; height: 0"></view>
                    <view class="grid col-3 text-center margin-bottom margin-top">
                        <view class="" @tap="parseEventDynamicCode($event, item.is_open == 0 ? 'check_share' : '')">
                            <button hover-class="none" :open-type="item.is_open == 0 ? '' : 'share'" :data-key="dataListindex">
                                <image
                                    :src="http_root + 'addons/yl_welore/web/static/mineIcon/index2/fenfen.png'"
                                    style="width: 40rpx; vertical-align: middle; height: 40rpx"
                                ></image>
                                <text class="index_nav_name" style="color: #999999; font-size: 12px; margin-left: 15rpx; vertical-align: middle">分享</text>
                            </button>
                        </view>
                        <view class="">
                            <button @tap="home_pl" :data-id="item.id" :data-key="dataListindex" hover-class="none">
                                <image
                                    :src="http_root + 'addons/yl_welore/web/static/mineIcon/index2/xiaoxi.png'"
                                    style="width: 40rpx; vertical-align: middle; height: 40rpx"
                                ></image>
                                <text class="index_nav_name" style="color: #999999; font-size: 12px; margin-left: 15rpx; vertical-align: middle">
                                    {{ item.study_repount }}
                                </text>
                            </button>
                        </view>
                        <view class="">
                            <button hover-class="none" @tap="parseEventDynamicCode($event, item.is_buy == 1 ? '' : 'add_zan')" :data-id="item.id" :data-key="dataListindex">
                                <image
                                    :animation="item.animationData_zan"
                                    v-if="item.is_info_zan == false"
                                    :src="http_root + 'addons/yl_welore/web/static/mineIcon/index2/zan_1.png'"
                                    style="width: 40rpx; height: 40rpx; vertical-align: middle"
                                ></image>
                                <image
                                    :animation="item.animationData_zan"
                                    v-if="item.is_info_zan == true"
                                    :src="http_root + 'addons/yl_welore/web/static/mineIcon/index2/zan.png'"
                                    style="width: 40rpx; height: 40rpx; vertical-align: middle"
                                ></image>
                                <text class="index_nav_name" style="color: #999999; font-size: 12px; margin-left: 15rpx; vertical-align: middle">
                                    {{ item.info_zan_count_this > 10000 ? item.info_zan_count : item.info_zan_count_this }}
                                </text>
                            </button>
                        </view>
                    </view>
                    <!-- <view class="weui-flex" style='padding-bottom:10px;padding-top:10px;'>

        </view> -->
                </view>
            </view>

            <view style="padding: 30rpx" v-if="dataListindex % ad_info.isolate == 0 && dataListindex != 0 && ad_info.adsper == 1">
                <ad :unit-id="ad_info.adunit_id"></ad>
            </view>
        </block>
        <view :class="'cu-load ' + (!di_msg ? 'loading' : 'over')"></view>
    </view>
</template>

<script>
export default {
    props: ['data', 'compName'],
    computed: {
        new_list() {
            return this.$parent.$data.new_list;
        },
        dataListindex() {
            return this.$parent.$data.dataListindex;
        },
        item() {
            return this.$parent.$data.item;
        },
        http_root() {
            return this.$parent.$data.http_root;
        },
        $state() {
            return this.$parent.$data.$state;
        },
        order_time() {
            return this.$parent.$data.order_time;
        },
        version() {
            return this.$parent.$data.version;
        },
        img() {
            return this.$parent.$data.img;
        },
        img_index() {
            return this.$parent.$data.img_index;
        },
        vo_index() {
            return this.$parent.$data.vo_index;
        },
        vo_item() {
            return this.$parent.$data.vo_item;
        },
        voi_item() {
            return this.$parent.$data.voi_item;
        },
        index() {
            return this.$parent.$data.index;
        },
        ad_info() {
            return this.$parent.$data.ad_info;
        },
        di_msg() {
            return this.$parent.$data.di_msg;
        }
    },
    methods: {
        home_url(e) {
            this.$emit('home-url', e);
        },
        gambit_list(e) {
            this.$emit('gambit-list', e);
        },
        dian_option(e) {
            this.$emit('dian-option', e);
        },
        vote_do(e) {
            this.$emit('vote-do', e);
        },
        play(e) {
            this.$emit('play', e);
        },
        stop(e) {
            this.$emit('stop', e);
        },
        sliderChange(e) {
            this.$emit('slider-change', e);
        },
        home_pl(e) {
            this.$emit('home-pl', e);
        },
        parseEventDynamicCode(e, type) {
            this.$emit('dynamic-code', e, type);
        }
    }
};
</script>
<style></style>

<template>
    <view>
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">回收站</view>
        </cu-custom>
        <scroll-view scroll-x class="bg-white nav tab-container">
            <view class="flex text-center">
                <view :class="'flex-sub tab-item ' + (current == 'tab1' ? 'text-orange cur active-tab' : '')" @tap="handleChange" data-key="tab1">
                    <text class="tab-emoji">🗑️</text>
                    <text class="tab-text">系统删帖</text>
                </view>
                <view :class="'flex-sub tab-item ' + (current == 'tab2' ? 'text-orange cur active-tab' : '')" @tap="handleChange" data-key="tab2">
                    <text class="tab-emoji">👑</text>
                    <text class="tab-text">{{ design.qq_name }}主删帖</text>
                </view>
                <view :class="'flex-sub tab-item ' + (current == 'tab3' ? 'text-orange cur active-tab' : '')" @tap="handleChange" data-key="tab3">
                    <text class="tab-emoji">🏠</text>
                    <text class="tab-text">楼主删帖</text>
                </view>
                <view :class="'flex-sub tab-item ' + (current == 'tab4' ? 'text-orange cur active-tab' : '')" @tap="handleChange" data-key="tab4">
                    <text class="tab-emoji">👤</text>
                    <text class="tab-text">自己删帖</text>
                </view>
                <view :class="'flex-sub tab-item ' + (current == 'tab5' ? 'text-orange cur active-tab' : '')" @tap="handleChange" data-key="tab5">
                    <text class="tab-emoji">↩️</text>
                    <text class="tab-text">系统打回</text>
                </view>
            </view>
        </scroll-view>

        <view style="padding: 0px 20rpx">
            <block v-for="(item, i_index) in info" :key="i_index">
                <view class="post-card" v-if="item.is_reply == 3">
                    <view class="card-content">
                        <view class="post-title">
                            <text class="title-emoji">📝</text>
                            <text class="title-text">{{ item.study_title == '' ? item.study_content : item.study_title }}</text>
                        </view>
                        <view class="reject-reason">
                            <text class="reason-emoji">⚠️</text>
                            <text class="reason-text">审核打回：{{ item.reject_reason }}</text>
                        </view>
                        <view class="post-info">
                            <view class="info-item">
                                <text class="info-emoji">🏷️</text>
                                <text class="info-text">{{ item.realm_name }}</text>
                            </view>
                            <view class="info-item">
                                <text class="info-emoji">🕒</text>
                                <text class="info-text">{{ item.prove_time }}</text>
                            </view>
                            <view v-if="item.is_complaint == 0" :data-key="i_index" @tap="user_mutter" class="action-btn restore-btn">
                                <text class="btn-emoji">🔄</text>
                                <text class="btn-text">申请恢复</text>
                            </view>
                            <view v-if="item.is_complaint == 1" class="action-btn appealed-btn">
                                <text class="btn-emoji">✅</text>
                                <text class="btn-text">已申诉</text>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="post-card" v-if="item.is_reply == 0">
                    <view class="card-content">
                        <view class="post-title">
                            <text class="title-emoji">📝</text>
                            <text class="title-text">{{ item.study_title == '' ? item.study_content : item.study_title }}</text>
                        </view>
                        <view class="delete-reason">
                            <text class="reason-emoji">❌</text>
                            <text class="reason-text">{{ item.whether_reason }}</text>
                        </view>
                        <block v-for="(is, index) in item.is_complaint_list" :key="index">
                            <view class="complaint-item">
                                <text class="complaint-emoji">💬</text>
                                <text class="complaint-text">申诉理由：{{ is.tale_content }}</text>
                            </view>
                            <view v-if="is.acceptance_status == 1" class="reply-item">
                                <text class="reply-emoji">💭</text>
                                <text class="reply-text">受理回复：{{ is.tale_instruct }}</text>
                            </view>
                        </block>
                        <view class="post-info">
                            <view class="info-item">
                                <text class="info-emoji">🏷️</text>
                                <text class="info-text">{{ item.realm_name }}</text>
                            </view>
                            <view class="info-item">
                                <text class="info-emoji">🕒</text>
                                <text class="info-text">{{ item.whetd_time }}</text>
                            </view>
                            <view v-if="item.is_complaint == 0" :data-key="i_index" @tap="user_mutter" class="action-btn restore-btn">
                                <text class="btn-emoji">🔄</text>
                                <text class="btn-text">申请恢复</text>
                            </view>
                            <view v-if="item.is_complaint == 1" class="action-btn appealed-btn">
                                <text class="btn-emoji">✅</text>
                                <text class="btn-text">已申诉</text>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="post-card" v-if="item.is_reply == 1">
                    <view class="card-content">
                        <view class="post-title">
                            <text class="title-emoji">📝</text>
                            <text class="title-text">帖子：{{ item.study_title == '' ? item.study_content : item.study_title }}</text>
                        </view>
                        <view v-if="item.reply_content" class="reply-content">
                            <text class="reply-emoji">💬</text>
                            <text class="reply-text">回复：{{ item.reply_content }}</text>
                        </view>
                        <block v-for="(is, index) in item.is_complaint_list" :key="index">
                            <view class="complaint-item">
                                <text class="complaint-emoji">💬</text>
                                <text class="complaint-text">申诉理由：{{ is.tale_content }}</text>
                            </view>
                            <view v-if="is.acceptance_status == 1" class="reply-item">
                                <text class="reply-emoji">💭</text>
                                <text class="reply-text">受理回复：{{ is.tale_instruct }}</text>
                            </view>
                        </block>
                        <view class="post-info">
                            <view class="delete-reason">
                                <text class="reason-emoji">❌</text>
                                <text class="reason-text">{{ item.whether_reason }}</text>
                            </view>
                            <view class="info-item">
                                <text class="info-emoji">🕒</text>
                                <text class="info-text">{{ item.apter_time }}</text>
                            </view>
                            <view v-if="item.is_complaint == 0" :data-key="i_index" @tap="user_mutter" class="action-btn restore-btn">
                                <text class="btn-emoji">🔄</text>
                                <text class="btn-text">申请恢复</text>
                            </view>
                            <view v-if="item.is_complaint == 1" class="action-btn appealed-btn">
                                <text class="btn-emoji">✅</text>
                                <text class="btn-text">已申诉</text>
                            </view>
                        </view>
                    </view>
                </view>
            </block>
        </view>

        <view :class="'cu-load ' + (info.length == 0 ? 'over' : '')"></view>

        <view :class="'cu-modal modal-container ' + (sc_msg ? 'show' : '')">
            <view class="cu-dialog modal-dialog">
                <view class="cu-bar bg-white justify-end modal-header">
                    <view class="content modal-title">
                        <text class="modal-emoji">📝</text>
                        <text class="modal-title-text">申诉理由</text>
                    </view>
                    <view class="action" @tap="hideModal">
                        <text class="cuIcon-close text-red close-icon"></text>
                    </view>
                </view>
                <view v-if="sc_msg" class="padding-sm modal-body">
                    <textarea @input="is_sc_text" class="modal-textarea" placeholder="📝 请详细填写申诉理由..." />
                </view>
                <view class="cu-bar bg-white justify-end modal-footer">
                    <view class="action">
                        <button class="modal-btn cancel-btn" @tap="hideModal">
                            <text class="btn-emoji">❌</text>
                            <text>取消</text>
                        </button>
                        <button class="modal-btn confirm-btn" @tap="do_user_mutter">
                            <text class="btn-emoji">✅</text>
                            <text>确定</text>
                        </button>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
var app = getApp();
var http = require('../../../util/http.js');
export default {
    data() {
        return {
            info: [],
            current: 'tab1',
            page: 1,
            design:{},
            sc_msg: false,
            id: '',
            sc_text: ''
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        var design = uni.getStorageSync('is_diy');
        this.design = design;
        //this.get_help_info();
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {
        //this.get_user_banned();
        this.get_user_paper_del();
    },
    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {
        var forward = app.globalData.forward;
        console.log(forward);
        if (forward) {
            return {
                title: forward.title,
                path: '/yl_welore/pages/index/index',
                imageUrl: forward.reis_img
            };
        } else {
            return {
                title: '您的好友给您发了一条信息',
                path: '/yl_welore/pages/index/index'
            };
        }
    },
    methods: {
        handleChange(detail) {
            this.current = detail.currentTarget.dataset.key;
            this.get_user_paper_del();
        },

        /**
         * 获取删帖
         */
        get_user_paper_del() {
            var b = app.globalData.api_root + 'User/get_user_paper_del';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            params.del_type = this.current;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        this.info = res.data.info;
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },

        /**
         * 申诉
         */
        user_mutter(e) {
            console.log(e);
            this.sc_msg = true;
            this.id = e.currentTarget.dataset.key;
        },

        /**
         *
         */
        is_sc_text(e) {
            this.sc_text = e.detail.value;
        },

        /**
         * 隐藏窗口
         */
        hideModal() {
            this.sc_msg = false;
        },

        /**
         * 申诉
         */
        do_user_mutter() {
            var b = app.globalData.api_root + 'User/do_paper_mutter';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            params.id = this.info[this.id]['id'];
            params.tory_id = this.info[this.id]['tory_id'];
            params.is_reply = this.info[this.id]['is_reply'];
            params.tale_content = this.sc_text;
            if(this.sc_text == ''){
                uni.showToast({
                    title: '请填写申诉理由！',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        this.sc_msg = false;
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                        //this.get_user_banned();
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },

        navbackFun() {
            uni.navigateBack();
        }
    }
};
</script>
<style>
page {
    background-color: #f8f9fa;
}

/* 标签页样式优化 */
.tab-container {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 0 0 20rpx 20rpx;
}

.tab-item {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 20rpx 10rpx !important;
    transition: all 0.3s ease;
    border-radius: 15rpx;
    margin: 10rpx 5rpx;
    position: relative;
    min-height: 120rpx;
}

.tab-item.active-tab {
    background: linear-gradient(135deg, #ff9700, #ed1c24) !important;
    color: white !important;
    transform: translateY(-2rpx);
    box-shadow: 0 4px 12px rgba(255, 151, 0, 0.3);
}

.tab-item.active-tab .tab-text {
    color: white !important;
}

.tab-emoji {
    font-size: 32rpx;
    margin-bottom: 8rpx;
    display: block !important;
}

.tab-text {
    font-size: 24rpx !important;
    font-weight: 500;
    text-align: center;
    line-height: 1.2;
    color: #333 !important;
    display: block !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120rpx;
}

/* 帖子卡片样式 */
.post-card {
    background: white;
    border-radius: 20rpx;
    margin: 20rpx 0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
}

.post-card:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.card-content {
    padding: 30rpx;
}

/* 标题样式 */
.post-title {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20rpx;
    padding-bottom: 20rpx;
    border-bottom: 1px solid #f5f5f5;
}

.title-emoji {
    font-size: 32rpx;
    margin-right: 15rpx;
    flex-shrink: 0;
    margin-top: 5rpx;
}

.title-text {
    font-size: 32rpx;
    font-weight: 600;
    color: #2c3e50;
    line-height: 1.4;
    flex: 1;
}

/* 原因样式 */
.reject-reason, .delete-reason {
    display: flex;
    align-items: flex-start;
    margin: 20rpx 0;
    padding: 20rpx;
    background: linear-gradient(135deg, #fff5f5, #ffe6e6);
    border-radius: 15rpx;
    border-left: 4px solid #e74c3c;
}

.reason-emoji {
    font-size: 28rpx;
    margin-right: 12rpx;
    flex-shrink: 0;
}

.reason-text {
    font-size: 28rpx;
    color: #e74c3c;
    line-height: 1.4;
    flex: 1;
}

/* 回复内容样式 */
.reply-content {
    display: flex;
    align-items: flex-start;
    margin: 20rpx 0;
    padding: 20rpx;
    background: linear-gradient(135deg, #f0f8ff, #e6f3ff);
    border-radius: 15rpx;
    border-left: 4px solid #3498db;
}

.reply-emoji {
    font-size: 28rpx;
    margin-right: 12rpx;
    flex-shrink: 0;
}

.reply-text {
    font-size: 28rpx;
    color: #3498db;
    line-height: 1.4;
    flex: 1;
}

/* 申诉项目样式 */
.complaint-item {
    display: flex;
    align-items: flex-start;
    margin: 15rpx 0;
    padding: 20rpx;
    background: linear-gradient(135deg, #fff9e6, #fff3cd);
    border-radius: 15rpx;
    border-left: 4px solid #f39c12;
}

.complaint-emoji {
    font-size: 28rpx;
    margin-right: 12rpx;
    flex-shrink: 0;
}

.complaint-text {
    font-size: 26rpx;
    color: #d68910;
    line-height: 1.4;
    flex: 1;
}

/* 回复项目样式 */
.reply-item {
    display: flex;
    align-items: flex-start;
    margin: 15rpx 0;
    padding: 20rpx;
    background: linear-gradient(135deg, #f0fff4, #d4edda);
    border-radius: 15rpx;
    border-left: 4px solid #27ae60;
}

.reply-item .reply-emoji {
    font-size: 28rpx;
    margin-right: 12rpx;
    flex-shrink: 0;
}

.reply-item .reply-text {
    font-size: 26rpx;
    color: #27ae60;
    line-height: 1.4;
    flex: 1;
}

/* 信息项样式 */
.post-info {
    margin-top: 25rpx;
    padding-top: 20rpx;
    border-top: 1px solid #f5f5f5;
}

.info-item {
    display: flex;
    align-items: center;
    margin: 15rpx 0;
}

.info-emoji {
    font-size: 26rpx;
    margin-right: 12rpx;
    flex-shrink: 0;
}

.info-text {
    font-size: 26rpx;
    color: #7f8c8d;
    line-height: 1.3;
}

/* 操作按钮样式 */
.action-btn {
    display: inline-flex;
    align-items: center;
    padding: 15rpx 25rpx;
    border-radius: 50rpx;
    margin: 15rpx 0;
    transition: all 0.3s ease;
    cursor: pointer;
    font-weight: 500;
}

.restore-btn {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.restore-btn:active {
    transform: translateY(1rpx);
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.4);
}

.appealed-btn {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
    box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
}

.btn-emoji {
    font-size: 24rpx;
    margin-right: 8rpx;
}

.btn-text {
    font-size: 26rpx;
}

/* 模态框样式优化 */
.modal-container {
    backdrop-filter: blur(10rpx);
}

.modal-dialog {
    border-radius: 25rpx;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    border: 1px solid #f0f0f0;
}

.modal-header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 25rpx 30rpx;
}

.modal-title {
    display: flex;
    align-items: center;
}

.modal-emoji {
    font-size: 32rpx;
    margin-right: 15rpx;
}

.modal-title-text {
    font-size: 32rpx;
    font-weight: 600;
    color: white;
}

.close-icon {
    font-size: 36rpx;
    color: white;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.close-icon:active {
    opacity: 1;
}

.modal-body {
    padding: 40rpx 30rpx;
    background: #fafbfc;
}

.modal-textarea {
    width: 100%;
    min-height: 200rpx;
    padding: 20rpx;
    border: 2px solid #e9ecef;
    border-radius: 15rpx;
    font-size: 28rpx;
    line-height: 1.5;
    background: white;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.modal-textarea:focus {
    border-color: #667eea;
    outline: none;
}

.modal-footer {
    padding: 25rpx 30rpx;
    background: white;
}

.modal-btn {
    display: inline-flex;
    align-items: center;
    border-radius: 50rpx;
    padding: 20rpx 35rpx;
    font-weight: 500;
    transition: all 0.3s ease;
    height: 80rpx;
    margin-left: 30rpx;
}

.cancel-btn {
    border: 2px solid #e74c3c;
    color: #e74c3c;
}

.cancel-btn:active {
    background: #e74c3c;
    color: white;
}

.confirm-btn {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    border: none;
    color: white;
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.confirm-btn:active {
    transform: translateY(1rpx);
    box-shadow: 0 2px 8px rgba(39, 174, 96, 0.4);
}

.modal-btn .btn-emoji {
    font-size: 24rpx;
    margin-right: 8rpx;
}

/* 加载状态优化 */
.cu-load.over::before {
    content: "🎉 已加载全部内容";
    color: #95a5a6;
    font-size: 28rpx;
}

/* 响应式优化 */
@media (max-width: 750rpx) {
    .tab-text {
        font-size: 22rpx !important;
        max-width: 100rpx;
    }

    .tab-emoji {
        font-size: 28rpx;
    }

    .tab-item {
        min-height: 100rpx;
        padding: 15rpx 8rpx !important;
    }

    .post-card {
        margin: 15rpx 0;
    }

    .card-content {
        padding: 25rpx;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30rpx);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.post-card {
    animation: fadeInUp 0.6s ease-out;
}

.tab-item {
    animation: fadeInUp 0.4s ease-out;
}
</style>

<template>
    <view class="container">
        <view class="pageH5">
            <web-view @message="bindmessage" :src="url"></web-view>
        </view>
    </view>
</template>

<script>
const app = getApp();
export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            url: '',
            title: '',
            height: ''
        }
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {},
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        uni.showShareMenu({
            menus: ['shareAppMessage', 'shareTimeline']
        });
        console.log(options);
        var kkk = decodeURIComponent(options.url);
        console.log(kkk);
        var c = kkk.indexOf('&', 0);
        if (c != -1) {
            var kkk = kkk.substring(0, c) + '?' + kkk.substring(c + 1);
        }
        this.url = kkk;
        this.height = app.globalData.height;
    },
    /**
     * 用户点击右上角分享
     */
    onShareAppMessage(options) {
        var url = this.url;
        console.log(url);
        return {
            title: this.title,
            path: '/yl_welore/pages/web/index?url=' + url
        };
    },
    methods: {
        bindmessage(e) {
            //接收web-view传递的参数
            console.log(e);
            if (e.detail.data[e.detail.data - 1].title) {
                this.title = e.detail.data[0].title;
            }
        }
    }
}
</script>
<style>
.nav-wrap {
    position: fixed;
    width: 100%;
    top: 0;
    background: #fff;
    color: #000;
    z-index: 9999999;
}

/* 标题要居中 */
.nav-title {
    position: absolute;
    text-align: center;
    max-width: 377rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    font-size: 36rpx;
    color: #2c2b2b;
    font-weight: 600;
}

.nav-capsule {
    display: flex;
    align-items: center;
    margin-left: 20rpx;
    width: 50rpx;
    justify-content: space-around;
    border-radius: 50%;
    margin-top: 54rpx;
    z-index: 999999999;
}

.navbar-v-line {
    width: 1px;
    height: 32rpx;
    background-color: #f3f3f3;
}

.back-pre {
    width: 32rpx;
    height: 32rpx;
    margin-top: 11rpx;
    margin-left: 0rpx;
}
</style>

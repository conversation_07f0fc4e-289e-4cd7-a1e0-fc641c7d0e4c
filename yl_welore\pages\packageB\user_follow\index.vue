<template>
    <view class="page-container">
        <cu-custom bgColor="none" :isSearch="false" :isBack="true">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">关注的人</view>
        </cu-custom>

        <!-- 统计信息区域 -->
        <view class="stats-container">
            <text class="stats-emoji">👥</text>
            <text class="stats-text">{{ uid != id ? '他/她' : '我' }}{{ nvabarData.title }}{{ num }}人</text>
        </view>

        <!-- 用户列表 -->
        <view class="user-list-container">
            <block v-for="(item, index) in info" :key="index">
                <view class="user-card" @tap="top_url" :data-id="item.id">
                    <view class="cu-list menu-avatar">
                        <view
                            :class="'cu-item user-item ' + (modalName == 'move-box-' + index ? 'move-cur' : '')"
                            @touchstart="ListTouchStartFun"
                            @touchmove="ListTouchMove"
                            @touchend="ListTouchEnd"
                            :data-target="'move-box-' + index"
                        >
                            <view  class="cu-avatar round lg avatar-enhanced" :style="'background-image:url(' + item.user_head_sculpture + ');'">
                                <view :class="'cu-tag badge gender-tag ' + (item.gender == 2 ? 'cuIcon-female bg-pink' : 'cuIcon-male bg-blue')"></view>
                            </view>
                            <view class="content content-enhanced">
                                <view class="username">{{ item.user_nick_name }}</view>
                                <view class="signature" v-if="item.autograph">
                                    <text class="signature-content">
                                        <text class="cuIcon-writefill signature-icon"></text>
                                        {{ item.autograph }}
                                    </text>
                                </view>
                            </view>
                            <view v-if="uid == id" class="move">
                                <view @tap="cancel" :data-id="item.id" :data-index="index" class="delete-btn">🗑️ 删除</view>
                            </view>
                        </view>
                    </view>
                </view>
            </block>
        </view>

        <view :class="'cu-load load-enhanced ' + (info.length == 0 ? 'over' : '')"></view>
    </view>
</template>

<script>
var app = getApp();
import http from '../../../util/http.js';
export default {
    data() {
        return {
            page: 1,
            info: [],
            id: '',
            uid: '',
            type: '',

            nvabarData: {
                title: ''
            },

            msg: '',
            num: '',
            ListTouchStart: '',
            ListTouchDirection: '',
            modalName: ''
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        var e = app.globalData.getCache('userinfo');
        this.id = options.id;
        this.uid = e.uid;
        this.type = options.type;
        this.page = 1;
        this.info = [];
        if (options.type == 1) {
            this.nvabarData = {
                title: '关注的' //导航栏 中间的标题
            };
            this.msg = '没有关注任何人';
        }
        if (options.type == 2) {
            this.nvabarData = {
                title: '粉丝' //导航栏 中间的标题
            };
            this.msg = '一个粉丝也没有';
        }
        this.get_follow_fansi();
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {},
    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {
        var forward = app.globalData.forward;
        console.log(forward);
        if (forward) {
            return {
                title: forward.title,
                path: '/yl_welore/pages/index/index',
                imageUrl: forward.reis_img
            };
        } else {
            return {
                title: '您的好友给您发了一条信息',
                path: '/yl_welore/pages/index/index'
            };
        }
    },
    /**
     * 加载下一页
     */
    onReachBottom() {
        this.page = this.page + 1;
        this.get_follow_fansi();
    },
    methods: {
        /**
         * 取消关注/关注
         */
        cancel(d) {
            var id = d.currentTarget.dataset.id;
            var index = d.currentTarget.dataset.index;
            var b = app.globalData.api_root + 'User/get_user_cancel';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = id;
            params.this_uid = e.uid;
            params.is_user = 1;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                        var lists = this.info;
                        lists.splice(index, 1);
                        this.info = lists;
                        this.num = this.num - 1;
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: function () {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },

        /**
         * 我的粉丝/我关注的人
         */
        get_follow_fansi() {
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = this.id;
            params.page = this.page;
            params.type = this.type;
            var b = app.globalData.api_root + 'User/get_follow_fansi';
            var allMsg = this.info;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        for (var i = 0; i < res.data.info.length; i++) {
                            allMsg.push(res.data.info[i]);
                        }
                        this.info = allMsg;
                        this.num = res.data.num;
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: function () {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },

        // ListTouch触摸开始
        ListTouchStartFun(e) {
            console.log(e);
            this.ListTouchStart = e.touches[0].pageX;
        },

        // ListTouch计算方向
        ListTouchMove(e) {
            //console.log(e.touches[0].pageX - this.ListTouchStart);
            //console.log(e.touches[0].pageX - this.ListTouchStart < -100);
            this.ListTouchDirection = e.touches[0].pageX - this.ListTouchStart < -100 ? 'left' : 'right';
            // this.setData({
            //   ListTouchDirection: e.touches[0].pageX - this.data.ListTouchStart > 0 ? 'right' : 'left'
            // })
        },

        // ListTouch计算滚动
        ListTouchEnd(e) {
            //console.log(e);
            if (this.ListTouchDirection == 'left') {
                this.modalName = e.currentTarget.dataset.target;
            } else {
                this.modalName = null;
            }
            this.ListTouchDirection = null;
        },

        top_url(d) {
            var id = d.currentTarget.dataset.id;
            uni.navigateTo({
                url: '/yl_welore/pages/packageB/my_home/index?id=' + id
            });
        }
    }
};
</script>
<style>
page {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

.page-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 统计信息区域样式 */
.stats-container {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    margin-bottom: 30rpx;
}

.stats-emoji {
    font-size: 20px;
    margin-right: 10px;
}

.stats-text {
    font-weight: 700;
    font-size: 16px;
    color: #2c2b2b;
}

/* 用户列表容器 */
.user-list-container {
    padding: 0 20rpx;
}

/* 用户卡片样式 */
.user-card {
    margin-bottom: 15px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.user-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.12);
}

/* 用户项目增强样式 */
.user-item {
    padding: 15px 20px !important;
    border-bottom: none !important;
}

/* 头像增强样式 */
.avatar-enhanced {
    border: 3px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 性别标签优化 */
.gender-tag {
    border: 2px solid rgba(255, 255, 255, 0.9);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* 内容区域增强 */
.content-enhanced {
    margin-left: 15px;
}

.username {
    color: #2c2b2b;
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 5px;
}

.signature {
    color: #666;
    font-size: 14px;
}

.signature-content {
    display: flex;
    align-items: center;
}

.signature-icon {
    margin-right: 5px;
    color: #999;
}

/* 删除按钮优化 */
.delete-btn {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
    transition: all 0.3s ease;
}

.delete-btn:active {
    transform: scale(0.95);
    box-shadow: 0 1px 4px rgba(255, 107, 107, 0.4);
}

/* 加载状态增强 */
.load-enhanced {
    margin: 20px 0;
    padding: 20px;
}

.load-enhanced.over::before {
    content: "🎉";
    color: #999;
    font-size: 14px;
}
</style>

<template>
    <view>
        <view v-if="rank.length > 0" class="flex justify-between" style="padding-left: 50rpx; margin-top: 10px">
            <view>
                <image :src="http_root + 'addons/yl_welore/web/static/applet_icon/task.png'" style="width: 45rpx; height: 45rpx; vertical-align: middle"></image>
                <text class="font-yl-2" style="font-size: 32rpx; color: #000000; font-weight: 600; margin-left: 20rpx">排行榜</text>
            </view>
        </view>
        <view v-if="rank.length > 0" style="background-color: #fff; border-radius: 5px; overflow: hidden">
            <scroll-view :scroll-x="true" style="padding: 15px; white-space: nowrap">
                <view v-for="(item, index) in rank" :key="index" @tap="open_ph" style="position: relative;;display: inline-block;margin-right: 30rpx;">
                    <view class="ph_txt">{{ item.ranking_name }}</view>
                    <image  :data-id="item.id" class="ph_img" :src="item.bg_img" style="width: 290rpx"></image>
                </view>
            </scroll-view>
        </view>
        <ui-tab bg="bg-white" :ui="!tab_show ? 'sticky' : ''" mark="text-yellow" scroll :tab="mod_list" tpl="long" curColor="bind_check" @change="mod_tab_change"></ui-tab>
        <view class="bg-white">
            <view class="flex justify-between align-center">
                <view class="">
                    <view style="margin: 30rpx 0px 20rpx 50rpx">
                        <image
                            :src="http_root + 'addons/yl_welore/web/static/mineIcon/circle3/ht.png'"
                            style="vertical-align: middle; height: 45rpx; width: 45rpx; display: inline-block"
                        ></image>
                        <text class="font-yl-2" style="font-size: 32rpx; font-weight: 600; color: #000000; margin-left: 20rpx">正在热聊</text>
                    </view>
                </view>
                <view style="margin-right: 20rpx">
                    <text :class="'cuIcon-title ' + (cardCur == kkk ? 'gamb_dian_dian' : 'gamb_dian')" v-for="(item, kkk) in gambit_list" :key="kkk"></text>
                </view>
            </view>

            <swiper
                @change="cardSwiper"
                class="screen-swiper square-dot"
                previous-margin="30rpx"
                next-margin="50rpx"
                :indicator-dots="' ' + false"
                :circular="true"
                :autoplay="false"
                duration="500"
                style="min-height: 460rpx"
            >
                <swiper-item :class="cardCur == index ? 'cur' : ''" style="padding: 0px" v-for="(item, index) in gambit_list" :key="index">
                    <view class="swiper-item" style="margin-right: 20rpx; padding: 5px">
                        <view class="bg-white" style="box-shadow: 0px 0px 10rpx 0px rgba(0, 0, 0, 0.1); height: 410rpx; width: 100%; border-radius: 7px">
                            <view class="flex justify-between" @tap="open_gambit" :data-id="dd.id" v-for="(dd, index1) in item" :key="index1">
                                <view class="padding-sm margin-xs radius">
                                    <view>
                                        <image
                                            :src="http_root + 'addons/yl_welore/web/static/mineIcon/circle3/bq.png'"
                                            style="width: 30rpx; height: 30rpx; display: inline-block; vertical-align: middle"
                                        ></image>
                                        <text class="font-yl-2" style="color: #000000; font-size: 15px; font-weight: 700; margin-left: 8px">{{ dd.gambit_name }}</text>
                                    </view>
                                    <view style="font-size: 12px; color: #9da0ad; padding-left: 25px; margin-top: 7rpx">{{ dd.page_count }}人参与讨论</view>
                                </view>

                                <view class="padding-sm margin-xs radius">
                                    <view class="cu-avatar-group" style="direction: unset">
                                        <view
                                            class="cu-avatar round"
                                            :style="'background-image:url(' + cc.user_head_sculpture + ');'"
                                            v-for="(cc, index2) in dd.img"
                                            :key="index2"
                                        ></view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </swiper-item>
            </swiper>
            <view style="padding-bottom: 80px; min-height: 40em">
                <block v-for="(item, dataListindex) in mod_my_list" :key="dataListindex">
                    <view style="border-radius: 10px; margin: 20rpx; overflow: hidden; position: relative; background-color: #f7f7f5; padding: 10rpx">
                        <view style="position: relative">
                            <view class="cu-list menu-avatar">
                                <view class="cu-item" style="background-color: #f7f7f5">
                                    <view
                                        @tap="home_url"
                                        data-k="1"
                                        :data-user_id="item.user_id"
                                        class="cu-avatar round index7"
                                        :style="'background-image:url(' + item.user_head_sculpture + ');'"
                                    >
                                        <block v-if="item.attr != ''">
                                            <image
                                                class="now_level"
                                                style="height: 30rpx; width: 30rpx; position: absolute; right: -1px; bottom: -3px; z-index: 100; max-width: initial"
                                                :src="item.attr.attest.at_icon"
                                            ></image>
                                        </block>
                                        <image
                                            v-if="item.user_id != 0"
                                            class="now_level"
                                            style="height: 96rpx; width: 96rpx; position: absolute; max-width: initial"
                                            :src="item.avatar_frame"
                                        ></image>
                                    </view>
                                    <view class="content flex-sub" style="left: 160rpx; margin-left: 0px; margin-bottom: 0px">
                                        <view class="align-center">
                                            <view :class="item.user_id != 0 ? item.special : ''" style="font-size: 15px">{{ item.user_nick_name }}</view>
                                            <image
                                                v-if="item.user_vip == 1 && item.user_id != 0"
                                                :src="http_root + 'addons/yl_welore/web/static/applet_icon/vip.png'"
                                                style="width: 30rpx; height: 30rpx; margin-left: 3px"
                                            ></image>
                                            <image
                                                v-if="item.user_id != 0"
                                                mode="heightFix"
                                                class="now_level"
                                                :data-index="dataListindex"
                                                :src="item.level"
                                                style="height: 13px; vertical-align: middle; margin-left: 3px"
                                            ></image>
                                            <image
                                                class="now_level"
                                                mode="heightFix"
                                                v-if="item.wear_merit && item.user_id != 0"
                                                :src="item.wear_merit"
                                                style="height: 13px; vertical-align: bottom; margin-left: 3px"
                                            ></image>
                                        </view>
                                        <view class="text-gray text-sm flex">
                                            <text style="font-size: 12px; color: #888888">{{ item.adapter_time }}</text>
                                        </view>
                                    </view>
                                </view>
                            </view>

                            <!-- 内容 -->
                            <view>
                                <view style="padding: 0rpx 10px 10px 15px">
                                    <view
                                        @tap="home_url"
                                        :data-index="dataListindex"
                                        data-k="3"
                                        :data-type="item.study_type"
                                        :data-id="item.id"
                                        class="text_num"
                                        :style="'word-break:break-all;position: relative;color:' + item.study_title_color + ';font-size:14px;padding: 10px 0px 0px 0px;'"
                                    >
                                        <block v-if="item.study_type == 0 || item.study_type == 1 || item.study_type == 2 || item.study_type == 3">
                                            <rich-text :nodes="item.study_title == '' ? item.study_content : item.study_title"></rich-text>
                                        </block>
                                    </view>
                                </view>
                                <view v-if="item.study_type == 0 || item.study_type == 3 || item.study_type == 4 || item.study_type == 5" style="overflow: hidden">
                                    <!-- 1 -->
                                    <view style="padding: 5px" v-if="item.image_part.length == 1 && img != ''" v-for="(img, img_index) in item.image_part" :key="img_index">
                                        <image
                                            @tap="prev_img"
                                            :data-key="dataListindex"
                                            :data-src="img"
                                            :lazy-load="true"
                                            :src="img"
                                            style="border-radius: 10px; width: 70%;"
                                            mode="widthFix"
                                        ></image>
                                    </view>
                                    <!-- 1 -->
                                    <!-- 2 -->
                                    <view class="grid col-2 text-center">
                                        <block v-if="item.image_part.length == 2" v-for="(img, img_index) in item.image_part" :key="img_index">
                                            <view style="text-align: center; padding: 2px" v-if="img_index == 0">
                                                <image
                                                    @tap="prev_img"
                                                    :data-key="dataListindex"
                                                    :data-src="img"
                                                    :lazy-load="true"
                                                    :src="img"
                                                    style="border-radius: 10px; width: 100%; height: 440rpx"
                                                    mode="aspectFill"
                                                ></image>
                                            </view>

                                            <view style="text-align: center; padding: 2px" v-if="img_index == 1">
                                                <image
                                                    @tap="prev_img"
                                                    :data-key="dataListindex"
                                                    :data-src="img"
                                                    :lazy-load="true"
                                                    :src="img"
                                                    style="border-radius: 10px; width: 100%; height: 440rpx"
                                                    mode="aspectFill"
                                                ></image>
                                            </view>
                                        </block>
                                    </view>
                                    <!-- 2 -->
                                    <!-- 3 -->
                                    <view class="grid col-3 text-center">
                                        <block v-if="item.image_part.length == 3" v-for="(img, img_index) in item.image_part" :key="img_index">
                                            <view style="text-align: center; padding: 2px" v-if="img_index < 3">
                                                <image
                                                    @tap="prev_img"
                                                    :data-key="dataListindex"
                                                    :data-src="img"
                                                    :lazy-load="true"
                                                    :src="img"
                                                    style="border-radius: 10px; width: 100%; height: 200rpx"
                                                    mode="aspectFill"
                                                ></image>
                                            </view>
                                        </block>
                                    </view>
                                    <!-- 3 -->
                                    <!-- 4 -->
                                    <view class="grid col-2 text-center">
                                        <block v-if="item.image_part.length >= 4 && item.image_part.length < 9" v-for="(img, img_index) in item.image_part" :key="img_index">
                                            <view style="text-align: center; padding: 0px 2px" v-if="img_index < 4">
                                                <image
                                                    @tap="prev_img"
                                                    :data-key="dataListindex"
                                                    :data-src="img"
                                                    :lazy-load="true"
                                                    :src="img"
                                                    style="border-radius: 10px; width: 100%; height: 336rpx"
                                                    mode="aspectFill"
                                                ></image>
                                            </view>
                                        </block>
                                    </view>
                                    <!-- 4 -->
                                    <!-- 4 -->
                                    <view class="grid col-3 text-center">
                                        <block v-if="item.image_part.length >= 9" v-for="(img, img_index) in item.image_part" :key="img_index">
                                            <view style="text-align: center; padding: 0px 2px" v-if="img_index < 9">
                                                <image
                                                    @tap="prev_img"
                                                    :data-key="dataListindex"
                                                    :data-src="img"
                                                    :lazy-load="true"
                                                    :src="img"
                                                    style="border-radius: 10px; width: 100%; height: 220rpx"
                                                    mode="aspectFill"
                                                ></image>
                                            </view>
                                        </block>
                                    </view>
                                    <!-- 4 -->
                                </view>
                                <!-- 投票 -->
                                <view v-if="item.study_type == 4 || item.study_type == 5" class="shadow-warp" style="margin: 15px; background-color: #f8f8f8">
                                    <view style="padding: 15px; text-align: center">
                                        <view
                                            @tap.stop.prevent="home_url"
                                            data-k="3"
                                            :data-index="dataListindex"
                                            :data-type="item.study_type"
                                            :data-id="item.id"
                                            class="text_num"
                                            style="font-size: 15px; font-weight: 600"
                                        >
                                            <text v-if="item.study_type == 4">（单选）</text>
                                            <text v-if="item.study_type == 5">（多选）</text>
                                            <rich-text v-if="item.study_title != ''" :nodes="item.study_title"></rich-text>
                                        </view>
                                        <view style="height: 10px"></view>
                                        <view style="position: relative" v-if="vo_index < 3" v-for="(vo_item, vo_index) in item.vo" :key="vo_index">
                                            <view
                                                style="width: 95%; height: 40px; border-radius: 5px; line-height: 40px; margin: 5px auto"
                                                class="text_num bg-white"
                                                @tap.stop.prevent="dian_option"
                                                :data-id="vo_item.id"
                                                :data-key="dataListindex"
                                                :data-index="vo_index"
                                            >
                                                <view class="text-cut" style="z-index: 3; position: relative; width: 70%; margin: 0 auto">
                                                    {{ vo_item.ballot_name }}
                                                </view>

                                                <text
                                                    v-if="voi_item == vo_item.id"
                                                    :style="'position: absolute;right: ' + (item.is_vo_check > 0 ? 90 : 7) + '%;z-index:3;top: 0;'"
                                                    class="cuIcon-check lg text-green"
                                                    v-for="(voi_item, index) in item.vo_id"
                                                    :key="index"
                                                ></text>
                                                <text v-if="item.is_vo_check > 0" style="z-index: 3; position: absolute; right: 40rpx; top: 0">{{ vo_item.voters }}</text>
                                            </view>

                                            <view
                                                v-if="item.is_vo_check > 0"
                                                class="cu-progress radius sm"
                                                style="
                                                    position: absolute;
                                                    z-index: 1;
                                                    left: 0;
                                                    right: 0;
                                                    top: 0;
                                                    width: 95%;
                                                    height: 40px;
                                                    margin: 0 auto;
                                                    background-color: #ffffff;
                                                "
                                            >
                                                <view :style="'width:' + vo_item.ratio + '%;background-image: linear-gradient(120deg, #a1c4fd 0%, #c2e9fb 100%);'"></view>
                                            </view>
                                        </view>

                                        <view
                                            @tap.stop.prevent="home_url"
                                            data-k="3"
                                            :data-index="dataListindex"
                                            :data-type="item.study_type"
                                            :data-id="item.id"
                                            v-if="item.vo.length > 3"
                                            style="width: 95%; height: 40px; border-radius: 5px; line-height: 40px; margin: 5px auto"
                                            class="text_num bg-white"
                                        >
                                            查看全部选项
                                            <text class="cuIcon-right lg text-gray"></text>
                                        </view>
                                    </view>
                                    <view class="flex align-end" style="padding-bottom: 10px">
                                        <view class="flex-sub">
                                            <view style="font-weight: 300; margin-left: 46rpx">参与人数：{{ item.vo_count }}</view>
                                        </view>
                                        <view class="flex-sub">
                                            <button
                                                @tap.stop.prevent="vote_do"
                                                :data-index="vo_index"
                                                :data-key="dataListindex"
                                                v-if="item.vo_id.length > 0 && item.is_vo_check == 0"
                                                style="font-weight: 300; float: right; margin-right: 46rpx"
                                                class="cu-btn bg-grey round sm"
                                            >
                                                投票
                                            </button>
                                        </view>
                                    </view>
                                </view>
                                <view class="weui-cell" v-if="item.study_type == 1">
                                    <view class="audiosBox">
                                        <view
                                            class="audioOpen"
                                            @tap="play"
                                            v-if="!item.is_voice"
                                            :data-vo="item.study_voice"
                                            :data-key="dataListindex"
                                            style="border: 2px solid #43b3f7"
                                        >
                                            <text style="color: #43b3f7; font-size: 25px" class="cicon-play-arrow"></text>
                                        </view>
                                        <view
                                            class="audioOpen"
                                            @tap="stop"
                                            v-if="item.is_voice"
                                            :data-vo="item.study_voice"
                                            :data-key="dataListindex"
                                            style="border: 2px solid #43b3f7"
                                        >
                                            <text style="color: #43b3f7; font-size: 26px" class="cicon-pause"></text>
                                        </view>
                                        <view class="slid">
                                            <slider
                                                style="width: 470rpx"
                                                @change="sliderChange"
                                                :data-key="dataListindex"
                                                block-size="12px"
                                                step="1"
                                                :value="item.offset"
                                                :max="item.max"
                                                selected-color="#43B3F7"
                                            />
                                            <view @tap="home_url" data-k="3" :data-index="dataListindex" :data-type="item.study_type" :data-id="item.id" style="margin-top: 3px">
                                                <text class="times" style="color: #43b3f7">{{ item.starttime }}</text>
                                                <!-- 进度时长 -->

                                                <text class="times" style="">{{ item.study_voice_time }}</text>
                                                <!-- 总时长 -->
                                            </view>
                                        </view>
                                    </view>
                                </view>
                                <!-- <navigator url='/yl_welore/pages/packageA/article/index?id={{item.id}}&type={{item.study_type}}' hover-class="none"> -->
                                <view
                                    @tap="home_url"
                                    data-k="3"
                                    :data-index="dataListindex"
                                    :data-type="item.study_type"
                                    :data-id="item.id"
                                    style="overflow: hidden"
                                    v-if="item.study_type == 1"
                                >
                                    <!-- 1 -->
                                    <view style="padding: 0px 15px" v-if="item.image_part.length == 1 && img != ''" v-for="(img, img_index) in item.image_part" :key="img_index">
                                        <image :lazy-load="true" :src="img" style="border-radius: 5px; width: 100%; height: 190px" mode="aspectFill"></image>
                                    </view>
                                    <!-- 1 -->
                                    <!-- 2 -->
                                    <view
                                        style="width: 50%; float: left; text-align: center"
                                        v-if="item.image_part.length == 2"
                                        v-for="(img, img_index) in item.image_part"
                                        :key="img_index"
                                    >
                                        <image
                                            :lazy-load="true"
                                            v-if="img_index == 0"
                                            :src="img"
                                            style="border-radius: 5px 0px 0px 5px; height: 180px; width: 100%; padding-right: 2px"
                                            mode="aspectFill"
                                        ></image>

                                        <image
                                            :lazy-load="true"
                                            v-if="img_index == 1"
                                            :src="img"
                                            style="border-radius: 0px 5px 5px 0px; height: 180px; width: 100%; padding-left: 2px"
                                            mode="aspectFill"
                                        ></image>
                                    </view>
                                    <!-- 2 -->
                                    <!-- 3 -->
                                    <block v-if="item.image_part.length > 2" v-for="(img, img_index) in item.image_part" :key="img_index">
                                        <view style="width: 65%; float: left; text-align: center; padding-left: 8px" v-if="img_index == 0">
                                            <image :lazy-load="true" :src="img" style="border-radius: 5px 0px 0px 5px; width: 100%; height: 185px" mode="aspectFill"></image>
                                        </view>

                                        <view style="width: 35%; float: left; text-align: center; padding-left: 5px" v-if="img_index == 1">
                                            <image :lazy-load="true" :src="img" style="border-radius: 0px 5px 0px 0px; width: 100%; height: 91px" mode="aspectFill"></image>
                                        </view>

                                        <view style="width: 35%; float: left; text-align: center; padding-left: 5px" v-if="img_index == 2">
                                            <image :lazy-load="true" :src="img" style="border-radius: 0px 0px 5px 0px; width: 100%; height: 91px" mode="aspectFill"></image>
                                        </view>
                                    </block>
                                    <!-- 3 -->
                                </view>
                                <!-- </navigator> -->
                                <view v-if="item.study_type == 2">
                                    <view @tap="home_url" data-k="3" :data-index="dataListindex" :data-type="item.study_type" :data-id="item.id">
                                        <view v-if="item.image_part.length > 0" class="grid flex-sub padding-lr col-1" style="position: relative">
                                            <image :src="item.image_part[0]" mode="aspectFill" style="height: 190px; margin: 0 auto; border-radius: 5px"></image>
                                            <text
                                                class="cuIcon-videofill lg text-white"
                                                style="z-index: 1; font-size: 40px; position: absolute; text-align: center; left: 44%; bottom: 37%"
                                            ></text>
                                        </view>

                                        <view
                                            v-if="item.image_part.length == null || item.image_part.length == 0"
                                            class="bg-black padding radius text-center shadow-blur"
                                            style="
                                                position: relative;
                                                margin: 0 auto;
                                                width: 80%;
                                                height: 180px;
                                                z-index: 100;
                                                overflow: hidden;
                                                border-radius: 5px;
                                                font-size: 16px;
                                            "
                                        >
                                            <text
                                                class="cuIcon-videofill lg text-white"
                                                style="z-index: 1; font-size: 40px; position: absolute; text-align: center; left: 44%; bottom: 37%"
                                            ></text>
                                        </view>
                                    </view>
                                </view>
                                <view
                                    v-if="item.gambit_id"
                                    @tap="gambit_list"
                                    :data-id="item.gambit_id"
                                    style="
                                        font-weight: 300;
                                        display: inline-block;
                                        background-color: #ededed;
                                        border-radius: 20px;
                                        padding: 2px 5px 2px 2px;
                                        font-size: 12px;
                                        margin: 15px;
                                    "
                                >
                                    <image
                                        style="width: 15px; height: 15px; vertical-align: middle"
                                        :src="http_root + 'addons/yl_welore/web/static/mineIcon/material/index_topic.png'"
                                    ></image>
                                    <text style="vertical-align: middle; margin-left: 5px; letter-spacing: 1px">{{ item.gambit_name }}</text>
                                </view>
                            </view>
                            <!-- 内容 -->
                            <view style="clear: both; height: 0"></view>

                            <view class="margin-sm">
                                <!-- <view class="margin-tb" bindtap="home_url" data-k="2" data-id="{{item.tory_id}}">
              <image src='{{http_root}}addons/yl_welore/web/static/mineIcon/index7/quan.png' style="width: 50rpx;vertical-align:middle;height:50rpx;"></image>
              <text style="font-size: 12px;color: #707070;margin-left: 5px;">{{item.realm_name}}</text>
            </view> -->
                                <view @tap="home_url" data-k="2" :data-id="item.tory_id" class="margin-tb">
                                    <image
                                        style="width: 30rpx; height: 30rpx; vertical-align: middle"
                                        :src="http_root + 'addons/yl_welore/web/static/mineIcon/material/index_topic.png'"
                                    ></image>
                                    <text style="vertical-align: middle; margin-left: 5px; letter-spacing: 1px; font-size: 12px; color: #707070; margin-left: 5px">
                                        {{ item.realm_name }}
                                    </text>
                                </view>
                                <view class="flex justify-star">
                                    <view @tap="parseEventDynamicCode($event, item.is_buy == 1 ? '' : 'add_zan')" :data-id="item.id" :data-key="dataListindex">
                                        <image
                                            :animation="item.animationData_zan"
                                            v-if="item.is_info_zan == false"
                                            :src="http_root + 'addons/yl_welore/web/static/mineIcon/index7/zan.png'"
                                            style="width: 40rpx; height: 40rpx; vertical-align: middle"
                                        ></image>
                                        <image
                                            :animation="item.animationData_zan"
                                            v-if="item.is_info_zan == true"
                                            :src="http_root + 'addons/yl_welore/web/static/mineIcon/index7/zan_1.png'"
                                            style="width: 40rpx; height: 40rpx; vertical-align: middle"
                                        ></image>
                                        <text class="index_nav_name" style="color: #707070; font-size: 13px; margin-left: 15rpx; vertical-align: middle">
                                            {{ item.is_info_zan == true ? '已赞' : '点赞' }}
                                        </text>
                                    </view>
                                    <view class="margin-left-xl" @tap="home_url" :data-index="dataListindex" data-k="3" :data-type="item.study_type" :data-id="item.id">
                                        <image
                                            :src="http_root + 'addons/yl_welore/web/static/mineIcon/index7/ping.png'"
                                            style="width: 35rpx; vertical-align: middle; height: 35rpx"
                                        ></image>
                                        <text class="index_nav_name" style="color: #707070; font-size: 13px; margin-left: 15rpx; vertical-align: middle">评论</text>
                                    </view>
                                    <view class="margin-left-xl">
                                        <button hover-class="none" open-type="share" :data-key="dataListindex">
                                            <image
                                                :src="http_root + 'addons/yl_welore/web/static/mineIcon/index7/zhuan.png'"
                                                style="width: 40rpx; vertical-align: middle; height: 40rpx"
                                            ></image>
                                            <text class="index_nav_name" style="color: #707070; font-size: 13px; margin-left: 15rpx; vertical-align: middle">转发</text>
                                        </button>
                                    </view>
                                </view>
                                <view
                                    @tap="home_url"
                                    :data-index="dataListindex"
                                    data-k="3"
                                    :data-type="item.study_type"
                                    :data-id="item.id"
                                    style="margin: 10px 0px 0px 0px; padding: 20rpx; background-color: #f3f4f6; border-radius: 3px"
                                    v-if="item.study_repount > 0"
                                >
                                    <view style="color: #777777; font-size: 12px">共{{ item.study_repount }}条评论</view>
                                    <block v-for="(r, index) in item.reply_list" :key="index">
                                        <view class="text_num" style="margin-top: 10px">
                                            <text style="font-weight: 600">{{ r.user_nick_name }}：</text>
                                            <rich-text style="display: inline-block" :nodes="r.reply_content"></rich-text>
                                            <image
                                                @tap.stop.prevent="previewHuiAndImage"
                                                v-if="r.image_part[0]"
                                                :src="r.image_part[0]"
                                                :data-src="r.image_part[0]"
                                                style="vertical-align: middle; height: 80rpx; width: 80rpx"
                                            ></image>
                                        </view>
                                    </block>
                                    <view class="text-blue" style="text-align: left; font-size: 12px; margin-top: 10px">进入查看更多评论</view>
                                </view>
                            </view>
                        </view>
                    </view>
                </block>
                <view :class="'cu-load ' + (!di_msg ? 'loading' : 'over')"></view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    props: ['data', 'compName'],
    computed: {
        rank(){
            return this.$parent.$data.rank;
        },
        http_root() {
            return this.$parent.$data.http_root;
        },
        tab_show() {
            return this.$parent.$data.tab_show;
        },
        mod_list() {
            return this.$parent.$data.mod_list;
        },
        cardCur() {
            return this.$parent.$data.cardCur;
        },
        gambit_list() {
            return this.$parent.$data.gambit_list;
        },
        mod_my_list() {
            return this.$parent.$data.mod_my_list;
        },
        di_msg() {
            return this.$parent.$data.di_msg;
        }
    },
    methods: {
        open_ph(e) {
            this.$emit('open_ph', e);
        },
        mod_tab_change(e) {
            this.$emit('mod_tab_change', e);
        },
        cardSwiper(e) {
            this.$emit('cardSwiper', e);
        },
        open_gambit(e) {
            this.$emit('open_gambit', e);
        },
        home_url(e) {
            this.$emit('home_url', e);
        },
        prev_img(e) {
            this.$emit('prev_img', e);
        },
        dian_option(e) {
            this.$emit('dian_option', e);
        },
        vote_do(e) {
            this.$emit('vote_do', e);
        },
        play(e) {
            this.$emit('play', e);
        },
        stop(e) {
            this.$emit('stop', e);
        },
        sliderChange(e) {
            this.$emit('sliderChange', e);
        },
        previewHuiAndImage(e) {
            uni.previewImage({
                current: e.currentTarget.dataset.src,
                urls: [e.currentTarget.dataset.src]
            });
        }
    }
};
</script>
<style></style>

<template>
    <view>
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
            <view slot="backText"></view>
            <view slot="content" style="color: #000000; font-weight: 600; font-size: 36rpx">{{ info.at_name }}</view>
        </cu-custom>
        <form>
            <block v-for="(item, c_index) in formItems" :key="c_index">
                <view class="cu-form-group" style="margin-top: 10px" v-if="item.dataType == 'text'">
                    <view class="title">
                        <text v-if="item.required == 'true'" style="color: red">*</text>
                        <text>{{ item.text }}</text>
                    </view>
                    <input @input="text_input" :data-id="c_index" :name="item.name" :placeholder="item.placeholder" />
                </view>

                <view class="cu-form-group align-start" style="margin-top: 10px" v-if="item.dataType == 'textarea'">
                    <view class="title">
                        <text v-if="item.required == 'true'" style="color: red">*</text>
                        <text>{{ item.text }}</text>
                    </view>
                    <textarea
                        @input="text_input"
                        :data-id="c_index"
                        :style="'height: ' + item.height + 'px;'"
                        maxlength="500"
                        :name="item.name"
                        :placeholder="item.placeholder"
                    ></textarea>
                </view>

                <view style="margin-top: 10px" v-if="item.dataType == 'radio'">
                    <view class="bg-white" style="padding: 15px">
                        <text v-if="item.required == 'true'" style="color: red">*</text>
                        <text>{{ item.text }}</text>
                    </view>
                    <radio-group style="display: block" @change="radioChange" :data-id="c_index">
                        <view class="cu-form-group" v-for="(c, index) in item.childItems" :key="index">
                            <view class="title" style="padding-left: 40rpx">{{ c.label }}</view>

                            <radio :value="c.value" class="radio" :checked="c.value == item.value ? true : false"></radio>
                        </view>
                    </radio-group>
                </view>

                <view style="margin-top: 10px" v-if="item.dataType == 'checkbox'">
                    <view class="bg-white" style="padding: 15px">
                        <text v-if="item.required == 'true'" style="color: red">*</text>
                        <text>{{ item.text }}</text>
                    </view>
                    <checkbox-group @change="checkboxChange" :data-id="c_index" style="display: block">
                        <view class="cu-form-group" v-for="(c, index) in item.childItems" :key="index">
                            <view class="title" style="padding-left: 40rpx">{{ c.label }}</view>

                            <checkbox :value="c.value"></checkbox>
                        </view>
                    </checkbox-group>
                </view>

                <view class="cu-form-group" style="margin-top: 10px" v-if="item.dataType == 'select'">
                    <view class="title">
                        <text v-if="item.required == 'true'" style="color: red">*</text>
                        <text>{{ item.text }}</text>
                    </view>
                    <picker mode="selector" @change="PickerChange" :data-id="c_index" :value="index" :range="item.childItems">
                        <view class="picker">
                            {{ item['value'] }}
                        </view>
                    </picker>
                </view>

                <block v-if="item.dataType == 'image'">
                    <view class="cu-bar bg-white margin-top">
                        <view class="action">
                            <text v-if="item.required == 'true'" style="color: red">*</text>
                            <text>{{ item.text }}</text>
                        </view>
                        <view class="action">{{ item.value.length }}/{{ item.count }}</view>
                    </view>
                    <view class="cu-form-group">
                        <view class="grid col-4 grid-square flex-sub">
                            <view class="bg-img" @tap="ViewImage" :data-id="c_index" :data-index="index" v-for="(vt, index) in item.value" :key="index">
                                <image :src="vt" mode="aspectFill"></image>

                                <view class="cu-tag bg-red" @tap.stop.prevent="DelImg" :data-id="c_index" :data-index="index">
                                    <text class="cuIcon-close"></text>
                                </view>
                            </view>
                            <view class="solids" @tap="chooseImage" :data-count="item.count" :data-id="c_index" v-if="item.value.length < item.count">
                                <text class="cuIcon-cameraadd"></text>
                            </view>
                        </view>
                    </view>
                </block>

                <view class="bg-white" style="text-align: center; padding: 40px 0px; margin-top: 5px" v-if="item.dataType == 'submit'">
                    <button @tap="submit" style="width: 90%" class="cu-btn lg bg-olive shadow">{{ item.text }}</button>
                </view>
            </block>
        </form>
    </view>
</template>

<script>
const app = getApp();
var http = require('../../../util/http.js');

export default {
    data() {
        return {
            info: {
                at_name: '',
                custom_form: [null, []]
            },
            id: 0
        }
    },
    computed: {
        formItems() {
            return this.info.custom_form && this.info.custom_form[1] ? this.info.custom_form[1] : [];
        }
    },
    methods: {
        checkboxChange(e) {
            this.$emit('checkboxChange', e);
            const index = e.target.dataset.id;
            const arr = e.detail.value;
            const value = arr.join('\n');
            this.$set(this.formItems[index], 'value', arr);
            this.$set(this.formItems[index], 'refrain', value);
        },
        radioChange(e) {
            this.$emit('radioChange', e);
            const index = e.target.dataset.id;
            const value = e.detail.value;
            this.$set(this.formItems[index], 'value', value);
        },
        text_input(e) {
            this.$emit('text_input', e);
            const index = e.currentTarget.dataset.id;
            const value = e.detail.value;
            this.$set(this.formItems[index], 'value', value);
        },
        PickerChange(e) {
            this.$emit('PickerChange', e);
            const index = e.target.dataset.id;
            const key = e.detail.value;
            const info = this.formItems[index];
            const value = info['childItems'][key];
            this.$set(this.formItems[index], 'value', value);
        },
        get_rz_from() {
            const b = app.globalData.api_root + 'Ranking/get_rz_from';
            const e = app.globalData.getCache('userinfo');
            const params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.id;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    this.info = res.data.info;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: () => {}
                    });
                }
            });
        },
        /**
         * 上传图片
         */
        chooseImage(item) {
            const index = item.currentTarget.dataset.id;
            console.log(index);
            const that = this;
            const e = app.globalData.getCache('userinfo');
            const b = app.globalData.api_root + 'User/img_upload';
            uni.chooseImage({
                count: parseInt(item.currentTarget.dataset.count),
                sizeType: ['original', 'compressed'],
                // 可以指定是原图还是压缩图，默认二者都有
                sourceType: ['album', 'camera'],
                // 可以指定来源是相册还是相机，默认二者都有
                success: (res) => {
                    console.log(res);
                    uni.showLoading({
                        title: '上传中...',
                        mask: true
                    });
                    let tempFilePaths = res.tempFilePaths;
                    for (let i = 0, h = tempFilePaths.length; i < h; i++) {
                        uni.uploadFile({
                            url: b,
                            filePath: tempFilePaths[i],
                            name: 'sngpic',
                            header: {
                                'content-type': 'multipart/form-data'
                            },
                            formData: {
                                'content-type': 'multipart/form-data',
                                token: e.token,
                                openid: e.openid,
                                much_id: app.globalData.siteInfo.uniacid
                            },
                            success: (res) => {
                                console.log(res);
                                if (res.data == '') {
                                    uni.hideLoading();
                                    uni.showModal({
                                        title: '提示',
                                        content: '内存溢出，请稍候重试'
                                    });
                                    return;
                                }
                                const data = JSON.parse(res.data);
                                console.log(data);
                                if (data.status == 'error') {
                                    uni.hideLoading();
                                    uni.showModal({
                                        title: '提示',
                                        content: data.msg
                                    });
                                    return;
                                } else {
                                    const list = that.info.custom_form[1][index].value;
                                    console.log(list);
                                    list.push(data.url);
                                    console.log(list);
                                    that.$set(that.info.custom_form[1][index], 'value', list);
                                    uni.hideLoading();
                                }
                            },
                            fail: (res) => {
                                uni.showModal({
                                    title: '提示',
                                    content: '上传错误！'
                                });
                            }
                        });
                    }
                }
            });
        },
        ViewImage(item) {
            const index = item.currentTarget.dataset.id;
            const img_index = item.currentTarget.dataset.index;
            uni.previewImage({
                urls: this.info['custom_form'][1][index]['value'],
                current: this.info['custom_form'][1][index]['value'][img_index]
            });
        },
        DelImg(item) {
            const index = item.currentTarget.dataset.id;
            const img_index = item.currentTarget.dataset.index;
            this.info['custom_form'][1][index]['value'].splice(img_index, 1);
            this.$forceUpdate();
        },
        submit() {
            uni.showLoading({
                title: '提交中...',
                mask: true
            });
            const info = this.info['custom_form'][1];
            const b = app.globalData.api_root + 'Ranking/set_user_attest';
            const that = this;
            const e = app.globalData.getCache('userinfo');
            const params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.id;
            params.info = JSON.stringify(info);
            http.POST(b, {
                params: params,
                success: (res) => {
                    uni.hideLoading();
                    if (res.data.status == 'error') {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            success(res) {}
                        });
                    } else {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            success(res) {
                                if (res.confirm) {
                                    that.get_subscribe();
                                } else if (res.cancel) {
                                    that.get_subscribe();
                                }
                            }
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },
        get_subscribe() {
            const subscribe = app.globalData.getCache('subscribe');
            if (subscribe && subscribe['YL0011'] && subscribe['YL0009'] && subscribe['YL0001']) {
                app.globalData.authorization(subscribe['YL0011'], subscribe['YL0009'], subscribe['YL0001'], (res) => {
                    setTimeout(() => {
                        uni.redirectTo({
                            url: '/yl_welore/pages/packageE/certification_list/index'
                        });
                    }, 1000);
                });
            } else {
                setTimeout(() => {
                    uni.redirectTo({
                        url: '/yl_welore/pages/packageE/certification_list/index'
                    });
                }, 1000);
            }
        }
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        uni.hideShareMenu();
        this.id = options.id;
        const subscribe = app.globalData.getCache('subscribe');
        if (!subscribe) {
            app.globalData.subscribe_message(
                (res) => {
                    //请求成功的回调函数
                    console.log(res);
                    if (res == '') {
                        return;
                    }
                    app.globalData.setCache('subscribe', res.parallelism_data);
                },
                () => {
                    //请求失败的回调函数，不需要时可省略
                }
            );
        }
        this.get_rz_from();
    }
};
</script>
<style>
.cu-form-group .title {
    min-width: calc(4em + 30rpx);
    font-size: 14px;
}
.cu-form-group + .cu-form-group {
    border-top: 1rpx solid #ffffff;
}
</style>

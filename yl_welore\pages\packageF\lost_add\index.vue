<template>
    <view class="page-container">
        <cu-custom bgColor="none" :isSearch="false" :isBack="true" style="color: #000000;">
            <view slot="backText">返回</view>
            <view slot="content" class="header-title">📝 发布失物招领</view>
        </cu-custom>
        <view class="form-container">
            <view class="modern-form-card">
                <view class="form-item">
                    <view class="form-label">
                        <text class="label-icon">📝</text>
                        <text class="label-text">发布类型</text>
                    </view>
                    <view class="form-content">
                        <radio-group class="radio-group" @change="radioType">
                            <view class="radio-container">
                                <label class="radio-item" :class="key == 0 ? 'active' : ''">
                                    <radio :checked="key == 0 ? true : false" value="0" class="custom-radio"></radio>
                                    <text class="radio-text">遗失</text>
                                </label>
                                <label class="radio-item" :class="key == 1 ? 'active' : ''">
                                    <radio :checked="key == 1 ? true : false" value="1" class="custom-radio"></radio>
                                    <text class="radio-text">捡拾</text>
                                </label>
                            </view>
                        </radio-group>
                    </view>
                </view>
                <view class="form-item">
                    <view class="form-label">
                        <text class="label-icon">📦</text>
                        <text class="label-text">物品类别</text>
                    </view>
                    <view class="form-content">
                        <picker @change="bindPickerType" :value="index" range-key="name" :range="type_list">
                            <view class="picker-container">
                                <text class="picker-text">{{ type_index == -1 ? '请选择物品类别' : type_list[type_index].name }}</text>
                                <text class="picker-arrow">▶</text>
                            </view>
                        </picker>
                    </view>
                </view>
                <view class="form-item">
                    <view class="form-label">
                        <text class="label-icon">⏰</text>
                        <text class="label-text">{{ key == 0 ? '遗失' : '拾取' }}时间</text>
                    </view>
                    <view class="form-content">
                        <pickerYMDHM data-key="1" @onPickerChange="onPickerChangeDo($event, { key: '1' })" startDate="2019-01-01" endDate="2099-01-01"></pickerYMDHM>
                    </view>
                </view>
                <view class="form-item">
                    <view class="form-label">
                        <text class="label-icon">🏷️</text>
                        <text class="label-text">物品名称</text>
                    </view>
                    <view class="form-content">
                        <input
                            @input="set_lost_name"
                            :value="lost_name"
                            class="modern-input"
                            placeholder="请输入物品名称"
                        />
                    </view>
                </view>
                <view class="form-item">
                    <view class="form-label">
                        <text class="label-icon">📍</text>
                        <text class="label-text">{{ key == 0 ? '遗失' : '拾取' }}地点</text>
                    </view>
                    <view class="form-content">
                        <input
                            @input="set_lost_address"
                            :value="lost_address"
                            class="modern-input"
                            :placeholder="'请输入' + (key == 0 ? '遗失' : '拾取') + '地点'"
                        />
                    </view>
                </view>
                <view class="form-item">
                    <view class="form-label">
                        <text class="label-icon">📞</text>
                        <text class="label-text">联系方式</text>
                    </view>
                    <view class="form-content">
                        <input
                            @input="set_lost_phone"
                            :value="lost_phone"
                            class="modern-input"
                            placeholder="手机号、微信、QQ等"
                        />
                    </view>
                </view>
                <view class="form-item form-item-vertical">
                    <view class="form-label">
                        <text class="label-icon">📄</text>
                        <text class="label-text">物品描述</text>
                    </view>
                    <view class="form-content">
                        <textarea
                            @input="set_lost_desc"
                            :value="lost_desc"
                            class="modern-textarea"
                            maxlength="200"
                            placeholder="请详细描述物品的特征、颜色、大小等信息"
                        ></textarea>
                    </view>
                </view>
                <view class="form-item form-item-vertical">
                    <view class="form-label">
                        <text class="label-icon">📷</text>
                        <text class="label-text">物品图片</text>
                    </view>
                    <view class="form-content">
                        <view class="image-upload-container">
                            <view class="image-item" @tap="ViewImage" :data-url="ImgArr[index]" v-for="(item, index) in ImgArr" :key="index">
                                <image :src="ImgArr[index]" mode="aspectFill" class="uploaded-image"></image>
                                <view class="delete-btn" @tap.stop.prevent="DelImg" :data-index="index">
                                    <text class="delete-icon">✕</text>
                                </view>
                            </view>
                            <view v-if="ImgArr.length < 3" @tap="previewImage" class="upload-btn">
                                <view class="upload-icon">📷</view>
                                <view class="upload-text">上传图片</view>
                            </view>
                        </view>
                    </view>
                </view>
                <view v-if="config.top_twig == 1" class="form-item" style="gap: 30rpx;">
                    <view class="form-label">
                        <text class="label-icon">⭐</text>
                        <text class="label-text">置顶设置</text>
                        <text class="label-desc">({{ config.top_price }}{{ config.price_type == 0 ? config.design.currency : config.design.confer }}/天)</text>
                    </view>
                    <view class="form-content">
                        <picker :value="index" :range="ToTop" @change="bindTopChange">
                            <view class="picker-container">
                                <text class="picker-text">{{ ToTop[TopIndex] }}</text>
                                <text class="picker-arrow">▶</text>
                            </view>
                        </picker>
                    </view>
                </view>
                <view v-if="config.top_twig == 1 && TopIndex != 0" class="cost-info-card">
                    <view class="cost-info">
                        <text class="cost-label">💰 需支付：</text>
                        <text class="cost-amount" :class="config.price_type == 2 ? 'text-price' : ''">{{ price }}</text>
                        <text class="cost-unit" v-if="config.price_type == 0 || config.price_type == 1">{{ config.price_type == 0 ? config.design.currency : config.design.confer }}</text>
                        <block v-if="config.price_type != 2">
                            <text class="cost-separator">|</text>
                            <text class="balance-label">我拥有：</text>
                            <text class="balance-amount" v-if="config.price_type == 0">{{ config.conch }}{{ config.design.currency }}</text>
                            <text class="balance-amount" v-if="config.price_type == 1">{{ config.fraction }}{{ config.design.confer }}</text>
                        </block>
                    </view>
                </view>

                <view class="rules-link" @tap="openUrl">
                    <text class="rules-icon">📋</text>
                    <text class="rules-text">《失物招领使用规则》</text>
                </view>

                <view class="submit-container">
                    <view @tap="submit" class="modern-submit-btn">
                        <text class="submit-icon">🚀</text>
                        <text class="submit-text">发布信息</text>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import pickerYMDHM from '@/yl_welore/util/pickerYMDHM/pickerYMDHM';
const app = getApp();
var http = require('../../../util/http.js');
var md5 = require('../../../util/md5.js');

export default {
    components: {
        pickerYMDHM
    },
    /**
     * 页面的初始数据
     */
    data() {
        return {
            config: {},
            key: 0,
            type_list: [],
            type_index: -1,
            lost_date: '',
            lost_name: '',
            lost_address: '',
            lost_desc: '',
            lost_phone: '',
            ImgArr: [],
            TopIndex: 0,
            ToTop: ['不设置', '1天', '2天', '3天', '4天', '5天', '6天', '7天', '8天', '9天', '10天'],
            price: 0,
            is_submit: false
        }
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        var lost = app.globalData.__PlugUnitScreen('27bc52a3b4dcfd099671fb09706f02d8');
        if (!lost) {
            uni.showToast({
                title: '未开通插件',
                icon: 'none',
                duration: 2000
            });
            setTimeout(() => {
                var pages = getCurrentPages();
                if (pages.length == 1) {
                    uni.reLaunch({
                        url: '/yl_welore/pages/index/index'
                    });
                    return;
                }
                uni.navigateBack();
            }, 1000);
            return;
        }
        this.key = options.key;
        this.getLostType();
        this.getLostConfig();
    },
    methods: {
        openUrl() {
            uni.navigateTo({
                url: '/yl_welore/pages/packageF/lost_desc/index'
            });
        },
        onPickerChangeDo(d) {
            this.lost_date = d.detail.dateString;
        },
        bindTopChange(d) {
            console.log(d);
            this.TopIndex = d.detail.value;
            this.price = (d.detail.value * this.config.top_price).toFixed(2);
        },
        getLostConfig() {
            var b = app.globalData.api_root + 'Lost/getLostConfig';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    that.config = res.data;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },
        submit() {
            if (this.lost_date == '') {
                uni.showToast({
                    title: '请选择时间',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }
            if (this.type_index == -1) {
                uni.showToast({
                    title: '请选择物品类别',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }
            if (this.lost_name == '') {
                uni.showToast({
                    title: '请填写物品名称',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }
            if (this.lost_address == '') {
                uni.showToast({
                    title: '请填写地点',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }
            if (this.lost_phone == '') {
                uni.showToast({
                    title: '请填写联系方式',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }
            if (this.lost_desc == '') {
                uni.showToast({
                    title: '请填写物品描述',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }
            uni.showLoading({
                title: '发布中...',
                mask: true
            });
            var b = app.globalData.api_root + 'Lost/InsLost';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.key = this.key;
            params.type = this.type_list[this.type_index].id;
            params.lost_date = this.lost_date;
            params.lost_name = this.lost_name;
            params.lost_address = this.lost_address;
            params.lost_desc = this.lost_desc;
            params.lost_phone = this.lost_phone;
            params.top_day = this.TopIndex;
            params.ImgArr = JSON.stringify(this.ImgArr);
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.code == 1) {
                        //that.Back();
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: (res) => {}
                        });
                        uni.hideLoading();
                    }
                    if (res.data.code == 0) {
                        that.Back();
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: (res) => {}
                        });
                        uni.hideLoading();
                    }
                    if (res.data.code == 2) {
                        that.pay_submit(res.data.item);
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },
        /**
         * 充值
         */
        pay_submit(item) {
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.lost_id = item.id;
            params.top_day = item.top_day;
            params.uid = e.id;
            var b = app.globalData.api_root + 'Pay/do_lost_pay';
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.return_msg == 'OK') {
                        var timeStamp = (Date.parse(new Date()) / 1000).toString();
                        var pkg = 'prepay_id=' + res.data.prepay_id;
                        var nonceStr = res.data.nonce_str;
                        var paySign = md5
                            .hexMD5(
                                'appId=' +
                                    res.data.appid +
                                    '&nonceStr=' +
                                    nonceStr +
                                    '&package=' +
                                    pkg +
                                    '&signType=MD5&timeStamp=' +
                                    timeStamp +
                                    '&key=' +
                                    res.data.app_info['app_key']
                            )
                            .toUpperCase(); //此处用到hexMD5插件
                        //发起支付
                        uni.requestPayment({
                            timeStamp: timeStamp,
                            nonceStr: nonceStr,
                            package: pkg,
                            signType: 'MD5',
                            paySign: paySign,
                            success: (res) => {
                                uni.showModal({
                                    title: '提示',
                                    content: '支付成功，请等待审核！',
                                    showCancel: false,
                                    success: (res) => {
                                        that.Back();
                                    }
                                });
                            },
                            complete: () => {
                                uni.hideLoading();
                                that.Back();
                            }
                        });
                    } else {
                        uni.showModal({
                            title: '提示',
                            content: '支付参数错误！',
                            showCancel: false,
                            success: (res) => {}
                        });
                        uni.hideLoading();
                        that.Back();
                    }
                },
                fail: () => {
                    that.is_submit = false;
                    uni.hideLoading();
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },
        DelImg(d) {
            var that = this;
            uni.showModal({
                title: '提示',
                content: '确定要删除这张图片吗？',
                cancelText: '取消',
                confirmText: '删除',
                success: (res) => {
                    if (res.confirm) {
                        that.ImgArr.splice(d.currentTarget.dataset.index, 1);
                        that.ImgArr = that.ImgArr;
                    }
                }
            });
        },
        ViewImage(e) {
            uni.previewImage({
                urls: this.ImgArr,
                current: e.currentTarget.dataset.url
            });
        },
        /**
         * 上传主图
         */
        previewImage() {
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var b = app.globalData.api_root + 'User/img_upload';
            uni.chooseMedia({
                count: 3,
                mediaType: ['image'],
                // 可以指定是原图还是压缩图，默认二者都有
                sourceType: ['album', 'camera'],
                // 可以指定来源是相册还是相机，默认二者都有
                sizeType: ['original', 'compressed'],
                success: (res) => {
                    uni.showLoading({
                        title: '上传中...',
                        mask: true
                    });
                    console.log(res);
                    var tempFilePaths = res.tempFiles;
                    for (var i = 0, h = tempFilePaths.length; i < h; i++) {
                        uni.uploadFile({
                            url: b,
                            filePath: tempFilePaths[i].tempFilePath,
                            name: 'sngpic',
                            header: {
                                'content-type': 'multipart/form-data'
                            },
                            formData: {
                                'content-type': 'multipart/form-data',
                                token: e.token,
                                openid: e.openid,
                                much_id: app.globalData.siteInfo.uniacid
                            },
                            success: (res) => {
                                console.log(res);
                                var data = JSON.parse(res.data);
                                console.log(data);
                                if (data.status == 'error') {
                                    uni.showToast({
                                        title: data.msg,
                                        icon: 'none',
                                        duration: 2000
                                    });
                                } else {
                                    that.ImgArr = that.ImgArr.concat(data.url);
                                    uni.hideLoading();
                                }
                            },
                            fail: (res) => {
                                uni.showToast({
                                    title: '上传错误！',
                                    icon: 'none',
                                    duration: 2000
                                });
                            }
                        });
                    }
                }
            });
        },
        set_lost_phone(d) {
            this.lost_phone = d.detail.value;
        },
        set_lost_desc(d) {
            this.lost_desc = d.detail.value;
        },
        set_lost_address(d) {
            this.lost_address = d.detail.value;
        },
        set_lost_name(d) {
            this.lost_name = d.detail.value;
        },
        bindDateChange(d) {
            console.log(d);
            this.lost_date = d.detail.value;
        },
        bindPickerType(d) {
            console.log(d);
            this.type_index = d.detail.value;
        },
        getLostType() {
            var b = app.globalData.api_root + 'Lost/getLostType';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            http.POST(b, {
                params: params,
                success: (res) => {
                    that.type_list = res.data;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },
        radioType(d) {
            this.key = d.detail.value;
        },
        /**
         * 返回上一页
         */
        Back(e) {
            //获取页面栈
            let pages = getCurrentPages();
            //获取所需页面
            let prevPage = pages[pages.length - 2]; //上一页
            prevPage.Refresh = true; //需要传过去的数据

            uni.navigateBack({
                delta: 1
            });
        }
    }
};
</script>
<style>
/* 页面整体样式 */
page {
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 50%, #f0f8ff 100%);
    min-height: 100vh;
}

.page-container {
    background: transparent;
}

/* 头部标题样式 */
.header-title {
    color: #000000;
    font-weight: 600;
    font-size: 36rpx;
}

/* 表单容器 */
.form-container {
    padding: 30rpx 20rpx;
}

.modern-form-card {
    background: #ffffff;
    border-radius: 32rpx;
    padding: 40rpx 30rpx;
    box-shadow: 0 20rpx 60rpx rgba(102, 126, 234, 0.15);
    position: relative;
    overflow: hidden;
}

.modern-form-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 8rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 表单项样式 */
.form-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 40rpx;
    position: relative;
}

.form-item:last-child {
    margin-bottom: 0;
}

.form-item-vertical {
    flex-direction: column;
    align-items: flex-start;
}

.form-label {
    display: flex;
    align-items: center;
    min-width: 200rpx;
}

.form-item-vertical .form-label {
    margin-bottom: 20rpx;
}

.label-icon {
    font-size: 32rpx;
    margin-right: 16rpx;
}

.label-text {
    font-size: 28rpx;
    font-weight: 600;
    color: #333333;
}

.label-desc {
    font-size: 22rpx;
    color: #999999;
    margin-left: 8rpx;
}

.form-content {
    flex: 1;
}

.form-item-vertical .form-content {
    width: 100%;
}

/* 单选按钮样式 */
.radio-group {
    width: 100%;
}

.radio-container {
    display: flex;
    gap: 30rpx;
}

.radio-item {
    display: flex;
    align-items: center;
    padding: 15rpx 30rpx;
    border-radius: 20rpx;
    background: #f8f9ff;
    border: 2rpx solid transparent;
    transition: all 0.3s ease;
    cursor: pointer;
    min-height: 80rpx;
}

.radio-item.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
}

.radio-item.active .radio-text {
    color: #ffffff;
}

.custom-radio {
    margin-right: 12rpx;
}

/* 自定义radio选中颜色 */
radio[checked] {
    color: #667eea !important;
}

.custom-radio .wx-radio-input.wx-radio-input-checked {
    background-color: #667eea !important;
    border-color: #667eea !important;
}

.custom-radio .wx-radio-input.wx-radio-input-checked::before {
    color: #ffffff !important;
}

/* uni-app radio 选中样式 */
.custom-radio .uni-radio-input.uni-radio-input-checked {
    background-color: #667eea !important;
    border-color: #667eea !important;
}

.custom-radio .uni-radio-input.uni-radio-input-checked::before {
    color: #ffffff !important;
}

.radio-text {
    font-size: 26rpx;
    font-weight: 500;
    color: #333333;
    transition: color 0.3s ease;
}

/* 选择器样式 */
.picker-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx 30rpx;
    background: #f8f9ff;
    border-radius: 20rpx;
    border: 2rpx solid #e8f4fd;
    transition: all 0.3s ease;
}

.picker-container:active {
    border-color: #667eea;
    background: #f0f8ff;
}

.picker-text {
    font-size: 26rpx;
    color: #333333;
    font-weight: 500;
}

.picker-arrow {
    font-size: 20rpx;
    color: #667eea;
    transform: rotate(0deg);
    transition: transform 0.3s ease;
}

/* 输入框样式 */
.modern-input {
    width: 100%;
    padding: 24rpx 30rpx;
    background: #f8f9ff;
    border: 2rpx solid #e8f4fd;
    border-radius: 20rpx;
    font-size: 26rpx;
    color: #333333;
    transition: all 0.3s ease;
    height: 86rpx;
}

.modern-input:focus {
    border-color: #667eea;
    background: #f0f8ff;
    box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

/* 文本域样式 */
.modern-textarea {
    width: 100%;
    min-height: 200rpx;
    padding: 24rpx 30rpx;
    background: #f8f9ff;
    border: 2rpx solid #e8f4fd;
    border-radius: 20rpx;
    font-size: 26rpx;
    color: #333333;
    line-height: 1.6;
    resize: none;
    transition: all 0.3s ease;
}

.modern-textarea:focus {
    border-color: #667eea;
    background: #f0f8ff;
    box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

/* 图片上传样式 */
.image-upload-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
}

.image-item {
    position: relative;
    width: 160rpx;
    height: 160rpx;
   
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.uploaded-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 20rpx;
}

.delete-btn {
    position: absolute;
    top: -8rpx;
    right: -8rpx;
    width: 40rpx;
    height: 40rpx;
    background: linear-gradient(135deg, #ff6b6b, #ff5252);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
    z-index: 10;
}

.delete-icon {
    font-size: 20rpx;
    color: #ffffff;
    font-weight: 700;
}

.upload-btn {
    width: 160rpx;
    height: 160rpx;
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
    border: 2rpx dashed #667eea;
    border-radius: 20rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.upload-btn:active {
    transform: scale(0.95);
    background: linear-gradient(135deg, #f0f8ff 0%, #e0f2ff 100%);
}

.upload-icon {
    font-size: 40rpx;
    margin-bottom: 8rpx;
}

.upload-text {
    font-size: 22rpx;
    color: #667eea;
    font-weight: 500;
}

/* 费用信息卡片 */
.cost-info-card {
    background: linear-gradient(135deg, #fff9e6 0%, #fff3d9 100%);
    border-radius: 20rpx;
    padding: 30rpx;
    margin: 30rpx 0;
    border-left: 6rpx solid #ffa726;
}

.cost-info {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 12rpx;
}

.cost-label {
    font-size: 26rpx;
    color: #333333;
    font-weight: 600;
}

.cost-amount {
    font-size: 32rpx;
    color: #ff6b35;
    font-weight: 700;
}

.cost-unit {
    font-size: 24rpx;
    color: #666666;
}

.cost-separator {
    font-size: 24rpx;
    color: #999999;
    margin: 0 8rpx;
}

.balance-label {
    font-size: 24rpx;
    color: #666666;
}

.balance-amount {
    font-size: 26rpx;
    color: #4caf50;
    font-weight: 600;
}

/* 规则链接 */
.rules-link {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30rpx;
    margin: 30rpx 0;
    background: linear-gradient(135deg, #e3f2fd 0%, #f0f8ff 100%);
    border-radius: 20rpx;
    transition: all 0.3s ease;
}

.rules-link:active {
    transform: scale(0.98);
    background: linear-gradient(135deg, #d1e7dd 0%, #e8f4fd 100%);
}

.rules-icon {
    font-size: 28rpx;
    margin-right: 12rpx;
}

.rules-text {
    font-size: 26rpx;
    color: #1976d2;
    font-weight: 500;
}

/* 提交按钮容器 */
.submit-container {
    margin-top: 50rpx;
    padding: 0 20rpx;
}

.modern-submit-btn {
    width: 100%;
    padding: 32rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 28rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 16rpx 40rpx rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.modern-submit-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.modern-submit-btn:active {
    transform: translateY(2rpx);
    box-shadow: 0 12rpx 32rpx rgba(102, 126, 234, 0.4);
}

.modern-submit-btn:active::before {
    left: 100%;
}

.submit-icon {
    font-size: 32rpx;
    margin-right: 16rpx;
}

.submit-text {
    font-size: 32rpx;
    color: #ffffff;
    font-weight: 700;
    letter-spacing: 2rpx;
}

/* 兼容原有样式 */
.ui-form-group {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    padding: 0.5em 30rpx;
}

.ui-form-group .ui-form-title {
    text-align: justify;
    font-size: 1.1em;
    position: relative;
    padding-left: 0;
    display: flex;
    align-items: center;
    margin-right: 30rpx;
}

.ui-form-group .ui-form-content {
    flex: 1;
    border-radius: 10rpx;
    display: flex;
    align-items: center;
    min-height: 3em;
}

.border-bottom {
    border-bottom: 1px solid rgba(119, 119, 119, 0.075);
}

.left_text {
    margin: 10px 0px 20px 0px;
}
</style>

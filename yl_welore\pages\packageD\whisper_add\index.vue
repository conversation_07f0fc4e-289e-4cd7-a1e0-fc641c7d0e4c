<template>
    <view>
        <view class="page">
            <view style="width: 100%; margin: 0px auto">
                <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
                    <view slot="backText"></view>
                    <view slot="content" style="color: #000000; font-weight: 600; font-size: 36rpx">发布</view>
                </cu-custom>
            </view>
        </view>
        <form>
            <view class="bg-white" style="margin-top: 5px">
                <textarea
                    :focus="my_focus"
                    :value="get_text"
                    style="border-radius: 3px; width: 100%; padding: 10px 30px 10px 15px; background-color: #fff; height: 150px"
                    class="weui-textarea"
                    maxlength="-1"
                    @input="get_reply_text"
                    placeholder="来吧，说出你的故事..."
                />
                <view class="flex justify-start align-center bg-white" style="padding: 0px 30rpx 15px">
                    <view>
                        <text @tap="openExMode" class="cicon-emoji-o" style="font-size: 25px"></text>
                        <text @tap="open_url" class="cicon-at-line" style="font-size: 25px; margin-left: 10px"></text>
                    </view>
                    <view v-if="user_info">
                        <text style="font-size: 14px; margin-left: 10px">{{ user_info.user_nick_name }}</text>
                        <text @tap="del_aite" class="cicon-close" style="font-size: 14px; margin-left: 10px"></text>
                    </view>
                </view>
            </view>
            <view class="cu-bar bg-white margin-top">
                <view class="action">
                    图片上传
                    <text style="font-size: 11px">（长按拖动排序）</text>
                </view>
                <view class="action">{{ img_arr.length }}/9</view>
            </view>
            <view class="cu-form-group">
                <view class="image-drag-container">
                    <movable-area class="movable-area" :style="'min-height:' + imageWitdh + 'px;height:' + areaHeight + 'px'">
                        <view class="image-choose-container">
                            <view
                                class="image-item"
                                :style="'width:' + imageWitdh + 'px;height:' + imageWitdh + 'px'"
                                :data-url="url"
                                :data-index="index"
                                @longpress="handleLongTap"
                                @touchend="handleTouchEnd"
                                @touchmove.stop.prevent="handleTouchMove"
                                v-for="(url, index) in img_arr"
                                :key="index"
                            >
                                <image :src="url" mode="aspectFit" @tap.stop.prevent="handlePreview" :data-index="index" :data-url="url"></image>

                                <view class="close" @tap.stop.prevent="handleDelete" :data-index="index" style="z-index: 100">X</view>
                            </view>
                            <view
                                class="add-button"
                                :style="'width:' + imageWitdh + 'px;height:' + imageWitdh + 'px'"
                                v-if="img_arr.length >= 0 && img_arr.length < 9"
                                @tap="handleChooseImage"
                            >
                                +
                            </view>
                            <view :style="'width:' + imageWitdh + 'px'" class="image-item image-item-temp" v-if="img_arr.length % 3 == 1"></view>
                        </view>
                        <movable-view
                            class="movable-view"
                            :style="'z-index: 10;width:' + imageWitdh + 'px;height:' + imageWitdh + 'px'"
                            v-if="!hidden"
                            :x="x - 80"
                            :y="y - 50"
                            direction="all"
                            :damping="5000"
                            :friction="1"
                        >
                            <image :src="currentImg" v-if="currentImg.length > 0"></image>
                        </movable-view>
                    </movable-area>
                </view>
            </view>
            <view style="text-align: center; margin-top: 50px">
                <button @tap="submit" :disabled="submit_dis" class="cu-btn round bg-yellow lg text-white" style="width: 70%; margin: 0 auto">发布</button>
            </view>
        </form>

        <!-- 小表情 -->
        <view :class="'cu-modal bottom-modal ' + (ExModal ? 'show' : '')" @tap="hideModal">
            <view class="cu-dialog" style="padding-bottom: 20px; background-color: #f3f3f3">
                <view style="width: 100%; height: 400rpx; background-color: #f3f3f3">
                    <swiper :indicator-dots="true" style="height: 400rpx">
                        <block v-for="(emojis, t_index) in expression" :key="t_index">
                            <swiper-item>
                                <view class="grid col-5 text-center" style="padding-left: 7px; padding-top: 10px; margin-bottom: 60rpx">
                                    <view
                                        @tap.stop.prevent="set_emoji"
                                        :data-key="t_index"
                                        :data-index="n_index"
                                        style="width: 60rpx; height: 60rpx; margin: 5px"
                                        v-for="(n_item, n_index) in emojis"
                                        :key="n_index"
                                    >
                                        <image :src="http_root + 'addons/yl_welore/web/static/expression/' + n_item" style="width: 60rpx; height: 60rpx"></image>
                                    </view>
                                </view>
                            </swiper-item>
                        </block>
                    </swiper>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp();
import http from '../../../util/http.js';
export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            http_root: app.globalData.http_root,
            submit_dis: false,
            imageWitdh: 0,
            x: 0,
            // movable-view的坐标
            y: 0,
            areaHeight: 0,
            // movable-area的高度
            hidden: true,
            // movable-view是否隐藏
            currentImg: '',
            // movable-view的图片地址
            currentIndex: 0,
            // 要改变顺序的图片的下标
            pointsArr: [],
            // 每张图片的坐标
            flag: true,
            // 是否是长按
            scrollTop: 0,
            // 滚动条距离顶部的距离
            my_focus: false,
            ExModal: false,
            expression: [],
            img_arr: [],
            //图片集
            user_info: '',
            get_text: '',
            submit_loadin: false
        };
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        uni.hideShareMenu();
        var subscribe = app.globalData.getCache('subscribe');
        if (!subscribe) {
            app.globalData.subscribe_message(
                (res) => {
                    //请求成功的回调函数
                    console.log(res);
                    if (res == '') {
                        return;
                    }
                    app.globalData.setCache('subscribe', res.parallelism_data);
                },
                () => {
                    //请求失败的回调函数，不需要时可省略
                }
            );
        }
        this.getExpEmjList();
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady() {
        // 计算图片
        this._handleComputedImage();
    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {},

    methods: {
        del_aite() {
            this.user_info = '';
        },
        /**
         * 隐藏选择颜色
         */
        hideModal() {
            this.ExModal = false;
        },
        set_emoji(d) {
            var index = d.currentTarget.dataset.index;
            var t_index = d.currentTarget.dataset.key;
            var str = this.expression[t_index][index];
            var k = str.split('.')[0];
            console.log(k);
            this.get_text = this.get_text + '[#:' + k + ']';
        },
        /**
         * 获取留言内容
         */
        get_reply_text(c) {
            this.get_text = c.detail.value;
        },
        getExpEmjList() {
            var b = app.globalData.api_root + 'Polls/get_emj_list';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            http.POST(b, {
                params: params,
                success: (res) => {
                    this.expression = res.data;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },
        // 选择图片
        handleChooseImage(e) {
            var length = this.img_arr.length;
            if (length >= 9) {
                uni.showToast({
                    title: '亲，最多只能选择九张图哦~',
                    icon: 'none',
                    duration: 2000
                });
                return false;
            }
            var e_user = app.globalData.getCache('userinfo');
            var b = app.globalData.api_root + 'User/img_upload';
            uni.chooseMedia({
                count: 9 - this.img_arr.length,
                mediaType: ['image'],
                sizeType: ['original', 'compressed'],
                //可选择原图或压缩后的图片
                sourceType: ['album', 'camera'],
                //可选择性开放访问相册、相机
                success: (res) => {
                    uni.showLoading({
                        title: '上传中...',
                        mask: true
                    });
                    let tempFilePaths = res.tempFiles;
                    console.log(res);
                    for (var i = 0, h = tempFilePaths.length; i < h; i++) {
                        uni.uploadFile({
                            url: b,
                            filePath: tempFilePaths[i].tempFilePath,
                            name: 'sngpic',
                            header: {
                                'content-type': 'multipart/form-data'
                            },
                            formData: {
                                'content-type': 'multipart/form-data',
                                token: e_user.token,
                                openid: e_user.openid,
                                much_id: app.globalData.siteInfo.uniacid
                            },
                            success: (res) => {
                                if (res.data == '') {
                                    uni.hideLoading();
                                    uni.showModal({
                                        title: '提示',
                                        content: '内存溢出，请稍候重试'
                                    });
                                    return;
                                }
                                var data = JSON.parse(res.data);
                                if (data.status == 'error') {
                                    uni.hideLoading();
                                    uni.showModal({
                                        title: '提示',
                                        content: data.msg
                                    });
                                    return;
                                } else {
                                    this.img_arr = this.img_arr.concat(data.url);
                                    //上传完之后更新面积
                                    this._handleComputedArea();
                                    uni.hideLoading();
                                }
                            },
                            fail: (res) => {
                                uni.showModal({
                                    title: '提示',
                                    content: '上传错误！'
                                });
                            }
                        });
                    }
                },
                fail: (err) => console.log(err)
            });
        },
        openExMode(d) {
            this.my_focus = false;
            this.ExModal = true;
        },
        // 删除图片
        handleDelete(e) {
            let index = e.target.dataset.index;
            let img_arr = this.img_arr;
            img_arr.splice(index, 1);
            this.img_arr = img_arr;
            this._handleComputedArea();
        },
        // 预览图片
        handlePreview(e) {
            let index = e.target.dataset.index;
            let img_arr = this.img_arr;
            uni.previewImage({
                current: img_arr[index],
                //当前预览的图片
                urls: img_arr //所有要预览的图片数组
            });
        },

        // 移动结束的时候
        handleTouchEnd(e) {
            if (!this.flag) {
                // 非长按情况下
                return;
            }
            let x = e.changedTouches[0].pageX;
            let y = e.changedTouches[0].pageY - this.scrollTop;
            const pointsArr = this.pointsArr;
            let data = this.img_arr;
            for (var j = 0; j < pointsArr.length; j++) {
                const item = pointsArr[j];
                if (x > item.left && x < item.right && y > item.top && y < item.bottom) {
                    const endIndex = item.dataset.index;
                    const beginIndex = this.currentIndex;
                    //临时保存移动的目标数据
                    let temp = data[beginIndex];
                    //将移动目标的下标值替换为被移动目标的下标值
                    data[beginIndex] = data[endIndex];
                    //将被移动目标的下标值替换为beginIndex
                    data[endIndex] = temp;
                }
            }
            this.img_arr = data;
            this.hidden = true;
            this.flag = false;
            this.currentImg = '';
        },
        // 计算movable-area的高度
        _handleComputedArea(e) {
            uni.createSelectorQuery()
                .in(this)
                .selectAll('.image-choose-container')
                .boundingClientRect((rect) => {
                    this.areaHeight = rect[0].height;
                })
                .exec();
        },
        // 计算每张图片的坐标
        _handleComputedPoints(e) {
            var query = uni.createSelectorQuery().in(this);
            var nodesRef = query.selectAll('.image-item');
            nodesRef
                .fields(
                    {
                        dataset: true,
                        rect: true
                    },
                    (result) => {
                        this.pointsArr = result;
                    }
                )
                .exec();
        },

        // 长按图片
        handleLongTap(e) {
            // 计算每张图片的坐标
            this._handleComputedPoints();
            this.currentImg = e.currentTarget.dataset.url;
            this.currentIndex = e.currentTarget.dataset.index;
            this.hidden = false;
            this.flag = true;
            this.x = e.currentTarget.offsetLeft;
            this.y = e.currentTarget.offsetTop;
        },
        // 计算图片宽度
        _handleComputedImage(e) {
            uni.getSystemInfo({
                success: (res) => {
                    const windowWidth = res.windowWidth;
                    const width = windowWidth - 30;
                    const imageWitdh = (width - 30) / 3;
                    this.imageWitdh = imageWitdh;
                },
                fail: (err) => {
                    console.log(err);
                }
            });
        },
        // 移动的过程中
        handleTouchMove(e) {
            let x = e.touches[0].pageX;
            let y = e.touches[0].pageY;
            // 首先先获得当前image-choose-container距离顶部的距离
            uni.createSelectorQuery()
                .in(this)
                .selectAll('.image-choose-container')
                .boundingClientRect((rect) => {
                    let top = rect[0].top;
                    y = y - this.scrollTop - top;
                    // x: x - this.imageWitdh / 2 > 0 ? x - this.imageWitdh / 2:0,
                    // y: y - this.imageWitdh / 2 > 0 ? y - this.imageWitdh / 2:0,
                    this.x = x;
                    this.y = y;
                })
                .exec();
        },
        open_url() {
            uni.navigateTo({
                url: '/yl_welore/pages/packageD/whisper_user/index'
            });
        },
        textareaAInput(item) {
            this.secret = item.detail.value;
        },
        select_color(item) {
            // var index = item.target.dataset.key;
            // var name = this.ColorList[index]; // TODO: ColorList is not defined.
            // this.color = index;
            // this.back = name['name'];
        },
        submit() {
            this.submit_dis = true;
            if (this.get_text == '' && this.img_arr.length == 0) {
                uni.showToast({
                    title: '内容不能为空',
                    icon: 'none',
                    duration: 2000
                });
                this.submit_dis = false;
                return;
            }
            var b = app.globalData.api_root + 'Whisper/add_secret';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.secret = this.get_text; //秘密
            params.user_info = this.user_info == '' ? 0 : this.user_info['id']; //@
            params.img_arr = JSON.stringify(this.img_arr);
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res.data);
                    this.submit_loadin = false;
                    if (res.data.code == 0) {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            success: (res) => {
                                if (res.confirm) {
                                    this.get_subscribe();
                                } else {
                                    this.get_subscribe();
                                }
                            }
                        });
                    } else {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: (res) => {}
                        });
                    }
                },
                fail: () => {
                    this.submit_loadin = false;
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },
        get_subscribe() {
            var subscribe = app.globalData.getCache('subscribe');
            if (subscribe && subscribe['YL0009'] && subscribe['YL0004']) {
                app.globalData.authorization(subscribe['YL0009'], subscribe['YL0004'], false, (res) => {
                    setTimeout(() => {
                        //获取页面栈
                        let pages = getCurrentPages();
                        //获取所需页面
                        let prevPage = pages[pages.length - 2]; //上一页
                        prevPage.setData({
                            onload: 1 //你需要传过去的数据
                        });

                        uni.navigateBack();
                    }, 500);
                });
            } else {
                setTimeout(() => {
                    //获取页面栈
                    let pages = getCurrentPages();
                    //获取所需页面
                    let prevPage = pages[pages.length - 2]; //上一页
                    prevPage.setData({
                        onload: 1 //你需要传过去的数据
                    });

                    uni.navigateBack();
                }, 500);
            }
        }
    }
};
</script>
<style>
page {
    background-color: #ffffff;
}

/* pages/index.wxss */
.container {
    width: 100%;
    padding: 16rpx;
    box-sizing: border-box;
}

.placeholder-class {
    font-size: 14px;
    font-weight: 400;
    color: rgba(204, 204, 204, 1);
    line-height: 40rpx;
}

.image-drag-container {
    padding: 32rpx 0;
    box-sizing: border-box;
}

.movable-area {
    width: 100%;
}

.image-choose-container {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
}

.image-choose-container > view {
    position: relative;
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 16rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(248, 248, 248, 1);
    border-radius: 12rpx;
    box-sizing: border-box;
    overflow: hidden;
}

.image-choose-container .image-item-temp {
    height: 0;
    background: transparent;
}

.image-choose-container > view image {
    width: 100%;
    height: 100%;
}

.image-choose-container > view .close {
    position: absolute;
    top: 0;
    right: 0;
    width: 32rpx;
    height: 32rpx;
    font-size: 12rpx;
    text-align: center;
    line-height: 32rpx;
    color: #fff;
    background: rgba(0, 0, 0, 0.4);
    border-radius: 50%;
}

.movable-view {
    /* border:1px solid rgb(0, 0, 0); */
    border-radius: 12rpx;
    box-sizing: border-box;
    background-color: rgb(255, 255, 255, 0.5);
}

.movable-view image {
    width: 100%;
    height: 100%;
}
</style>

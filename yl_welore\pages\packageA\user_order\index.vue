<template>
    <view>
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">我的订单</view>
        </cu-custom>

        <scroll-view scroll-x class="bg-white nav text-center">
            <view :class="'cu-item ' + (current == 'tab0' ? 'text-blue cur' : '')" @tap="handleChange" data-key="tab0">
                <text :class="current == 'tab0' ? '_this' : ''" style="font-weight: 700">未付款</text>
            </view>
            <view :class="'cu-item ' + (current == 'tab1' ? 'text-blue cur' : '')" @tap="handleChange" data-key="tab1">
                <text :class="current == 'tab1' ? '_this' : ''" style="font-weight: 700">待发货</text>
            </view>
            <view :class="'cu-item ' + (current == 'tab2' ? 'text-blue cur' : '')" @tap="handleChange" data-key="tab2">
                <text :class="current == 'tab2' ? '_this' : ''" style="font-weight: 700">已发货</text>
            </view>
            <view :class="'cu-item ' + (current == 'tab3' ? 'text-blue cur' : '')" @tap="handleChange" data-key="tab3">
                <text :class="current == 'tab3' ? '_this' : ''" style="font-weight: 700">退款中</text>
            </view>
            <view :class="'cu-item ' + (current == 'tab4' ? 'text-blue cur' : '')" @tap="handleChange" data-key="tab4">
                <text :class="current == 'tab4' ? '_this' : ''" style="font-weight: 700">已退款</text>
            </view>
            <view :class="'cu-item ' + (current == 'tab5' ? 'text-blue cur' : '')" @tap="handleChange" data-key="tab5">
                <text :class="current == 'tab5' ? '_this' : ''" style="font-weight: 700">已完成</text>
            </view>
        </scroll-view>

        <view style="background-color: #f3f3f3; min-height: 41.3em">
            <view style="padding: 5px">
                <view style="background-color: #fff; border-radius: 5px; min-height: 100px; padding: 10px; margin: 10px" v-for="(item, index) in my_list" :key="index">
                    <view @tap="open_goods" :data-id="item.product_id" class="flex p-xs margin-bottom-sm mb-sm align-center">
                        <view class="flex-sub radius">
                            <image :src="item.product_img" mode="aspectFit" style="width: 240rpx; height: 240rpx; border-radius: 5px"></image>
                        </view>
                        <view class="flex-twice">
                            <view style="margin-left: 10px; font-weight: 300">{{ item.product_name }}</view>
                            <view style="margin-left: 10px; margin-top: 10px; font-size: 12px; color: #999999">下单时间：{{ item.buy_time }}</view>
                            <view style="float: right; margin: 60rpx 20px 0px 0px; font-size: 14px">
                                <image
                                    v-if="item.pay_type == 0 || item.pay_type == 1"
                                    :src="stateVar.diy.currency_icon"
                                    style="width: 20px; height: 20px; vertical-align: middle"
                                ></image>
                                <text :class="item.pay_type == 2 ? 'text-price' : ''" style="font-size: 20px; vertical-align: middle; margin-left: 5px">
                                    {{ item.actual_price }}
                                </text>
                                <text v-if="item.pay_type == 0 || item.pay_type == 1" style="font-size: 20rpx">
                                    ({{ item.pay_type == 0 ? stateVar.diy.currency : stateVar.diy.confer }})
                                </text>
                                <text v-if="item.pay_type == 2" style="font-size: 20rpx">(微信支付)</text>
                            </view>
                        </view>
                    </view>

                    <view class="flex solid-bottom padding justify-end">
                        <button @tap="open_order" :data-id="item.id" class="text-lin margin-xs cu-btn round bg-black shadow sm">查看详情</button>
                        <button
                            v-if="item.status == 0 && item.pay_status == 0 && item.pay_type == 2"
                            @tap="cancel_order"
                            :data-id="item.id"
                            class="text-lin margin-xs cu-btn round bg-green shadow sm"
                        >
                            取消订单
                        </button>
                        <button v-if="item.status == 1" @tap="ok_mod_do" :data-id="item.id" class="text-lin margin-xs cu-btn round bg-green shadow sm">确认收货</button>
                        <button
                            @tap="refund_modFun"
                            :data-id="item.id"
                            v-if="item.pay_status == 1 && item.refund_count == 0 && item.status != 3 && item.status != 4"
                            class="text-lin margin-xs cu-btn round bg-red shadow sm"
                        >
                            申请退款
                        </button>
                        <button @tap="refund_del_do" :data-id="item.id" v-if="item.status == 2" class="text-lin margin-xs cu-btn round bg-purple shadow sm">取消退款</button>
                    </view>
                </view>
            </view>
            <view :class="'cu-load ' + (!di_msg ? 'loading' : 'over')"></view>
        </view>

        <view :class="'cu-modal ' + (refund_mod ? 'show' : '')">
            <view class="cu-dialog">
                <view class="cu-bar bg-white justify-end">
                    <view class="content">申请退款</view>
                    <view class="action" @tap="hideModal">
                        <text class="cuIcon-close text-red"></text>
                    </view>
                </view>
                <view v-if="refund_mod" class="padding-sm">
                    <textarea @input="bindTextAreaBlur" maxlength="500" auto-focus style="width: 100%; height: 100px; text-align: left" placeholder="请填写退款理由(最多500个字)" />
                </view>
                <view class="cu-bar bg-white justify-end">
                    <view class="action">
                        <button class="cu-btn line-green text-green" @tap="hideModal">取消</button>
                        <button class="cu-btn bg-green margin-left" @tap="refund_do">确定</button>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp();
var http = require('../../../util/http.js');
export default {
    data() {
        return {
            http_root: app.globalData.http_root,
            current: 'tab1',
            my_list: [],
            refund_mod: false,
            refund_del: false,
            ok_mod: false,
            page: 1,
            id: '',
            refund_text: '',
            di_msg: false,
            show: false,

            stateVar: {
                diy: {
                    currency_icon: '',
                    currency: '',
                    confer: ''
                }
            }
        };
    }
    /**
     * 生命周期函数--监听页面加载
     */,
    onLoad(options) {
        if (options.key == 1) {
            this.current = 'tab2';
        }
        this.up_user_smail();
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {},
    /**
     * 加载下一页
     */
    onReachBottom() {
        this.page = this.page + 1;
        this.up_user_smail();
    },
    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {},
    /**
     * 下拉刷新
     */
    onPullDownRefresh() {
        //wx.showNavigationBarLoading() //在标题栏中显示加载
        //模拟加载
        setTimeout(function () {
            uni.hideNavigationBarLoading(); //完成停止加载
            uni.stopPullDownRefresh(); //停止下拉刷新
        }, 1500);
        this.my_list = [];
        this.page = 1;
        this.up_user_smail();
    },
    methods: {
        open_order(d) {
            var id = d.currentTarget.dataset.id;
            uni.navigateTo({
                url: '/yl_welore/pages/packageA/order_info/index?id=' + id
            });
        },

        open_goods(d) {
            var id = d.currentTarget.dataset.id;
            uni.navigateTo({
                url: '/yl_welore/pages/packageA/good_info/index?id=' + id
            });
        },

        /**
         * 取消订单
         */
        cancel_order(d) {
            console.log(d);
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = d.currentTarget.dataset.id;
            var b = app.globalData.api_root + 'Order/cancel_order_do';
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                        this.my_list = [];
                        this.page = 1;
                        this.up_user_smail();
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },

        /**
         * 退款mod
         */
        refund_modFun(e) {
            this.refund_mod = true;
            this.id = e.currentTarget.dataset.id;
        },

        /**
         * 确认收货
         */
        ok_modFun(e) {
            this.ok_mod = true;
            this.id = e.currentTarget.dataset.id;
        },

        /**
         * 退款mod取消
         */
        hideModal() {
            this.refund_mod = false;
            this.refund_del = false;
            this.ok_mod = false;
        },

        /**
         * 记录退款内容
         */
        bindTextAreaBlur(e) {
            this.refund_text = e.detail.value;
        },

        /**
         * 确认收货
         */
        ok_mod_do(dd) {
            uni.showModal({
                title: '提示',
                content: '确定要确认收货吗？',
                success: (res) => {
                    if (res.confirm) {
                        var e = app.globalData.getCache('userinfo');
                        var params = new Object();
                        params.token = e.token;
                        params.openid = e.openid;
                        params.uid = e.uid;
                        params.id = dd.currentTarget.dataset.id;
                        var b = app.globalData.api_root + 'User/ok_mod_do';
                        http.POST(b, {
                            params: params,
                            success: (res) => {
                                console.log(res);
                                if (res.data.status == 'success') {
                                    uni.showToast({
                                        title: res.data.msg,
                                        icon: 'none',
                                        duration: 2000
                                    });
                                    this.my_list = [];
                                    this.page = 1;
                                    this.hideModal();
                                    this.up_user_smail();
                                } else {
                                    uni.showToast({
                                        title: res.data.msg,
                                        icon: 'none',
                                        duration: 2000
                                    });
                                }
                            },
                            fail: () => {
                                uni.showModal({
                                    title: '提示',
                                    content: '网络繁忙，请稍候重试！',
                                    showCancel: false,
                                    success: function (res) {}
                                });
                            }
                        });
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                }
            });
        },

        /**
         * 取消退款
         */
        refund_del_do(dd) {
            uni.showModal({
                title: '提示',
                content: '确定要取消退款吗？',
                success: (res) => {
                    if (res.confirm) {
                        var e = app.globalData.getCache('userinfo');
                        var params = new Object();
                        params.token = e.token;
                        params.openid = e.openid;
                        params.uid = e.uid;
                        params.id = dd.currentTarget.dataset.id;
                        var b = app.globalData.api_root + 'User/refund_del_do';
                        http.POST(b, {
                            params: params,
                            success: (res) => {
                                console.log(res);
                                if (res.data.status == 'success') {
                                    uni.showToast({
                                        title: res.data.msg,
                                        icon: 'none',
                                        duration: 2000
                                    });
                                    this.my_list = [];
                                    this.page = 1;
                                    this.hideModal();
                                    this.up_user_smail();
                                } else {
                                    uni.showToast({
                                        title: res.data.msg,
                                        icon: 'none',
                                        duration: 2000
                                    });
                                }
                            },
                            fail: () => {
                                uni.showModal({
                                    title: '提示',
                                    content: '网络繁忙，请稍候重试！',
                                    showCancel: false,
                                    success: function (res) {}
                                });
                            }
                        });
                    }
                }
            });
        },

        /**
         * 申请退款
         */
        refund_do() {
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            params.refund_text = this.refund_text;
            params.id = this.id;
            var b = app.globalData.api_root + 'User/order_refund';
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                        this.my_list = [];
                        this.page = 1;
                        this.hideModal();
                        this.up_user_smail();
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },

        handleChange(detail) {
            this.my_list = [];
            this.page = 1;
            this.current = detail.currentTarget.dataset.key;
            this.up_user_smail();
        },

        /**
         * 订单列表
         */
        up_user_smail() {
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            params.order_type = this.current;
            params.page = this.page;
            var b = app.globalData.api_root + 'User/get_order_list';
            var allMsg = this.my_list;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        for (var i = 0; i < res.data.info.length; i++) {
                            allMsg.push(res.data.info[i]);
                        }
                        this.my_list = allMsg;
                        if (res.data.info.length == 0 || allMsg.length < 5) {
                            this.di_msg = true;
                        }
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },

        navbackFun() {
            var pages = getCurrentPages();
            var Page = pages[pages.length - 1]; //当前页
            var prevPage = pages[pages.length - 2]; //上一个页面
            if (pages.length == 1) {
                uni.reLaunch({
                    url: '/yl_welore/pages/index/index'
                });
                return;
            }
            prevPage.show = false; //设置数据
            uni.navigateBack();
        }
    }
};
</script>
<style>
.nav-wrap {
    position: fixed;
    width: 100%;
    top: 0;
    background: #f8f8f8;
    color: #000;
    z-index: 9999999;
}
/* 标题要居中 */
.nav-title {
    position: absolute;
    text-align: center;
    max-width: 377rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    font-size: 36rpx;
    color: #2c2b2b;
    font-weight: 600;
}
.yes_pos {
    background-color: #f8f8f8 !important;
    position: relative;
}
.nav-capsule {
    display: flex;
    align-items: center;
    margin-left: 20rpx;
    width: 50rpx;
    justify-content: space-around;
    border-radius: 50%;
    margin-top: 54rpx;
    z-index: 999999999;
}

.navbar-v-line {
    width: 1px;
    height: 32rpx;
    background-color: #f3f3f3;
}

.back-pre {
    width: 40rpx;
    height: 40rpx;
    margin-top: 11rpx;
    margin-left: 0rpx;
}
.class_img {
    width: 130rpx;
    height: 130rpx;
    border-radius: 100%;
}

.classify {
    height: 100%;
    margin: 25rpx;
    text-align: center;
    border-radius: 25rpx;
    box-sizing: border-box;
    display: inline-block;
    position: relative;
}

/**
     * 弹窗
     */
.show-btn {
    margin-top: 100rpx;
    color: #22cc22;
}

.modal-mask {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    background: #000;
    opacity: 0.5;
    overflow: hidden;
    z-index: 9000;
    color: #fff;
}

.modal-dialog {
    width: 440rpx;
    overflow: hidden;
    position: fixed;
    top: 45%;
    left: 0;
    z-index: 9999;
    background: #f9f9f9;
    margin: -180rpx 160rpx;
    border-radius: 30rpx;
}

.modal-title {
    padding-top: 50rpx;
    font-size: 14px;
    color: #030303;
    text-align: center;
}

.modal-content {
    padding: 20rpx 32rpx;
}

.modal-input {
    display: flex;
    background: #fff;
    border: 2rpx solid #ddd;
    border-radius: 4rpx;
    font-size: 28rpx;
}

.input {
    width: 100%;
    height: 82rpx;
    font-size: 28rpx;
    line-height: 28rpx;
    padding: 0 20rpx;
    box-sizing: border-box;
    color: #333;
}

input-holder {
    color: #666;
    font-size: 28rpx;
}

.modal-footer {
    display: flex;
    flex-direction: row;
    height: 86rpx;
    border-top: 1px solid #dedede;
    font-size: 14px;
    line-height: 86rpx;
}

.btn-cancel {
    width: 50%;
    color: #666;
    text-align: center;
    border-right: 1px solid #dedede;
}

.btn-confirm {
    width: 50%;
    color: #cc3333;
    text-align: center;
}
.text-lin {
    font-weight: 300;
}
</style>

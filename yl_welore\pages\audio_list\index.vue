<template>
<view>
<cu-custom bgColor="bg-white" :isBack="true" :isSearch="false">
  <view slot="backText">返回</view>
  <view slot="content" style="color: #2c2b2b;font-weight: 600; font-size: 36rpx;">有声</view>
</cu-custom>
<view style="background-color:#FFFFFF;padding-bottom:80px;min-height:400px;">
  <block v-for="(item,dataListindex) in (list)" :key="dataListindex">
    



<view style="background-color:#fff;overflow:hidden;position: relative;">
      <view style="">
        <!-- 头像 -->
        <view class="cu-list menu-avatar">
          <view class="cu-item">
            <view @tap="home_url" data-k="1" :data-user_id="item.user_id" class="cu-avatar round lg" :style="'background-image:url('+(item.user_head_sculpture)+');'">
              <view :class="'cu-tag badge '+(item.gender==2?'cuIcon-female bg-pink':'cuIcon-male bg-blue')"></view>
              <image class="now_level" style="height: 30rpx;width: 30rpx;position: absolute;right:-1px;bottom:-3px;z-index:100;max-width:initial" :src="item.attr.attest.at_icon"></image>
            </view>
            <view class="content flex-sub">
              <view class="align-center">
                <view :class="item.special">{{item.user_nick_name}}</view>
                <image mode="widthFix" class="now_level" :src="item.level" style="width:30rpx;vertical-align: middle;margin-left: 5px;"></image>
                <image v-if="item.user_vip==1" :src="(http_root)+'addons/yl_welore/web/static/applet_icon/vip.png'" style="width:20px;height:20px;;vertical-align:bottom;margin-left:3px;"></image>
                <image mode="heightFix" class="now_level" v-if="item.wear_merit" :src="item.wear_merit" style="height:13px;vertical-align:bottom;margin-left:3px;"></image>
              </view>
              <view class="text-gray text-sm flex">
                <text style="font-size: 13px;color: #888888;">{{item.adapter_time}}</text>
              </view>
            </view>
          </view>
        </view>
        <!-- 头像 -->
        <!-- 内容 -->
        <view>
          <view class="weui-cell" style="padding:0rpx 10px 10px 20px;">
            <view class="weui-cell__hd text_num" :style="'font-size:16px;position: relative;margin-right: 10px;color:'+(item.study_title_color)+';'">
              <text @tap="gambit_list" :data-id="item.gambit_id" style="color:#0099FF;margin-right: 5px;">{{item.gambit_name}}</text>
              <text @tap="home_url" :data-index="dataListindex" data-k="3" :data-type="item.study_type" :data-id="item.id" v-if="item.study_title||item.study_content">{{item.study_title==''?item.study_content:item.study_title}}</text>
            </view>
          </view>
          <view class="weui-cell" v-if="item.study_type==1">
            <view style="margin: 0 auto;overflow: hidden;display: flex;justify-content: space-between;align-items: center;height: 170rpx;width: 90%;background-color: #f6f7f7;border:1px solid #F0F0F0;border-radius:10rpx;">
              <view :style="'background-image: url('+(item.user_head_sculpture)+');background-size: cover;background-position: center;width: 170rpx;background-color: #000;height: 170rpx;'">
                <view class="audioOpen" @tap="play" v-if="(!item.is_voice)" :data-key="dataListindex" :data-vo="item.study_voice">
                  <text style="color:#ffffff;font-size: 15px;" class="cicon-play-arrow"></text>
                </view>
                <view class="audioOpen" @tap="stop" v-if="item.is_voice" :data-key="dataListindex" :data-vo="item.study_voice">
                  <text style="color:#ffffff;font-size: 15px;" class="cicon-pause"></text>
                </view>
              </view>
              <view style="width:75%;padding: 20rpx">
                <view style="display: flex;justify-content: space-between;align-items: center;">
                  <view style="font-size: 28rpx;color: #555555;font-weight: 600;">
                    {{item.user_nick_name}}上传的音乐
                  </view>
                  <view class="times">{{item.starttime}}</view>
                </view>
                <view style="display: flex;justify-content: space-between;align-items: center;margin-top: 20rpx;">
                  <view style="font-size: 24rpx;color: #999;">{{item.user_nick_name}}</view>
                  <view>
                    <slider style="width: 170rpx;" @change="sliderChange" block-size="12px" step="1" :value="item.offset" :max="item.max" selected-color="#4c9dee"/>
                  </view>
                </view>
              </view>
            </view>
            <!-- <view class='audiosBox'>
              <view class="audioOpen" bindtap="play" wx:if="{{!item.is_voice}}" data-vo="{{item.study_voice}}" data-key='{{dataListindex}}'>
                <text style="color:#4c9dee;font-size: 25px;" class="cicon-play-arrow"></text>
              </view>
              <view class="audioOpen" bindtap="stop" wx:if="{{item.is_voice}}" data-vo="{{item.study_voice}}" data-key='{{dataListindex}}'>
                <text style="color:#4c9dee;font-size: 26px;" class="cicon-pause"></text>
              </view>
              <view class='slid'>
                <slider bindchange="sliderChange" data-key='{{dataListindex}}' block-size="12px" step="1" value="{{item.offset}}" max="{{item.max}}" selected-color="#4c9dee" />
                <view bindtap="home_url" data-k="3" data-type="{{item.study_type}}" data-id="{{item.id}}" style="margin-top: 3px;">
                  <text class='times'>{{item.starttime}}</text>
                  <text class='times'>{{item.study_voice_time}}</text>
                </view>
              </view>
            </view> -->
          </view>
        </view>
        <!-- 内容 -->
        <view style="clear:both;height:0"></view>
        <view class="" style="padding-bottom:10px;padding-top:5px;">
          <view @tap="home_url" data-k="2" :data-id="item.tory_id" style="float:left;margin-left:20px;font-size:14px;padding-top:6px;font-weight:500;color:#3399FF;" class="weui-flex__item">
            {{item.realm_name}}
          </view>
          <view style="float:right;margin-right:15px;" class="weui-flex__item">
            <button @tap="home_url" data-k="3" :data-type="item.study_type" :data-id="item.id" hover-class="none">
              <image :src="(http_root)+'addons/yl_welore/web/static/mineIcon/index3/xiaoxi.png'" style="width: 28px;vertical-align:middle;height:28px;"></image>
              <text class="index_nav_name" style="color:#000;font-size:12px;vertical-align:super;">{{item.study_repount}}</text>
            </button>
          </view>
          <view style="float:right;margin-bottom:15px;margin-right:15px;" class="weui-flex__item">
            <button hover-class="none" @tap="parseEventDynamicCode($event, item.is_buy==1?'':'add_zan')" :data-id="item.id" :data-key="dataListindex">
              <image :src="(http_root)+'addons/yl_welore/web/static/mineIcon/index3/zan_1.png'" style="width: 28px;height:28px;vertical-align:middle;"></image>
              <text class="index_nav_name" style="color:#000;font-size:12px;vertical-align:super;">{{item.info_zan_count_this>10000?item.info_zan_count:item.info_zan_count_this}}</text>
            </button>
          </view>
        </view>
      </view>
    </view>




    



<view style="width:93%;height:1px;background-color:#F2F2F2;margin:0 auto;"></view>




  </block>
  <view :class="'cu-load '+(!di_msg?'loading':'over')"></view>
</view>
</view>
</template>

<script >
var app = getApp();
import http from "../../util/http.js";
const innerAudioContext = uni.getBackgroundAudioManager();
export default {
  /**
   * 页面的初始数据
   */
  data() {
    return {
      http_root: app.globalData.http_root,
      di_reply: false,
      page: 1,
      list: [],
      videoimage: "block",
      //默认显示封面
      bindplay: null,
      _index: 0,
      list_index: 0,
      check_user_login: false
    };
  },
  /**
  * 生命周期函数--监听页面加载
  */
  onLoad(options) {
    this.get_audio_list();
  },
  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    uni.showNavigationBarLoading(); //在标题栏中显示加载
    //模拟加载
    setTimeout(() => {
      uni.hideNavigationBarLoading(); //完成停止加载
      uni.stopPullDownRefresh(); //停止下拉刷新
    }, 1500);
    this.list = [];
    this.page = 1;
    this.get_audio_list();
  },
  /**
   * 加载下一页
   */
  onReachBottom() {
    this.page = this.page + 1;
    this.get_audio_list();
  },
  onShow() {},
  methods: {
    get_audio_list() {
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.page = this.page;
      var b = app.globalData.api_root + 'Index/get_audio_list';
      http.POST(b, {
        params: params,
        success: res => {
          console.log(res);
          if (res.data.info.length == 0) {
            this.di_msg = true;
          }
          for (var i = 0; i < res.data.info.length; i++) {
            this.list.push(res.data.info[i]);
          }
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) {}
          });
        }
      });
    },
    /**
    * 点击话题
    */
    gambit_list(d) {
      var e = app.globalData.getCache("userinfo");
      var warrant_arbor = getApp().globalData.store.getState().copyright['warrant_arbor'];
      if (e.tourist == 1 && warrant_arbor == 1) {
        this.check_user_login = true;
        return;
      }
      var id = d.currentTarget.dataset.id;
      uni.navigateTo({
        url: '/yl_welore/pages/gambit/index?id=' + id
      });
    },
    /**
     * 首页跳转链接
     */
    home_url(dd) {
      var warrant_arbor = getApp().globalData.store.getState().copyright['warrant_arbor'];
      var e = app.globalData.getCache("userinfo");
      var key = dd.currentTarget.dataset.k; //跳转类型
      if (key == 1) {
        //头像跳转
        uni.navigateTo({
          url: '/yl_welore/pages/packageB/my_home/index?id=' + dd.currentTarget.dataset.user_id
        });
        return;
      }
      if (key == 2) {
        //圈子跳转
        uni.navigateTo({
          url: '/yl_welore/pages/packageA/circle_info/index?id=' + dd.currentTarget.dataset.id
        });
        return;
      }
      if (key == 3) {
        //内容跳转
        var douyin = app.globalData.__PlugUnitScreen('5fb4baf1f25fe251685b526dc8c30b8f');
        var info = this.list[dd.currentTarget.dataset.index];
        if (dd.currentTarget.dataset.type == 2 && info.is_buy == 0 && e.user_phone && douyin) {
          uni.navigateTo({
            url: '/yl_welore/pages/packageF/full_video/index?id=' + dd.currentTarget.dataset.id
          });
          return;
        }
        uni.navigateTo({
          url: '/yl_welore/pages/packageA/article/index?id=' + dd.currentTarget.dataset.id + '&type=' + dd.currentTarget.dataset.type
        });
        return;
      }
    },
    //播放声音
    play(e) {
      var index = e.currentTarget.dataset.key;
      var nuw = this.list;
      var key = 1;
      uni.getBackgroundAudioPlayerState({
        success(res) {
          console.log(res);
          const status = res.status;
          key = res.status;
        }
      });
      for (var i = 0; i < nuw.length; i++) {
        this.$set(this.list[i], 'is_voice', false);
      }
      console.log('播放');
      innerAudioContext.src = e.currentTarget.dataset.vo;
      innerAudioContext.title = nuw[index]['study_title'] ? nuw[index]['study_title'] : '暂无标题';
      innerAudioContext.onTimeUpdate(() => {
        //console.log(innerAudioContext.currentTime)
        var duration = innerAudioContext.duration;
        var offset = innerAudioContext.currentTime;
        var currentTime = parseInt(innerAudioContext.currentTime);
        var min = "0" + parseInt(currentTime / 60);
        var sec = currentTime % 60;
        if (sec < 10) {
          sec = "0" + sec;
        }
        var starttime = min + ':' + sec; /*  00:00  */
        this.$set(this.list[index], 'starttime', starttime);
        this.$set(this.list[index], 'offset', offset);
      });
      // innerAudioContext.play();

      this.$set(this.list[index], 'is_voice', true);
      this.list_index = index;
      //播放结束
      innerAudioContext.onEnded(() => {
        this.$set(this.list[index], 'is_voice', false);
        this.starttime = '00:00';
        this.offset = 0;
        console.log("音乐播放结束");
      });
      innerAudioContext.play();
    },
    /**
     * 停止
     */
    stop(e) {
      innerAudioContext.pause();
      console.log('暂停');
      var index = e.currentTarget.dataset.key;
      this.$set(this.list[index], 'is_voice', false);
    },
    // 进度条拖拽
    sliderChange(e) {
      var index = e.currentTarget.dataset.key;
      var offset = parseInt(e.detail.value);
      innerAudioContext.play();
      innerAudioContext.seek(offset);
      this.$set(this.list[index], 'is_voice', true);
    }
  }
};
</script>
<style >
 page{
  background-color: #ffffff;
}

/**index.wxss**/
.audiosBox{
  width: 92%;
  margin: auto;
  height: 130rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f6f7f7;
  border-radius: 10rpx;
}

/*按钮大小  */
.audioOpen {
  width: 50rpx;
  height: 50rpx;
  border: 1px solid #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  margin-top: 57rpx;
}

.image2 {
  margin-left: 10%;
}

/*进度条长度  */
.slid {
  flex: 1;
  position: relative;
}

.slid view {
  display: flex;
  justify-content: space-between;
}

.slid view>text:nth-child(1) {
  color: #4c9dee;
  margin-left: 6rpx;
}

.slid view>text:nth-child(2) {
  margin-right: 6rpx;
}

slider {
  width: 520rpx;
  margin: 0;
  margin-left: 35rpx;
}

/*横向布局  */
.times{
  width: 100rpx;
  text-align: center;
  display: inline-block;
  font-size: 24rpx;
  color:#999999;
  margin-top: 5rpx;
}
.title view{
  text-indent: 2em;
}

button::after {
    line-height: normal;
    font-size: 30rpx;
    width: 0;
    height: 0;
    top: 0;
    left: 0;
  }
  
  button {
    line-height: normal;
    display: block;
    padding-left: 0px;
    padding-right: 0px;
    background-color: rgba(255, 255, 255, 0);
    font-size: 30rpx;
    overflow: inherit;
  }  
</style>
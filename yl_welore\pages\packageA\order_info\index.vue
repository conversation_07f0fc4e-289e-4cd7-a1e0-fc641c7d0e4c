<template>
    <view>
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">订单详情</view>
        </cu-custom>
        <block v-if="order_info">
            <view class="card" v-if="order_info.easy != ''">
                <view class="t_box">
                    <view class="top">
                        <view class="title">
                            <text>将二维码出示给商家</text>
                        </view>
                    </view>
                </view>
                <view class="b_box">
                    <view class="bottom">
                        <view v-if="load && !ok_pos" class="cu-load loading"></view>
                        <canvas v-if="!ok_pos" style="width: 150px; height: 150px; margin: 0 auto" canvas-id="myQrcode"></canvas>
                        <view v-if="ok_pos" class="cu-tag radius bg-green">商家已验证</view>
                        <!-- <view style="font-size: 12px;padding: 10px;color: #999999;">
          <text user-select="true">券号：{{order_info.card}}</text>
        </view> -->
                        <view style="font-size: 12px; padding: 10px; color: #999999; text-align: left">
                            <text class="_icon-info-o"></text>
                            <text>为保障您的权益，到店消费前不要将券码或二维码提供给商家!</text>
                        </view>
                    </view>
                </view>
            </view>
        </block>
        <view style="padding: 5px 5px 40rpx 5px">
            <view style="background-color: #fff; border-radius: 5px; min-height: 100px; padding: 20px 10px 10px 10px; margin: 10px">
                <view class="cu-bar bg-white">
                    <view class="action">
                        <text class="cuIcon-titles text-black"></text>
                        <text class="text-xl text-bold">
                            <text v-if="order_info.status == 0">{{ order_info.is_offline == 1 ? '待提货' : '待发货' }}</text>
                            <text v-if="order_info.status == 1">已发货</text>
                            <text v-if="order_info.status == 2">申请退款</text>
                            <text v-if="order_info.status == 3">已退款</text>
                            <text v-if="order_info.status == 4">已完成</text>
                        </text>
                    </view>
                </view>
                <view class="flex p-xs margin align-start">
                    <view class="flex-sub">
                        <image :src="order_info.product_img" mode="aspectFit" style="width: 120px; height: 120px; border-radius: 5px"></image>
                    </view>
                    <view class="flex-twice">
                        <view style="font-weight: 500; font-size: 16px; padding-left: 10px">{{ order_info.product_name }}</view>
                        <view v-if="order_info.vested_attribute" style="font-weight: 500; font-size: 14px; padding: 20rpx; color: #999999">
                            <text>{{ order_info.vested_attribute[0] }}：</text>
                            <text>{{ order_info.vested_attribute[1] }}</text>
                        </view>
                    </view>
                </view>
                <block v-if="order_info.easy != ''">
                    <view class="cu-bar bg-white">
                        <view class="action">
                            <text class="cuIcon-titles text-black"></text>
                            <text class="text-xl text-bold">商家信息</text>
                        </view>
                    </view>
                    <view class="flex solid-bottom padding justify-between">
                        <view>商家</view>
                        <view>
                            <text>{{ order_info.easy.merchant_name }}</text>
                        </view>
                    </view>
                    <view class="flex solid-bottom padding justify-between">
                        <view>地址</view>
                        <view
                            @tap.stop.prevent="get_position"
                            :data-pos_name="order_info.easy.address_name"
                            :data-latitude="order_info.easy.address_latitude"
                            :data-longitude="order_info.easy.address_longitude"
                        >
                            <text>{{ order_info.easy.address_name }}</text>
                            <text class="cicon-location-on"></text>
                        </view>
                    </view>
                    <view class="flex solid-bottom padding justify-between">
                        <view>联系方式</view>
                        <view @tap.stop.prevent="open_phone" :data-p="order_info.easy.merchant_phone">
                            <text>{{ order_info.easy.merchant_phone }}</text>
                        </view>
                    </view>
                </block>
                <view class="cu-bar bg-white">
                    <view class="action">
                        <text class="cuIcon-titles text-black"></text>
                        <text class="text-xl text-bold">支付信息</text>
                    </view>
                </view>
                <view class="flex solid-bottom padding justify-between">
                    <view>价格</view>
                    <view>
                        <text :class="order_info.pay_type == 2 ? 'text-price' : ''">{{ order_info.product_price }}</text>
                        <text v-if="order_info.pay_type == 0 || order_info.pay_type == 1" style="font-size: 20rpx">
                            ({{ order_info.pay_type == 0 ? design.currency : design.confer }})
                        </text>
                        <text v-if="order_info.pay_type == 2" style="font-size: 20rpx">(微信支付)</text>
                    </view>
                </view>
                <view v-if="order_info.product_discount < 1" class="flex solid-bottom padding justify-between">
                    <view>折扣</view>
                    <view>{{ order_info.product_discount * 100 }}折</view>
                </view>
                <view class="flex solid-bottom padding justify-between">
                    <view>实付</view>
                    <view>
                        <text :class="order_info.pay_type == 2 ? 'text-price' : ''">{{ order_info.actual_price }}</text>
                        <text v-if="order_info.pay_type == 0 || order_info.pay_type == 1" style="font-size: 20rpx">
                            ({{ order_info.pay_type == 0 ? design.currency : design.confer }})
                        </text>
                        <text v-if="order_info.pay_type == 2" style="font-size: 20rpx">(微信支付)</text>
                    </view>
                </view>
                <view class="flex solid-bottom padding justify-between">
                    <view>赠送{{ design.confer }}</view>
                    <view>{{ order_info.product_rebate }}</view>
                </view>
                <view class="flex solid-bottom padding justify-between">
                    <view>支付时间</view>
                    <view>{{ order_info.buy_time }}</view>
                </view>

                <view class="cu-bar bg-white">
                    <view class="action">
                        <text class="cuIcon-titles text-black"></text>
                        <text class="text-xl text-bold">订单信息</text>
                    </view>
                </view>
                <view class="flex solid-bottom padding justify-between">
                    <view>订单编号</view>
                    <view>
                        <text>{{ order_info.order_number }}</text>
                        <view @tap="copyBtn" :data-no="order_info.order_number" class="cu-tag radius sm" style="margin-left: 5px">复制</view>
                    </view>
                </view>
                <view class="flex solid-bottom padding justify-between">
                    <view>创建时间</view>
                    <view>
                        <text>{{ order_info.buy_time }}</text>
                    </view>
                </view>
                <view v-if="order_info.status == 1 || order_info.status == 4" class="flex solid-bottom padding justify-between">
                    <view>发货时间</view>
                    <view>
                        <text>{{ order_info.ship_time }}</text>
                    </view>
                </view>
                <view class="flex solid-bottom padding justify-between">
                    <view>订单备注</view>
                    <view>
                        <text>{{ order_info.remark }}</text>
                    </view>
                </view>
                <view class="cu-bar bg-white">
                    <view class="action">
                        <text class="cuIcon-titles text-black"></text>
                        <text class="text-xl text-bold">收货信息</text>
                    </view>
                </view>
                <view class="flex solid-bottom padding justify-between">
                    <view>收货人</view>
                    <view>
                        <text>{{ order_info.buyer_name ? order_info.buyer_name : '' }}</text>
                    </view>
                </view>
                <view class="flex solid-bottom padding justify-between">
                    <view>电话</view>
                    <view>
                        <text>{{ order_info.buyer_phone ? order_info.buyer_phone : '' }}</text>
                    </view>
                </view>
                <view class="flex solid-bottom padding justify-between">
                    <view>地址</view>
                    <view style="width: 80%">
                        <text>{{ order_info.buyer_address ? order_info.buyer_address : '' }}</text>
                    </view>
                </view>
                <view class="flex solid-bottom padding justify-between align-center">
                    <view>
                        <view style="text-align: center">物流</view>
                        <view style="text-align: center">/</view>
                        <view style="text-align: center">兑换码</view>
                    </view>
                    <view style="width: 60%">
                        <text style="word-break: break-all">{{ order_info.shipment == null ? '无' : order_info.shipment }}</text>
                        <view @tap="copyBtn" :data-no="order_info.shipment" v-if="order_info.shipment != null" class="cu-tag radius sm" style="margin-left: 5px">复制</view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp();
var http = require('../../../util/http.js');
var drawQrcode = require('../../../util/weapp.qrcode.js');
export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            http_root: app.globalData.http_root,
            order_info: '',
            load: true,
            ok_pos: false,
            height: app.globalData.height,
            id: null,
            design: null
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        const design = uni.getStorageSync('is_diy');
        this.height = app.globalData.height;
        this.id = options.id;
        this.design = design;
        this.get_my_order();
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {},
    /**
     * 下拉刷新
     */
    onPullDownRefresh() {
        //wx.showNavigationBarLoading() //在标题栏中显示加载
        //模拟加载
        setTimeout(() => {
            uni.hideNavigationBarLoading(); //完成停止加载
            uni.stopPullDownRefresh(); //停止下拉刷新
        }, 1500);
        this.get_my_order();
    },
    methods: {
        /**
         * 打开地图
         */
        get_position(d) {
            console.log(d);
            var a = Number(d.currentTarget.dataset.latitude);
            var o = Number(d.currentTarget.dataset.longitude);
            var name = d.currentTarget.dataset.pos_name;
            if (a && o) {
                uni.openLocation({
                    latitude: a,
                    longitude: o,
                    name: name
                });
            }
        },
        open_phone(d) {
            console.log(d);
            uni.makePhoneCall({
                phoneNumber: d.currentTarget.dataset.p
            });
        },

        draw() {
            var url = this.http_root + 'addons/yl_welore/web/index.php?code=1&token=' + this.order_info.code;
            console.log(url);
            drawQrcode({
                width: 150,
                height: 150,
                x: 0,
                y: 0,
                canvasId: 'myQrcode',
                typeNumber: -1,
                text: url,
                callback: (e) => {
                    this.load = false;
                }
            });
        },
        /**
         * 订单详情
         */
        get_my_order() {
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            params.id = this.id;
            var b = app.globalData.api_root + 'User/get_my_order';
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        this.order_info = res.data.info;
                        if (res.data.info.is_offline == 1) {
                            if (res.data.info.status == 0 || res.data.info.status == 1) {
                                this.draw();
                            } else {
                                this.ok_pos = true;
                            }
                        }
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },
        /**
         * 一键复制
         */
        copyBtn(e) {
            uni.setClipboardData({
                data: e.currentTarget.dataset.no,
                success: function (res) {}
            });
        }
    }
};
</script>
<style>
page {
    background-color: #f3f3f3;
}

.nav-wrap {
    position: fixed;
    width: 100%;
    top: 0;
    color: #000;
    z-index: 9999999;
}
/* 标题要居中 */
.nav-title {
    position: absolute;
    text-align: center;
    max-width: 377rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    font-size: 36rpx;
    color: #2c2b2b;
    font-weight: 600;
}

.nav-capsule {
    display: flex;
    align-items: center;
    margin-left: 20rpx;
    width: 50rpx;
    justify-content: space-around;
    border-radius: 50%;
    margin-top: 54rpx;
    z-index: 999999999;
}

.navbar-v-line {
    width: 1px;
    height: 32rpx;
    background-color: #f3f3f3;
}

.back-pre {
    width: 32rpx;
    height: 32rpx;
    margin-top: 11rpx;
    margin-left: 0rpx;
}
.class_img {
    width: 130rpx;
    height: 130rpx;
    border-radius: 100%;
}

.classify {
    height: 100%;
    margin: 25rpx;
    text-align: center;
    border-radius: 25rpx;
    box-sizing: border-box;
    display: inline-block;
    position: relative;
}

.card {
    width: 683rpx;
    border-radius: 40rpx;
    box-sizing: border-box;
    margin: 20px auto 0px;
}

.top {
    box-sizing: border-box;
    padding-top: 40rpx;
    padding-bottom: 40rpx;
    border-bottom: 1px dashed #000000;
    position: relative;
}

.title {
    display: flex;
    align-items: center;
    justify-content: center;
}

.title text {
    font-size: 30rpx;
    font-weight: bold;
    color: #000000;
    padding: 0 13rpx 0 20rpx;
}

.title span {
    width: 21rpx;
    height: 21rpx;
    background: #999999;
    border-radius: 50%;
    position: relative;
}

.tips {
    font-size: 26rpx;
    font-weight: 500;
    color: #333333;
    margin: 24rpx 39rpx 0 24rpx;
    word-break: break-all;
    word-wrap: break-word;
    height: 50px;
}

.bottom {
    text-align: center;
    padding: 10rpx 0px 20px 0px;
    height: auto;
}

.t_box {
    border-radius: 40rpx 40rpx 0 0;
    padding: 0 24rpx;
    background-size: 70% 100%;
    background-image: radial-gradient(circle at 100% 100%, transparent 0, transparent 11px, #fff 11px),
        radial-gradient(circle at 0 100%, transparent 0, transparent 11px, #fff 11px);
    background-repeat: no-repeat;
    background-position: right bottom, left bottom;
}

.b_box {
    border-radius: 0 0 40rpx 40rpx;
    padding: 30rpx 24rpx 0;
    background-size: 70% 100%;
    background-image: radial-gradient(circle at 0 0, transparent 0, transparent 11px, #fff 11px), radial-gradient(circle at 100% 0, transparent 0, transparent 11px, #fff 11px);
    background-repeat: no-repeat;
    background-position: left top, right top;
}
</style>

<template>
    <view>
        <cu-custom bgColor="bg-white" :isBack="true" :isSearch="false">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">申请列表</view>
        </cu-custom>
        <view>
            <block v-for="(item, index) in user_list" :key="index">
                <view class="cu-list menu-avatar">
                    <view class="cu-item">
                        <view class="cu-avatar round lg" :style="'background-image:url(' + item.user_head_sculpture + ');'"></view>
                        <view class="content flex-sub">
                            <view class="text-grey">{{ item.user_nick_name }}</view>
                            <view class="text-gray text-sm flex justify-between">
                                <text style="color: #000">申请时间：{{ item.sult_time }}</text>
                            </view>
                        </view>
                        <view class="flex padding align-end" style="z-index: 10">
                            <view
                                @tap="envite_sulord"
                                :data-id="item.id"
                                data-key="2"
                                style="float: right; margin-left: 11px; margin-top: 14px; color: #fff; background-color: red; padding: 5px; border-radius: 5px"
                            >
                                拒绝
                            </view>
                            <view
                                @tap="envite_sulord"
                                :data-id="item.id"
                                data-key="1"
                                style="float: right; margin-left: 11px; margin-top: 14px; color: #ffffff; background-color: #33cc66; padding: 5px; border-radius: 5px"
                            >
                                同意
                            </view>
                        </view>
                    </view>
                    <view style="margin-left: 11px" v-if="item.reason">
                        <text style="color: #000">申请理由：{{ item.reason }}</text>
                    </view>
                </view>
            </block>
        </view>

        <view :class="'cu-load ' + (user_list.length == 0 ? 'over' : '')"></view>

        <view :class="'cu-modal ' + (envite ? 'show' : '')">
            <view class="cu-dialog">
                <view class="cu-bar bg-white justify-end">
                    <view class="content">操作确认</view>
                    <view class="action" @tap="hideModal">
                        <text class="cuIcon-close text-red"></text>
                    </view>
                </view>
                <view class="padding" style="background-color: #fff">
                    确定要
                    <text style="color: #33cc66" v-if="data_key == 1">通过申请</text>
                    <text style="color: red" v-if="data_key == 2">拒绝申请</text>
                    吗？
                </view>
                <view class="cu-bar bg-white justify-end">
                    <view class="action">
                        <button class="cu-btn line-green text-green" @tap="hideModal">取消</button>
                        <button class="cu-btn bg-green margin-left" @tap="add_envite_sulord">确定</button>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp();
var http = require('../../../util/http.js');
export default {
    data() {
        return {
            user_list: [],
            envite: false,
            ins_id: '',
            page: 0,
            id: '',
            data_key: ''
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.page = 1;
        this.id = options.id;
        this.get_my_rec();
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {},
    methods: {
        /**
         * 申请列表
         */
        get_my_rec() {
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            params.id = this.id;
            params.page = this.page;
            var b = app.globalData.api_root + 'User/get_user_territory_interest';
            http.POST(b, {
                params: params,
                success: function (res) {
                    console.log(res);
                    if (res.data.status == 'success') {
                        that.user_list = res.data.info;
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: function () {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },

        envite_sulord(e) {
            this.envite = true;
            this.ins_id = e.currentTarget.dataset.id;
            this.data_key = e.currentTarget.dataset.key;
        },

        hideModal() {
            this.envite = false;
        },

        /**
         * 拒绝或者同意
         */
        add_envite_sulord() {
            this.hideModal();
            uni.showLoading({
                title: '操作中...',
                mask: true
            });
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            params.id = this.ins_id;
            params.status = this.data_key;
            var b = app.globalData.api_root + 'User/add_territory_interest';
            http.POST(b, {
                params: params,
                success: function (res) {
                    if (res.data.status == 'success') {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                        that.get_my_rec();
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                    uni.hideLoading();
                },
                fail: function () {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        }
    }
};
</script>
<style>
page {
    background-color: #fff;
}
</style>

<template>
    <view>
        <view v-if="rank.length > 0" class="flex justify-between" style="padding-left: 13px; margin-top: 10px">
            <view>
                <image v-if="http_root" :src="http_root + 'addons/yl_welore/web/static/applet_icon/task.png'"
                    style="margin-right: 5px; width: 50rpx; height: 50rpx; vertical-align: middle"></image>
                <text style="font-size: 34rpx; color: #000000; font-weight: 600">排行榜</text>
            </view>
        </view> 
        <view v-if="rank.length > 0"
            style="background-color: #fff; border-radius: 5px; overflow: hidden; margin-bottom: 10px">
            <scroll-view :scroll-x="true" style="padding: 15px; white-space: nowrap">
                <view v-for="(item, index) in rank" :key="index" @tap="open_ph" :data-id="item.id" style="position: relative;;display: inline-block;margin-right: 30rpx;">
                    <view class="ph_txt">{{ item.ranking_name }}</view>
                    <image   class="ph_img" :src="item.bg_img" style="width: 290rpx"></image>
                </view>
            </scroll-view>
        </view>
        <view style="padding-left: 26rpx; color: #afafaf; margin-top: 10px">
            <image v-if="http_root" :src="http_root + 'addons/yl_welore/web/static/applet_icon/my_jia.png'"
                style="width: 60rpx; height: 60rpx; vertical-align: middle"></image>
            <text style="font-size: 34rpx; color: #000000; font-weight: 600">我加入的{{ design.landgrave }}</text>
            <navigator v-if="copyright.tory_arbor == 1" url="/yl_welore/pages/packageB/set_territory/index"
                style="display: inline" hover-class="none">
                <view class="" style="
                        float: right;
                        background-color: #2e77ed;
                        color: #ffffff;
                        border-radius: 40rpx;
                        padding: 10rpx;
                        width: 135rpx;
                        text-align: center;
                        font-size: 11px;
                        margin-right: 24rpx;
                    ">
                    创建{{ design.landgrave }}
                </view>
            </navigator>
        </view>

        <scroll-view :scroll-x="true" @scrolltolower="nex_my_qq" style="white-space: nowrap; width: 100%">
            <view class="classify" :data-id="item.id" @tap="this_url" v-for="(item, index) in info" :key="index">
                <!-- <navigator url='/yl_welore/pages/packageA/circle_info/index?id={{item.id}}' hover-class="none"> -->

                <view class="cu-avatar round lg"
                    :style="'background-color:#fff;background-image:url(' + item.realm_icon + ');'">
                    <view v-if="item.attention == 1" class="cu-tag badge cuIcon-lock bg-red"></view>
                    <view v-if="item.attention == 2" class="cu-tag badge cuIcon-vip bg-yellow"></view>
                </view>

                <view style="font-size: 12px; margin-top: 5px">
                    <text class="text-cut" style="vertical-align: text-bottom">{{ item.realm_name }}</text>
                </view>
            </view>
            <view v-if="info.length == 0" class="cu-load over" style="color: #999999"></view>
        </scroll-view>

        <view class="flex justify-between" style="padding-left: 13px; margin-top: 10px">
            <view>
                <image v-if="http_root" :src="http_root + 'addons/yl_welore/web/static/applet_icon/my_chang.png'"
                    style="width: 60rpx; height: 60rpx; vertical-align: middle"></image>
                <text style="font-size: 34rpx; color: #000000; font-weight: 600">{{ design.landgrave }}广场</text>
            </view>
            <view style="margin-right: 10px">
                <navigator url="/yl_welore/pages/square/index" hover-class="none">
                    <text style="font-size: 30rpx; color: #6c6c6c; margin-left: 10rpx">所有{{ design.landgrave }}</text>
                    <text class="cuIcon-right lg text-gray"></text>
                </navigator>
            </view>
        </view>

        <scroll-view :scroll-x="true" style="width: 100%; white-space: nowrap; overflow: hidden">
            <view class="classify_nex" style="vertical-align: top" v-for="(item, index) in needle" :key="index">
                <view class="chang_img" :style="'width:100%;background-image:url(' +
                    item.icon +
                    '); background-repeat:no-repeat; background-size:100% 100%;-moz-background-size:100% 100%;text-align:left;line-height:76rpx;'
                    ">
                    <text style="font-size: 32rpx; font-weight: 600; color: #ffffff; margin-left: 30rpx">{{ item.name
                    }}</text>
                </view>

                <view class="cu-list menu-avatar">
                    <view style="background-color: #f9fafb" class="cu-item" v-if="c_index < 3" :data-id="c_item.id"
                        @tap="this_url" v-for="(c_item, c_index) in item.children" :key="c_index">
                        <view class="cu-avatar round" :style="'background-image:url(' + c_item.realm_icon + ');'">
                            <view v-if="c_item.attention == 1" class="cu-tag badge cuIcon-lock bg-red"></view>
                            <view v-if="c_item.attention == 2" class="cu-tag badge cuIcon-vip bg-yellow"></view>
                        </view>

                        <view class="content" style="left: 60px">
                            <view class="text-grey" style="width: 80px">
                                <text class="text-cut" style="width: 80px; text-align: left">{{ c_item.realm_name
                                }}</text>
                            </view>
                            <view class="text-gray text-sm flex">{{ c_item.concern }}人</view>
                        </view>
                    </view>
                    <view style="background-color: #f9fafb; text-align: center; display: block; height: 30px"
                        class="cu-item text-gray text-sm" @tap="quan_url" :data-id="item.id">
                        查看更多
                        <text class="cuIcon-right lg text-gray"></text>
                    </view>
                </view>
            </view>
        </scroll-view>
        <view style="clear: both; height: 0"></view>

        <view class="flex justify-between" style="padding-left: 13px; margin-top: 10px">
            <view>
                <image v-if="http_root" :src="http_root + 'addons/yl_welore/web/static/applet_icon/my_jian.png'"
                    style="width: 60rpx; height: 60rpx; vertical-align: middle"></image>
                <text style="font-size: 34rpx; color: #000000; font-weight: 600">为我推荐</text>
            </view>
            <view style="margin-right: 10px">
                <navigator url="/yl_welore/pages/square/index" hover-class="none">
                    <text style="font-size: 30rpx; color: #6c6c6c; margin-left: 10rpx">所有{{ design.landgrave }}</text>
                    <text class="cuIcon-right lg text-gray"></text>
                </navigator>
            </view>
        </view>
        <view style="clear: both; height: 0"></view>
        <!-- 为我推荐 -->

        <view class="cu-list menu-avatar" style="padding-bottom: 65px">
            <view class="cu-item" v-for="(item, t_index) in tj_list" :key="t_index">
                <view class="cu-avatar round lg" :style="'background-image:url(' + item.realm_icon + ');'"></view>

                <view class="content">
                    <view>{{ item.realm_name }}</view>
                    <view class="text-gray text-sm justify-between text-cut">
                        {{ item.realm_synopsis }}
                    </view>
                </view>

                <view style="margin-right: 10px; font-size: 12px" :data-id="item.id" @tap="this_url">
                    <button class="cu-btn round bg-green shadow">进入</button>
                </view>
            </view>
            <view :class="'cu-load ' + (!di_msg ? 'loading' : 'over')"></view>
        </view>
    </view>
</template>

<script>
export default {
    props: ['data', 'compName'],
    computed: {
        rank() {
            return this.$parent.$data.rank;
        },
        http_root() {
            return this.$parent.$data.http_root;
        },
        design() {
            return this.$parent.$data.design;
        },
        copyright() {
            return this.$parent.$data.copyright;
        },
        info() {
            return this.$parent.$data.info;
        },
        needle() {
            return this.$parent.$data.needle;
        },
        tj_list() {
            return this.$parent.$data.tj_list;
        },
        di_msg() {
            return this.$parent.$data.di_msg;
        },
    },
    methods: {
        open_ph(e) {
            this.$emit('open_ph', e);
         },
         nex_my_qq(e) {
            this.$emit('nex_my_qq', e);
        },
        this_url(e) {
            this.$emit('this_url', e);
        },
        quan_url(e) {
            this.$emit('quan_url', e);
        },
    }
};
</script>
<style></style>

<template>
    <view>
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">{{ title }}</view>
        </cu-custom>
        <view class="cu-bar search bg-white">
            <view class="search-form round">
                <text class="cuIcon-search"></text>
                <input @input="set_search_name" :value="search_name" type="text" placeholder="搜索" confirm-type="search" />
            </view>
            <view class="action">
                <button @tap="get_search_name" class="cu-btn bg-yellow shadow-blur round text-white">搜索</button>
            </view>
        </view>
        <!-- 主盒子 -->
        <view class="container" style="">
            <!-- 左侧栏 -->
            <view class="nav_left" :style="'height:' + left_height + 'px;  background-color: #F5F5F7;'">
                <!-- 当前项的id等于item项的id，那个就是当前状态 -->
                <!-- 用data-index记录这个数据在数组的下标位置，使用data-id设置每个item的id值，供打开2级页面使用 -->
                <block v-if="left_show">
                    <view :class="'nav_left_items ' + (curNav == -1 ? 'select' : '')" @tap="get_left_type" data-type="-1" :data-index="index" data-id="-1">已加入</view>
                    <view :class="'nav_left_items ' + (curNav == -2 ? 'select' : '')" @tap="get_left_type" data-type="-2" :data-index="index" data-id="-2">最热</view>
                    <view :class="'nav_left_items ' + (curNav == -3 ? 'select' : '')" @tap="get_left_type" data-type="-3" :data-index="index" data-id="-3">最新</view>
                </block>
                <view
                    :class="'nav_left_items ' + (curNav == item.id ? 'select' : '')"
                    @tap="get_left_type"
                    data-type="0"
                    :data-index="index"
                    :data-id="item.id"
                    v-for="(item, index) in navLeftItems"
                    :key="index"
                >
                    {{ item.name }}
                </view>
            </view>

            <!-- 右侧栏 -->
            <scroll-view :scroll-y="true" class="nav_right" @scrolltolower="lower">
                <!-- 如果有数据，才遍历项 -->
                <view class="cu-list menu-avatar">
                    <view
                        class="cu-item animation-slide-bottom"
                        :style="'margin:25rpx 0px;animation-delay: ' + (rightIndex + 1) * 0.1 + 's;'"
                        v-for="(item, rightIndex) in navRightItems"
                        :key="rightIndex"
                    >
                        <view class="cu-avatar lg" :style="'background-image:url(' + item.realm_icon + ');border-radius: 30rpx;'">
                            <view v-if="item.attention == 1" class="cu-tag badge cuIcon-lock bg-red"></view>
                            <view v-if="item.attention == 2" class="cu-tag badge cuIcon-vip bg-yellow"></view>
                        </view>

                        <view class="content flex-sub">
                            <view class="text-grey">{{ item.realm_name }}</view>
                            <view class="text-gray text-sm flex justify-between" style="margin-top: 10rpx">{{ item.concern }}人加入</view>
                        </view>

                        <view class="text-gray text-sm">
                            <button v-if="type == 0" @tap="ruel_url" :data-id="item.id" class="cu-btn bg-blue round light">进入</button>
                            <button v-if="type == 1" @tap="ruel_url" :data-name="item.realm_name" :data-id="item.id" class="cu-btn round bg-green shadow">选择</button>
                        </view>
                    </view>
                </view>
                <view :class="'cu-load ' + (!di_msg ? 'loading' : 'over')"></view>
            </scroll-view>
        </view>
    </view>
</template>

<script>
const app = getApp();
var http = require('../../util/http.js');
export default {
    data() {
        return {
            type: 0,
            left_height: 724,
            isIpx: app.globalData.isIpx,

            nvabarData: {
                showCapsule: 1 //是否显示左上角图标
            },

            title: '',

            //导航栏 中间的标题
            navLeftItems: [],

            navRightItems: [],
            curNav: -1,
            di_msg: true,
            page: 1,
            search_name: '',
            left_show: true,
            height: '',
            design: '',
            showLeft: false,
            fa_class: '',
            get_hidden: false,
            show: false,
            rightIndex: 0
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        var design = uni.getStorageSync('is_diy');
        this.height = app.globalData.height;
        this.title = '全部' + design['landgrave'];
        this.design = design;

        if (options.curNav) {
            this.curNav = options.curNav;
        }
        if (options.type == 'add') {
            this.type = 1;
        }
        this.get_left_needle();
        this.get_right_item();
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {
        var that = this;
        uni.getSystemInfo({
            success: (e) => {
                console.log(e);
                let capsule = uni.getMenuButtonBoundingClientRect();
                if (capsule) {
                    var Custom = capsule;
                    var CustomBar = capsule.bottom + capsule.top - e.statusBarHeight;
                } else {
                    var CustomBar = e.statusBarHeight + 50;
                }
                that.left_height = e.screenHeight - CustomBar - 50;
            }
        });
    },
    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {
        var forward = app.globalData.forward;
        console.log(forward);
        if (forward) {
            return {
                title: forward.title,
                path: '/yl_welore/pages/square/index',
                imageUrl: forward.reis_img
            };
        } else {
            return {
                title: '您的好友给您发了一条信息',
                path: '/yl_welore/pages/square/index'
            };
        }
    },
    methods: {
        set_search_name(d) {
            this.search_name = d.detail.value;
        },

        get_search_name() {
            if (this.search_name != '') {
                this.page = 1;
                this.navLeftItems = [
                    {
                        name: '搜索',
                        id: '-4'
                    }
                ];
                this.navRightItems = [];
                this.curNav = -4;
                this.left_show = false;
                this.get_right_item();
            } else {
                this.page = 1;
                this.navLeftItems = [];
                this.navRightItems = [];
                this.curNav = -1;
                this.left_show = true;
                this.get_left_needle();
                this.get_right_item();
            }
        },

        ruel_url(d) {
            if (this.type == 1) {
                //判断是否关注这个圈子
                var that = this;
                var e = app.globalData.getCache('userinfo');
                var params = new Object();
                params.token = e.token;
                params.openid = e.openid;
                params.open_id = d.currentTarget.dataset.id;
                var b = app.globalData.api_root + 'Nameplate/check_open_id';
                http.POST(b, {
                    params: params,
                    success: (res) => {
                        if (res.data.status == 'error') {
                            uni.showModal({
                                title: '提示',
                                content: res.data.msg,
                                showCancel: false,
                                success: (res) => {}
                            });
                        } else {
                            var pages = getCurrentPages();
                            var prevPage = pages[pages.length - 2]; //上一个页面
                            // 将我们想要传递的参数在这里直接赋值。上个页面就会执行这里的操作。
                            prevPage.title = '发布到' + d.currentTarget.dataset.name;
                            prevPage.showLeft = false;
                            prevPage.fa_class = d.currentTarget.dataset.id;
                            prevPage.get_hidden = true;
                            uni.navigateBack();
                        }
                    },
                    fail: () => {
                        uni.showModal({
                            title: '提示',
                            content: '网络繁忙，请稍候重试！',
                            showCancel: false,
                            success: (res) => {}
                        });
                    }
                });
            } else {
                uni.navigateTo({
                    url: '/yl_welore/pages/packageA/circle_info/index?id=' + d.currentTarget.dataset.id
                });
            }
        },

        lower() {
            this.page = this.page + 1;
            if (this.curNav > 0) {
                this.get_right_item();
            }
        },

        /**
         * 类型
         */
        get_left_type(op) {
            console.log(op);
            this.curNav = op.currentTarget.dataset.id;
            this.page = 1;
            this.navRightItems = [];
            this.get_right_item();
        },

        /**
         * 获取所有圈子
         */
        get_left_needle() {
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            var b = app.globalData.api_root + 'User/get_left_needle';
            http.POST(b, {
                params: params,
                success: (res) => {
                    if (res.data.status == 'success') {
                        that.navLeftItems = res.data.info;
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },

        /**
         * 右边的数据
         */
        get_right_item() {
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            params.get_id = that.curNav;
            params.page = that.page;
            params.search_name = that.search_name;
            var b = app.globalData.api_root + 'User/get_right_needle';
            var allMsg = that.navRightItems;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        for (var i = 0; i < res.data.info.length; i++) {
                            allMsg.push(res.data.info[i]);
                        }
                        that.navRightItems = allMsg;
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },

        navbackFun() {
            var pages = getCurrentPages();
            var Page = pages[pages.length - 1]; //当前页
            var prevPage = pages[pages.length - 2]; //上一个页面
            if (pages.length == 1) {
                this.backhomeFun();
                return;
            }
            prevPage.show = false; //设置数据
            uni.navigateBack();
        },

        backhomeFun() {
            uni.switchTab({
                url: '/yl_welore/pages/index/index'
            });
        }
    }
};
</script>
<style>
/*总体主盒子*/
.container {
    width: 100%;
    background-color: #fff;
    color: #939393;
}

/*左侧栏主盒子*/
.nav_left {
    /*设置行内块级元素（没使用定位）*/
    display: inline-block;
    width: 20%;
    height: 40em;
    /*主盒子设置背景色为灰色*/
    text-align: center;
    overflow-y: scroll;
    color: #000000;
}

/*左侧栏list的item*/
.nav_left .nav_left_items {
    /*每个高30px*/
    height: 100rpx;
    /*垂直居中*/
    line-height: 100rpx;
    /*再设上下padding增加高度，总高42px*/
    /*文字14px*/
    font-size: 14px;
}

/*左侧栏list的item被选中时*/
.select {
    /* border-left:4px solid #2E77ED; */
    color: #2e77ed;
    background-color: #ffffff !important;
}

/*右侧栏主盒子*/
.nav_right {
    /*右侧盒子使用了绝对定位*/
    position: absolute;
    top: 17%;
    right: 0;
    flex: 1;
    /*宽度75%，高度占满，并使用百分比布局*/
    height: 82%;
    padding: 10px;
    box-sizing: border-box;
    background: #fff;
    width: 79%;
    overflow-y: scroll;
}

/*右侧栏list的item*/
.nav_right .nav_right_items {
    /*浮动向左*/
    float: left;
    /*每个item设置宽度是33.33%*/
    width: 33.33%;
    height: 80px;
    text-align: center;
}

.nav_right .nav_right_items image {
    /*被图片设置宽高*/
    width: 50px;
    height: 30px;
}

.nav_right .nav_right_items text {
    /*给text设成块级元素*/
    display: block;
    margin-top: 5px;
    font-size: 10px;
    /*设置文字溢出部分为...*/
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
</style>

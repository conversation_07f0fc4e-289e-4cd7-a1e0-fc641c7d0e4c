<template>
    <view>
        <swiper
            v-if="sw_info.length > 0"
            previous-margin="5rpx"
            next-margin="5rpx"
            class="card-swiper square-dot"
            :indicator-dots="true"
            :circular="true"
            :autoplay="true"
            interval="5000"
            duration="500"
            @change="cardSwiper"
            indicator-color="#98ccc5"
            indicator-active-color="#028774"
        >
            <swiper-item :class="cardCur == index ? 'cur' : ''" v-for="(item, index) in sw_info" :key="index">
                <view
                    class="swiper-item"
                    @tap.stop.prevent="open_navigator"
                    :data-src="item.playbill_url"
                    :data-path="item.wx_app_url"
                    :data-type="item.practice_type"
                    :data-url="item.url"
                >
                    <image :src="item.playbill_url" mode="aspectFill"></image>
                </view>
            </swiper-item>
        </swiper>
        <Home @set_one="set_one" @top_home_url="top_url" @get_all_qq="get_all_qq" @nex_my_qq="nex_my_qq" @this_url="this_url" :data="parentData"></Home>

        <!-- 置顶 -->
        <view style="background-color: #fff; margin: 10px 20px; border-radius: 5px; color: #000; box-shadow: 0px 0px 5px 0px rgba(153, 153, 153, 0.5)">
            <view class="weui-cell" style="padding: 10px 15px 10px 20px" v-for="(item, index) in home_list" :key="index">
                <navigator :url="'/yl_welore/pages/packageA/article/index?id=' + item.id + '&type=' + item.study_type" hover-class="none">
                    <view class="weui-cell__bd">
                        <text style="vertical-align: sub; float: left; margin-right: 10px; font-size: 14px; color: #96cbc3">置顶</text>
                        <view class="text-cut" style="margin-left: 10px; font-size: 14px">
                            <text @tap="gambit_list" style="color: #0099ff; margin-right: 5px">{{ item.gambit_name }}</text>
                            {{ item.study_title == '' ? item.study_content : item.study_title }}
                        </view>
                    </view>
                </navigator>
            </view>
        </view>
        <!-- 置顶 -->
        <view class="grid margin-bottom text-center margin-top-lg">
            <view style="margin-left: 30px">
                <view @tap="handleChange" data-key="tab1" style="line-height: 25px; position: relative; height: 30px">
                    <text :class="current == 'tab1' ? '_this' : ''" :style="(current == 'tab1' ? 'font-size:20px;' : 'font-size:16px;') + ';font-weight:300;'">首页</text>
                    <image
                        class="now_level"
                        v-if="current == 'tab1'"
                        mode="widthFix"
                        style="width: 40px; display: block"
                        :src="http_root + 'addons/yl_welore/web/static/mineIcon/index6/tab.png'"
                    ></image>
                </view>
            </view>
            <view style="margin: 0px 30px">
                <view @tap="handleChange" data-key="tab3" style="line-height: 25px; position: relative; height: 30px">
                    <text :class="current == 'tab3' ? '_this' : ''" :style="(current == 'tab3' ? 'font-size:20px;' : 'font-size:16px;') + ';font-weight:300;'">推荐</text>
                    <image
                        class="now_level"
                        v-if="current == 'tab3'"
                        mode="widthFix"
                        style="width: 40px; display: block"
                        :src="http_root + 'addons/yl_welore/web/static/mineIcon/index6/tab.png'"
                    ></image>
                </view>
            </view>
            <view class="">
                <view @tap="handleChange" data-key="tab2" style="line-height: 25px; position: relative; height: 30px">
                    <text :class="current == 'tab2' ? '_this' : ''" :style="';' + (current == 'tab2' ? 'font-size:20px;' : 'font-size:16px;') + ';font-weight:300;'">关注</text>
                    <image
                        class="now_level"
                        v-if="current == 'tab2'"
                        mode="widthFix"
                        style="width: 40px; display: block"
                        :src="http_root + 'addons/yl_welore/web/static/mineIcon/index6/tab.png'"
                    ></image>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import Home from './home.vue';
export default {
    components: {
        Home 
    },
    props: ['data', 'compName'],
    computed: {
        parentData() {
            return this.$parent.$data;
        },
        sw_info() {
            return this.$parent.$data.sw_info;
        },
        cardCur() {
            return this.$parent.$data.cardCur;
        },
        current() {
            return this.$parent.$data.current;
        },
        home_list() {
            return this.$parent.$data.home_list;
        },
        http_root() {
            return this.$parent.$data.http_root;
        },
        
    },
    methods: {
        bindchange_top(e) {
            this.$emit('bindchange_top', e);
        },
        open_navigator(e) {
            this.$emit('open_navigator', e);
        },
        handleChange(e) {
            this.$emit('handleChange', e);
        },
        gambit_list(e) {
            this.$emit('gambit_list', e);
        },
        cardSwiper(e) {
            this.$emit('cardSwiper', e);
        },
        set_one(e) {
            this.$emit('set_one', e);
        },
        top_url(e) {
            this.$emit('top_url', e);
        },
        get_all_qq(e) {
            this.$emit('get_all_qq', e);
        },
        nex_my_qq(e) {
            this.$emit('nex_my_qq', e);
        },
        this_url(e) {
            this.$emit('this_url', e);
        }
    }
};
</script>
<style></style>

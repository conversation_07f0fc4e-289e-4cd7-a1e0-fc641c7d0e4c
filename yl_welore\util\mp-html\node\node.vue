<template>
    <view>
        <block v-for="(n1, i1) in childs" :key="i1">
            <El compName="el" :data="{ n: n1, i: '' + i1, opts: opts, ctrl: ctrl }" v-if="use(n1)"></El>

            <view v-else :id="n1.attrs.id" :class="'_' + n1.name + ' ' + n1.attrs.class" :style="n1.attrs.style">
                <block v-for="(n2, i2) in n1.children" :key="i2">
                    <El compName="el" :data="{ n: n2, i: i1 + '_' + i2, opts: opts, ctrl: ctrl }" v-if="use(n2)"></El>

                    <view v-else :id="n2.attrs.id" :class="'_' + n2.name + ' ' + n2.attrs.class" :style="n2.attrs.style">
                        <block v-for="(n3, i3) in n2.children" :key="i3">
                            <El compName="el" :data="{ n: n3, i: i1 + '_' + i2 + '_' + i3, opts: opts, ctrl: ctrl }" v-if="use(n3)"></El>

                            <view v-else :id="n3.attrs.id" :class="'_' + n3.name + ' ' + n3.attrs.class" :style="n3.attrs.style">
                                <block v-for="(n4, i4) in n3.children" :key="i4">
                                    <El compName="el" :data="{ n: n4, i: i1 + '_' + i2 + '_' + i3 + '_' + i4, opts: opts, ctrl: ctrl }" v-if="use(n4)"></El>

                                    <view v-else :id="n4.attrs.id" :class="'_' + n4.name + ' ' + n4.attrs.class" :style="n4.attrs.style">
                                        <block v-for="(n5, i5) in n4.children" :key="i5">
                                            <El compName="el" :data="{ n: n5, i: i1 + '_' + i2 + '_' + i3 + '_' + i4 + '_' + i5, opts: opts, ctrl: ctrl }" v-if="use(n5)"></El>

                                            <node
                                                v-else
                                                :id="n5.attrs.id"
                                                :class="'_' + n5.name + ' ' + n5.attrs.class"
                                                :style="n5.attrs.style"
                                                :childs="n5.children"
                                                :opts="opts"
                                            />
                                        </block>
                                    </view>
                                </block>
                            </view>
                        </block>
                    </view>
                </block>
            </view>
        </block>
    </view>
</template>
<script module="use" lang="wxs">
var e = { abbr: !0, b: !0, big: !0, code: !0, del: !0, em: !0, i: !0, ins: !0, label: !0, q: !0, small: !0, span: !0, strong: !0, sub: !0, sup: !0 }; module.exports = function (n) { return !n.c && (!n.children || "a" === n.name || !e[n.name] && -1 === (n.attrs.style || "").indexOf("inline")) };
</script>
<script>
'use strict';
import node from './node';
import El from './node-el.vue';
function t(t, i, e) {
    if (i in t) {
        Object.defineProperty(t, i, {
            value: e,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        t[i] = e;
    }
    return t;
}
export default {
    components: {
        node,
        El
    },
    data() {
        return {
            ctrl: {},
            i1: '',

            n1: {
                attrs: {
                    id: '',
                    class: '',
                    style: ''
                },

                name: '',
                children: []
            },

            i2: '',

            n2: {
                attrs: {
                    id: '',
                    class: '',
                    style: ''
                },

                name: '',
                children: []
            },

            i3: '',

            n3: {
                attrs: {
                    id: '',
                    class: '',
                    style: ''
                },

                name: '',
                children: []
            },

            i4: '',

            n4: {
                attrs: {
                    id: '',
                    class: '',
                    style: ''
                },

                name: '',
                children: []
            },

            i5: '',

            n5: {
                attrs: {
                    id: '',
                    class: '',
                    style: ''
                },

                name: '',
                children: ''
            }
        };
    },
    props: {
        childs: Array,
        opts: Array
    },
    mounted() {
        // 处理小程序 attached 生命周期
        this.attached();
    },
    methods: {
        attached: function () {
            this.$emit(
                'add',
                {
                    detail: this
                },
                {
                    bubbles: true,
                    composed: true
                }
            );
        },

        noop: function () {},

        getNode: function (t) {
            for (var i = t.split('_'), e = this.childs[i[0]], r = 1; r < i.length; r++) {
                e = e.children[i[r]];
            }
            return e;
        },

        play: function (t) {
            if (this.root.data.pauseVideo) {
                for (var i = false, e = t.target.id, r = this.root._videos.length; r--; ) {
                    this.root._videos[r].id === e ? (i = true) : this.root._videos[r].pause();
                }
                if (!i) {
                    var s = uni.createVideoContext(e, this);
                    s.id = e;
                    this.root._videos.push(s);
                }
            }
        },

        imgTap: function (t) {
            var i = this.getNode(t.target.dataset.i);
            if (i.a) {
                return this.linkTap(i.a);
            }
            if (
                !i.attrs.ignore &&
                (this.root.$emit('imgtap', {
                    detail: i.attrs
                }),
                this.root.data.previewImg)
            ) {
                var e = this.root.imgList[i.i];
                uni.previewImage({
                    current: e,
                    urls: this.root.imgList
                });
            }
        },

        imgLoad: function (i) {
            var e;
            var r = i.target.dataset.i;
            var s = this.getNode(r);
            s.w ? ((this.opts[1] && !this.ctrl[r]) || -1 === this.ctrl[r]) && (e = 1) : (e = i.detail.width);
            if (e) {
                this.setData(t({}, 'ctrl.' + r, e));
            }
        },

        linkTap: function (t) {
            var i = t.currentTarget ? this.getNode(t.currentTarget.dataset.i) : {};
            var e = i.attrs || t;
            var r = e.href;
            console.log(r);
            this.root.$emit('linktap', {
                detail: Object.assign(
                    {
                        innerText: this.root.getText(i.children || [])
                    },
                    e
                )
            });
            if (r) {
                '#' === r[0]
                    ? this.root.navigateTo(r.substring(1)).catch(function () {})
                    : r.split('?')[0].includes('://')
                    ? this.root.data.copyLink
                    : uni.navigateTo({
                          url: r,
                          fail: function () {
                              uni.switchTab({
                                  url: r,
                                  fail: function () {}
                              });
                          }
                      });
            }
        },

        mediaError: function (i) {
            var e = i.target.dataset.i;
            var r = this.getNode(e);
            if ('video' === r.name || 'audio' === r.name) {
                var s = (this.ctrl[e] || 0) + 1;
                if (s > r.src.length) {
                    s = 0;
                }
                if (s < r.src.length) {
                    return this.setData(t({}, 'ctrl.' + e, s));
                }
            } else {
                if ('img' === r.name && this.opts[2]) {
                    this.setData(t({}, 'ctrl.' + e, -1));
                }
            }
            if (this.root) {
                this.root.$emit('error', {
                    detail: {
                        source: r.name,
                        attrs: r.attrs,
                        errMsg: i.detail.errMsg
                    }
                });
            }
        }
    },
    created: function () {}
};
</script>
<style>
@import '../../../colorui/icon.css';
.blockquote {
    padding: 10px;
    border-left: 5rpx solid #ddd;
    background-color: #f9f9f9;
}
rich-text {
    line-height: 50rpx;
}
._a {
    padding: 1.5px 0 1.5px 0;
    color: #366092;
    word-break: break-all;
}

._hover {
    text-decoration: underline;
    opacity: 0.7;
}

._img {
    width: 100%;
    max-width: 100%;
    border-radius: 5px;
    -webkit-touch-callout: none;
    margin-top: 10px;
}

._b,
._strong {
    font-weight: 700;
}

._code {
    font-family: monospace;
}

._del {
    text-decoration: line-through;
}

._em,
._i {
    font-style: italic;
}

._h1 {
    font-size: 2em;
}

._h2 {
    font-size: 1.5em;
}

._h3 {
    font-size: 1.17em;
}

._h5 {
    font-size: 0.83em;
}

._h6 {
    font-size: 0.67em;
}

._h1,
._h2,
._h3,
._h4,
._h5,
._h6 {
    display: block;
    font-weight: 700;
}

._ins {
    text-decoration: underline;
}

._li {
    display: list-item;
}

._ol {
    list-style-type: decimal;
}

._ol,
._ul {
    display: block;
    padding-left: 40px;
    margin: 1em 0;
}

._q::before {
    content: '"';
}

._q::after {
    content: '"';
}

._sub {
    font-size: smaller;
    vertical-align: sub;
}

._sup {
    font-size: smaller;
    vertical-align: super;
}

._tbody,
._tfoot,
._thead {
    display: table-row-group;
}

._tr {
    display: table-row;
}

._td,
._th {
    display: table-cell;
    vertical-align: middle;
}

._th {
    font-weight: 700;
    text-align: center;
}

._ul {
    list-style-type: disc;
}

._ul ._ul {
    margin: 0;
    list-style-type: circle;
}

._ul ._ul ._ul {
    list-style-type: square;
}

._abbr,
._b,
._code,
._del,
._em,
._i,
._ins,
._label,
._q,
._span,
._strong,
._sub,
._sup {
    display: inline;
}

._blockquote,
._div,
._p {
    display: block;
}
.p ._img {
    text-align: center;
}
.stealth_module {
    padding: 30rpx;
    background-color: hsla(86, 9%, 51%, 0.07) !important;
    background-image: linear-gradient(
        225deg,
        rgba(141, 198, 63, 0.5) 10%,
        transparent 0,
        transparent 50%,
        rgba(141, 198, 63, 0.5) 0,
        rgba(141, 198, 63, 0.5) 60%,
        transparent 0,
        transparent
    );
    background-size: 7.07px 7.07px;
    z-index: -1;
    margin: 20rpx;
}

.stealth_module_true {
    color: #000000;
    padding: 40rpx;
    text-align: center;
    margin: 20rpx;
}

.bg-stripes-grey {
    background-color: hsla(0, 0%, 58%, 0.07) !important;
    background-image: linear-gradient(
        225deg,
        rgba(135, 153, 163, 0.5) 10%,
        transparent 0,
        transparent 50%,
        rgba(135, 153, 163, 0.5) 0,
        rgba(135, 153, 163, 0.5) 60%,
        transparent 0,
        transparent
    );
    background-size: 7.07px 7.07px;
    z-index: -1;
}

.bg-stripes-olive {
    background-color: hsla(86, 9%, 51%, 0.07) !important;
    background-image: linear-gradient(
        225deg,
        rgba(141, 198, 63, 0.5) 10%,
        transparent 0,
        transparent 50%,
        rgba(141, 198, 63, 0.5) 0,
        rgba(141, 198, 63, 0.5) 60%,
        transparent 0,
        transparent
    );
    background-size: 7.07px 7.07px;
    z-index: -1;
}

.cu-btn {
    position: relative;
    border: 0rpx;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    padding: 0 30rpx;
    font-size: 28rpx;
    height: 64rpx;
    line-height: 1;
    text-align: center;
    text-decoration: none;
    overflow: visible;
    margin-left: initial;
    transform: translate(0rpx, 0rpx);
    margin-right: initial;
}

.cu-btn.lg {
    padding: 0 25rpx;
    font-size: 28rpx;
    height: 75rpx;
    width: 80%;
    letter-spacing: 5px;
    font-weight: 700;
}

.bg-green {
    /* background-image: linear-gradient(to top, #4481eb 0%, #04befe 100%); */
    /* background-image: linear-gradient(120deg, #89f7fe 0%, #66a6ff 100%); */
    background-image: linear-gradient(to right, #4facfe 0%, #00f2fe 100%);
    /* background-image: linear-gradient(120deg, #84fab0 0%, #8fd3f4 100%); */
    color: #ffffff;
}
</style>

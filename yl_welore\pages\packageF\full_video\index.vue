<template>
    <view>
        <mp-video-swiper ref="videoSwiper" id="video-swiper-id"
            @tap.native="change($event, { tagId: 'video-swiper-id' })"
            @slide="onSlide($event, { tagId: 'video-swiper-id' })" class="video-swiper"
            :videoListNewDom="videoListNewDom" :video-list="videoList"
            @play="onPlay($event, { tagId: 'video-swiper-id' })" @pause="onPause($event, { tagId: 'video-swiper-id' })"
            @timeupdate="onTimeUpdate($event, { tagId: 'video-swiper-id' })"
            @ended="onEnded($event, { tagId: 'video-swiper-id' })"
            @error="onError($event, { tagId: 'video-swiper-id' })"
            @waiting="onWaiting($event, { tagId: 'video-swiper-id' })"
            @progress="onProgress($event, { tagId: 'video-swiper-id' })"
            @loadedmetadata="onLoadedMetaData($event, { tagId: 'video-swiper-id' })"
            @showPause="onShowPause($event, { tagId: 'video-swiper-id' })"
            @hidePause="onHidePause($event, { tagId: 'video-swiper-id' })"></mp-video-swiper>
        <image v-if="playShow" @tap="change" class="imagePlayer"
            :style="'z-index: 10;left:' + (systemInfo.screenWidth / 2 - 40 / 2) + 'px;top:' + (systemInfo.screenHeight / 2 - 40 / 2) + 'px'"
            src="/static/yl_welore/style/icon/home_bf.png"></image>
        <text @tap.stop.prevent="BackPage"
            :style="'z-index: 2000;position: fixed;top: ' + (menuButtonBoundingClientRect.top + 5) + 'px;left: 25rpx;font-size: 20px;'"
            class="cicon-back text-white"></text>
        <text @tap.stop.prevent="BackPage"
            :style="'z-index: 2000;position: fixed;top: ' + (menuButtonBoundingClientRect.top + 6) + 'px;left: 66rpx;font-size: 14px;'"
            class="text-white">
            返回
        </text>
        <view class="viewTabContainer" v-if="!BindPhone">
            <view class="viewTab" @tap.stop.prevent="open_url" :data-id="ListInfo.user_id" data-key="1">
                <image :src="ListInfo.user_head_sculpture" style="height: 40px; width: 40px; border-radius: 50%">
                </image>
            </view>
            <view class="viewTab" @tap.stop.prevent="VideoZan">
                <text :class="'cicon-favorite ' + (ListInfo.is_info_zan ? 'text-red' : 'text-white')"
                    style="font-size: 60rpx"></text>
                <text class="icon_bottom">{{ ListInfo.info_zan_count }}</text>
            </view>
            <view class="viewTab" @tap.stop.prevent="OpenVideoReply">
                <text class="cicon-chat-smiles" style="font-size: 60rpx"></text>
                <text class="icon_bottom">{{ ListInfo.study_repount }}</text>
            </view>
            <view class="viewTab" @tap.stop.prevent="VideoCollect">
                <text :class="'cicon-star ' + (ListInfo.is_study_collect ? 'text-yellow' : 'text-white')"
                    style="font-size: 60rpx"></text>
                <text class="icon_bottom">{{ ListInfo.study_collect }}</text>
            </view>
            <view class="viewTab">
                <button hover-class="none" open-type="share" class="share_chick cicon-reply text-white"></button>
            </view>
        </view>
        <view class="viewFloat">
            <view class="videoGambit text_num_1" v-if="ListInfo.gambit_name" @tap.stop.prevent="open_url"
                :data-id="ListInfo.gambit_id" data-key="2">
                <text class="cicon-slack-square text-white"
                    style="font-size: 20px; vertical-align: middle; margin-left: 5px"></text>
                <text style="padding: 0px 10px 0px 5px; vertical-align: middle">{{ ListInfo.gambit_name }}</text>
            </view>
            <view @tap="open_at" class="videoTitle text_num_1">{{ ListInfo.study_title }}</view>
            <view class="videoDes" @tap="open_at">
                <rich-text :nodes="ListInfo.study_content"></rich-text>
                <!-- <text  wx:if="{{ListInfo.is_len==1}}" class="text-blue" style="padding:3px;font-size: 10px;background-color: rgba(0,0,0,0.5);color:#CCCCCC;border-radius: 3px; letter-spacing: 2px;">详情</text> -->
            </view>
        </view>
        <view v-if="!BindPhone" @tap.stop.prevent="open_url" :data-id="ListInfo.tory_id" data-key="3"
            style="height: 80rpx; width: 100%; position: fixed; bottom: 90rpx; background-color: rgba(0, 0, 0, 0.5); line-height: 80rpx">
            <text class="cicon-discover text-white"
                style="font-size: 20px; vertical-align: middle; margin: 0px 10px"></text>
            <text class="text-white" style="vertical-align: middle">{{ ListInfo.realm_name }}</text>
            <text class="text-white cicon-forward"
                style="vertical-align: middle; float: right; margin-right: 10px"></text>
        </view>
        <view class="progress-box">
            <progress :percent="percent" stroke-width="3" backgroundColor="#3E3E3E" activeColor="#FFFFFF" />
        </view>

        <view v-if="modalName" class="cu-modal bottom-modal show" @tap="hideModal">
            <view class="cu-dialog" style="height: 60%; border-radius: 10px 10px 0px 0px" @tap.stop.prevent="t">
                <view class="cu-bar bg-white">
                    <view class="action text-green" style="margin-left: 0px"></view>
                    <view class="action text-black">{{ ListInfo.study_repount }}条评论</view>
                    <view class="action text-black cicon-close" @tap="hideModal" style="font-size: 20px"></view>
                </view>
                <scroll-view :scroll-y="true" @scrolltolower="RrplyListNext" class="padding-sl bg-white"
                    style="height: 90%; padding-bottom: 132rpx">
                    <view class="cu-list menu-avatar comment">
                        <view class="cu-item" v-for="(item, r_index) in RrplyList" :key="r_index">
                            <view class="cu-avatar round"
                                :style="'background-image:url(' + item.user_head_sculpture + ');'"></view>

                            <view class="content" style="width: calc(100% - 96rpx - 60rpx - 120rpx - 20rpx)">
                                <view style="color: #7b7d82; font-size: 12px">{{ item.user_nick_name }}</view>
                                <view class="text-black text-content text-df text-left"
                                    style="width: 86%; word-wrap: break-word; word-break: normal">
                                    <rich-text :nodes="item.reply_content"></rich-text>
                                    <image class="now_level" v-if="item.image_part[0]" :src="item.image_part[0]"
                                        :data-src="item.image_part[0]" mode="heightFix"
                                        style="vertical-align: middle; height: 100rpx"
                                        @tap.stop.prevent="previewHuiAndImage"></image>
                                </view>
                                <view class="margin-top-sm flex justify-between" @tap.stop.prevent="AddReply"
                                    :data-id="item.id" :data-key="r_index">
                                    <view style="color: #76777d; font-size: 12px">
                                        <text>{{ item.apter_time }}</text>
                                        <text style="margin-left: 40rpx">回复</text>
                                    </view>
                                </view>
                                <view class="cu-list menu-avatar comment children"
                                    :style="'max-height: ' + item.expandList.length * 100 + 'px;'">
                                    <view class="cu-item" style="padding: 30rpx 30rpx 0rpx 70rpx"
                                        v-for="(ex, e_index) in item.expandList" :key="e_index">
                                        <view class="cu-avatar round sm"
                                            :style="'background-image:url(' + ex.reply_user.user_head_sculpture + ');left: 0;'">
                                        </view>

                                        <view class="content">
                                            <view class="text_num_1" style="color: #7b7d82; font-size: 12px">
                                                <text>{{ ex.reply_user.user_nick_name }}</text>
                                                <block v-if="ex.reply_user_id != 0">
                                                    <text v-if="ex.reply_user_id" style="margin: 0px 5px"
                                                        class="cicon-play-arrow"></text>
                                                    <text>{{ ex.reply_user_reply.user_nick_name }}</text>
                                                </block>
                                            </view>
                                            <view class="text-black text-content text-df text-left" style="width: 86%">
                                                <rich-text :nodes="ex.duplex_content"></rich-text>
                                            </view>
                                            <view class="margin-top-sm flex justify-between"
                                                @tap.stop.prevent="AddReplyReply" :data-index="r_index" :data-id="ex.id"
                                                :data-key="e_index">
                                                <view style="color: #76777d; font-size: 12px">
                                                    <text>{{ ex.duplex_time }}</text>
                                                    <text style="margin-left: 40rpx">回复</text>
                                                </view>
                                            </view>
                                        </view>
                                    </view>
                                </view>
                                <view v-if="item.huifu_huifu_count > 0" @tap.stop.prevent="combination"
                                    :data-id="item.id" :data-key="r_index" class="text-left"
                                    style="margin-top: 10px; color: #8a8b90">
                                    <text class="cicon-move"></text>
                                    <text v-if="item.expandList.length == 0">展开{{ item.huifu_huifu_count }}条回复</text>
                                    <text
                                        v-if="item.expandList.length < item.huifu_huifu_count && item.expandList.length != 0">展开更多回复</text>
                                    <text v-if="item.expandList.length >= item.huifu_huifu_count">收起</text>
                                    <text class="cicon-back"
                                        :style="'transform: rotate(' + (item.expandList.length >= item.huifu_huifu_count ? 90 : 270) + 'deg);font-size: 30rpx;'"></text>
                                </view>
                            </view>

                            <view @tap.stop.prevent="get_huifu_zan" :data-id="item.id" :data-index="r_index"
                                class="text-gray" style="position: absolute; top: 55rpx; text-align: center">
                                <text :class="'cicon-thumb-up-line ' + (item.is_huifu_zan ? 'text-red' : '')"
                                    style="font-size: 20px"></text>
                                <view>{{ item.is_huifu_zan_count }}</view>
                            </view>
                        </view>
                    </view>
                    <view :class="'cu-load ' + (!di_get_reply ? 'loading' : 'over')"></view>
                </scroll-view>
                <view class="cu-bar search bg-white solid-top"
                    :style="'position: absolute;bottom: ' + (emj ? 420 : 30) + 'rpx;width:100%;'">
                    <view class="search-form round" style="height: 80rpx; background-color: #f3f3f4">
                        <input maxlength="100" :value="ReplyText" @input="check_text" @confirm="send_confirm"
                            type="text" :focus="focus" :placeholder="placeholder" confirm-type="send"
                            style="text-align: left; padding-left: 30rpx; height: 80rpx; line-height: 80rpx" />
                        <text v-if="input_col" @tap.stop.prevent="none_focus" class="cicon-close-round text-gray"
                            style="font-size: 25px; padding-right: 10rpx"></text>
                    </view>
                    <view @tap="openExp" class="action">
                        <text :class="'cicon-emoji-o ' + (emj ? 'text-blue' : 'text-black')"
                            style="font-size: 60rpx"></text>
                    </view>
                </view>
                <view v-if="emj"
                    style="width: 100%; height: 400rpx; background-color: #f3f3f3; position: absolute; bottom: 10px">
                    <swiper :indicator-dots="true" style="height: 400rpx">
                        <block v-for="(emojis, t_index) in emj_list" :key="t_index">
                            <swiper-item>
                                <view class="grid col-9 margin-bottom text-center" style="padding-top: 10px">
                                    <view :data-key="t_index" @tap="set_emoji" :data-index="n_index"
                                        style="margin: 5px 0px" v-for="(n_item, n_index) in emojis" :key="n_index">
                                        <image :src="http_root + 'addons/yl_welore/web/static/expression/' + n_item"
                                            style="width: 60rpx; height: 60rpx"></image>
                                    </view>
                                </view>
                            </swiper-item>
                        </block>
                    </swiper>
                </view>
            </view>
        </view>

        <view :class="'cu-modal ' + (BindPhone ? 'show' : '')">
            <view class="cu-dialog">
                <view class="cu-bar bg-white justify-end">
                    <view class="content">微信手机号授权</view>
                </view>
                <view class="bg-white" style="padding: 0rpx 50rpx 50rpx 50rpx">
                    <view style="font-size: 12px" class="text-grey">根据《网络安全法》相关规定，请绑定手机号</view>
                    <view class="padding flex flex-direction" style="margin-top: 20rpx">
                        <button style="height: 80rpx" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber"
                            class="cu-btn round bg-black" @tap="hideModal">
                            微信手机号一键绑定
                        </button>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import mpVideoSwiper from '@/yl_welore/colorui/video-swiper/index';
import phone from '@/yl_welore/util/user_phone/phone';
const app = getApp();
var http = require('../../../util/http.js');

export default {
    components: {
        mpVideoSwiper, phone
    },
    /**
     * 页面的初始数据
     */
    data() {
        return {
            http_root: app.globalData.http_root,
            videoListNewDom: [],
            videoList: [],
            RrplyList: [],
            ReplyText: '',
            page: 1,
            reply_page: 1,
            expand_page: 1,
            id: 0,
            activeId: 0,
            isPlaying: true,
            current: 0,
            playShow: false,
            skip: 0,
            count: 0,
            //数量
            ListInfo: {},
            //当前视频详情
            percent: 0,
            //进度
            ids: '',
            modalName: false,
            di_get_reply: false,
            focus: false,
            placeholder: '评论',
            reply_reply_id: 0,
            reply_id: 0,
            key: 0,
            //0 评论 1评论回复
            BindPhone: false,
            emj: false,
            emj_list: [],
            input_col: false,
            hui_focus: false,
            RrplyListCount: 0,
            systemInfo: {},
            menuButtonBoundingClientRect: {},
            check_user_login: false
        }
    },

    onLoad(op) {
        console.log(op);
        uni.showShareMenu({
            menus: ['shareAppMessage', 'shareTimeline']
        });
        uni.setStorageSync('slidecurrent', 0);
        var that = this;
        that.id = op.id ? op.id : '';
        uni.getSystemInfo({
            success: (res) => {
                that.systemInfo = res;
                that.menuButtonBoundingClientRect = uni.getMenuButtonBoundingClientRect();
                console.log(res);
            }
        });
        var op = uni.getLaunchOptionsSync();
        if (op.scene == 1154) {
            uni.showToast({
                title: '请前往小程序查看',
                icon: 'none',
                duration: 9000000
            });
            return;
        } else {
            this.doIt();
        }
    },
    onShow() {
        var douyin = app.globalData.__PlugUnitScreen('5fb4baf1f25fe251685b526dc8c30b8f');
        if (!douyin) {
            this.BackPage();
        }
    },
    /**
     * 用户点击右上角分享
     */
    onShareAppMessage(d) {
        var info = this.ListInfo;
        var title = info['study_title'] == '' ? info['study_content'] : info['study_title'];
        return {
            title: title,
            path: '/yl_welore/pages/packageF/full_video/index?id=' + info.id,
            imageUrl: info.image_part[0]
        };
    },
    /**
     * 用户点击右上角分享
     */
    onShareTimeline() {
        var info = this.ListInfo;
        var title = info['study_title'] == '' ? info['study_content'] : info['study_title'];
        return {
            title: title,
            query: 'id=' + info.id,
            imageUrl: info.image_part[0]
        };
    },
    methods: {
        openExp() {
            this.emj = !this.emj;
            this.focus = false;
        },
        set_emoji(d) {
            var index = d.currentTarget.dataset.index;
            var t_index = d.currentTarget.dataset.key;
            var str = this.emj_list[t_index][index];
            var k = str.split('.')[0];
            var emj = '[#:' + k + ']';
            this.ReplyText = this.ReplyText + emj;
        },
        previewHuiAndImage(e) {
            var current = e.target.dataset.src;
            uni.previewImage({
                current: current,
                // 当前显示图片的http链接
                urls: [current] // 需要预览的图片http链接列表
            });
        },

        combination(d) {
            console.log(d);
            var index = d.currentTarget.dataset.key;
            var id = d.currentTarget.dataset.id;
            var info = this.RrplyList[index];
            console.log(info);
            if (info.expandList.length == 0) {
                this.expand_reply_list(id, index);
            }
            if (info.expandList.length < info.huifu_huifu_count && info.expandList.length != 0) {
                this.expand_page = this.expand_page + 1;
                this.expand_reply_list(id, index);
            }
            if (info.expandList.length >= info.huifu_huifu_count) {
                this.expand_page = 1;
                this.RrplyList[index].expandList = [];
            }
        },
        RrplyListNext() {
            this.reply_page = this.reply_page + 1;
            this.VideoReply();
        },
        AddReply(d) {
            var index = d.currentTarget.dataset.key;
            var id = d.currentTarget.dataset.id;
            var info = this.RrplyList[index];
            this.input_col = true;
            this.key = 1;
            this.reply_id = id;
            this.focus = true;
            this.placeholder = '回复 ' + info.user_nick_name;
        },
        AddReplyReply(d) {
            console.log(d);
            var t_index = d.currentTarget.dataset.index;
            var index = d.currentTarget.dataset.key;
            var info = this.RrplyList[t_index].expandList[index];
            this.input_col = true;
            this.key = 1;
            this.reply_reply_id = info.id;
            this.reply_id = info.reply_id;
            this.focus = true;
            this.placeholder = '回复 ' + info.reply_user.user_nick_name;
        },
        ReplyReplyDo() {
            uni.showLoading({
                title: '回复中...',
                mask: true
            });
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.reply_id;
            params.and_id = this.reply_reply_id;
            params.duplex_content = this.ReplyText;
            params.name_card = 0;
            var b = app.globalData.api_root + 'User/add_paper_reply_duplex';
            http.POST(b, {
                params: params,
                success: (res) => {
                    uni.hideLoading();
                    that.ReplyText = '';
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none',
                        duration: 2000
                    });
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        },
        expand_reply_list(id, index) {
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = id;
            params.page = this.expand_page;
            var expandList = that.RrplyList[index]['expandList'];
            var b = app.globalData.api_root + 'Polls/get_expand_list';
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    expandList = [...expandList, ...res.data];
                    that.RrplyList[index].expandList = expandList;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        },
        OpenVideoReply() {
            var e = app.globalData.getCache('userinfo');
            if (!e) {
                return;
            }
            this.modalName = true;
            if (this.RrplyList.length > 0) {
                return;
            }
            this.VideoReply();
        },
        VideoReply() {
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.ListInfo.id;
            params.page = this.reply_page;
            var RrplyList = that.RrplyList;
            var b = app.globalData.api_root + 'Polls/get_video_reply';
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        if (res.data.huifu.length == 0 || res.data.huifu.length < 5) {
                            that.di_get_reply = true;
                        }
                        RrplyList = [...RrplyList, ...res.data.huifu];
                        that.RrplyList = RrplyList;
                        that.RrplyListCount = res.data.huifu_count;
                    } else {
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        },
        none_focus() {
            this.input_col = false;
            this.ReplyText = '';
            this.key = 0;
            this.reply_reply_id = 0;
            this.reply_id = 0;
            this.focus = false;
            this.placeholder = '评论';
        },
        check_text(e) {
            console.log(e);
            var v = e.detail.value;
            if (v == '') {
                this.input_col = false;
                this.ReplyText = '';
            } else {
                this.input_col = true;
                this.ReplyText = v;
            }
        },
        send_confirm(d) {
            this.ReplyText = d.detail.value;
            if (this.key == 0) {
                this.ReplyDo();
            } else {
                this.ReplyReplyDo();
            }
        },
        ReplyDo() {
            uni.showLoading({
                title: '回复中...',
                mask: true
            });
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.ListInfo.id;
            params.reply_type = 0;
            params.text = this.ReplyText;
            params.file_ss = 0;
            params.name_card = 0;
            console.log(params);
            var b = app.globalData.api_root + 'User/add_paper_reply_new';
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        uni.hideLoading();
                        uni.showToast({
                            title: '请等待审核！',
                            icon: 'none',
                            duration: 2000
                        });
                    } else if (res.data.status == 'error') {
                        uni.hideLoading();
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg
                        });
                    } else {
                        uni.hideLoading();
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg
                        });
                    }
                    that.ReplyText = '';
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        },
        /**
         * 回复赞
         */
        get_huifu_zan(dd) {
            var hui_id = dd.currentTarget.dataset.id;
            var index = dd.currentTarget.dataset.index;
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.id;
            params.hui_id = hui_id;
            var b = app.globalData.api_root + 'User/add_paper_prely';
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        var RrplyList = that.RrplyList;
                        that.RrplyList[index].is_huifu_zan = RrplyList[index]['is_huifu_zan'] == false ? true : false;
                        that.RrplyList[index].is_huifu_zan_count = RrplyList[index]['is_huifu_zan'] == false ? parseInt(RrplyList[index]['is_huifu_zan_count']) + 1 : parseInt(RrplyList[index]['is_huifu_zan_count']) - 1;
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        },
        hideModal() {
            this.modalName = false;
            this.focus = false;
            this.placeholder = '评论';
            this.emj = false;
        },
        open_url(d) {
            var e = app.globalData.getCache('userinfo');
            if (!e) {
                return;
            }
            this.change();
            var id = d.currentTarget.dataset.id;
            var key = d.currentTarget.dataset.key;
            if (key == 1) {
                console.log(id);
                if (typeof id == 'undefined' || id == 0) {
                    uni.showToast({
                        title: '无法查看',
                        icon: 'none',
                        duration: 2000
                    });
                    return;
                }
                uni.navigateTo({
                    url: '/yl_welore/pages/packageB/my_home/index?id=' + id
                });
            }
            if (key == 2) {
                uni.navigateTo({
                    url: '/yl_welore/pages/gambit/index?id=' + id
                });
            }
            if (key == 3) {
                uni.navigateTo({
                    url: '/yl_welore/pages/packageA/circle_info/index?id=' + id
                });
            }
        },
        change() {
            var vvideo = this.$refs.videoSwiper;
            var current_video = vvideo.data._videoContexts[vvideo.data._last];
            if (this.playShow) {
                current_video.play();
                this.playShow = false;
            } else {
                current_video.pause();
                this.playShow = true;
            }
        },
        onSlide(e) {
            var u = app.globalData.getCache('userinfo');
            if (!u) {
                uni.showToast({
                    title: '更多内容请前往小程序查看',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }
            if (e.detail.current > 7) {
                this.skip = 0;
                this.id = '';
                this.page = 1;
                this.videoList = [];
                this.get_video();
                return;
            }
            this.playShow = false;
            this.RrplyList = [];
            this.reply_page = 1;
            if (e.detail.current === this.videoList.length - 1) {
                let skip = Number(this.skip) + 1;
                if (skip < this.count) {
                    this.skip = skip;
                    this.page = this.page + 1;
                    this.get_video();
                }
            }
            console.log(e.detail.current);
            console.log();
            if (this.videoList.length > 0) {
                this.ListInfo = this.videoList[e.detail.current ? e.detail.current : 0];
                this.activeId = this.videoList[e.detail.current ? e.detail.current : 0]['id'];
            }
        },
        doIt() {
            app.globalData.getLogin(
                // 成功回调 returnA 
                (userInfo) => {
                    console.log(' 登录成功:', userInfo);
                    const do2 = this.BindPhoneFunc();
                    if (do2 == 1) {
                        this.get_video();
                    }
                    this.get_emj_list();
                },
                // 失败回调 returnB 
                (err) => {
                    console.error(' 登录失败:', err);
                }
            );
        },
        get_emj_list() {
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            var b = app.globalData.api_root + 'Polls/get_emj_list';
            http.POST(b, {
                params: params,
                success: (res) => {
                    that.emj_list = res.data;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        },
        BindPhoneFunc() {
            var e = app.globalData.getCache('userinfo');
            if (!e.user_phone) {
                this.BindPhone = true;
                return 0; //执行登陆
            } else {
                return 1; //执行登陆
            }
        },
        open_at() {
            var vvideo = this.$refs.videoSwiper;
            var current_video = vvideo.data._videoContexts[vvideo.data._last];
            current_video.pause();
            this.playShow = true;
            var info = this.ListInfo;
            uni.navigateTo({
                url: '/yl_welore/pages/packageA/article/index?id=' + info.id + '&type=' + info.study_type
            });
        },
        get_video() {
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.id;
            params.page = this.page;
            params.ids = this.ids;
            var videoList = that.videoList;
            var b = app.globalData.api_root + 'Polls/get_full_video';
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        if (res.data.info.length == 0) {
                            uni.showToast({
                                title: '没有更多了..',
                                icon: 'none'
                            });
                            return;
                        }
                        that.count = that.count + res.data.info.length;
                        that.ids = res.data.ids;
                        videoList = [...videoList, ...res.data.info];
                        that.setvideoList(videoList);
                        if (that.skip > 0) {
                            that.videoListNewDom = res.data.info;
                        }
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        },
        setvideoList(videoList) {
            if (this.page == 1) {
                console.log(videoList);
                this.videoList = videoList;
                this.activeId = videoList[0].id;
                this.ListInfo = videoList[0];
            } else {
                this.videoList = videoList;
            }
        },
        onPlay(e) { },
        onPause(e) {
            //  console.log('pause', e.detail.activeId)
        },
        onShowPause(e) {
            this.isPlaying = false;
        },
        onHidePause(e) {
            this.isPlaying = true;
        },
        onEnded(e) { },
        onError(e) { },
        onWaiting(e) { },
        onTimeUpdate(e) {
            //console.log(e);
            var k = ((e.detail.currentTime / e.detail.duration) * 100).toFixed(2);
            this.percent = k;
        },
        onProgress(e) { },
        onLoadedMetaData(e) {
            //console.log('LoadedMetaData', e)
            if (uni.getStorageSync('slidecurrent') === 0) {
                var vvideo = this.$refs.videoSwiper;
                vvideo.data._last = 0; //如果不赋值为0，就不是从第一条播放
                vvideo.data._invalidDown = 1;
                vvideo.playCurrent(0);
            }
        },
        /**
         * 获取手机号
         */
        getPhoneNumber(c) {
            console.log(c);
            if (c.detail.errMsg == 'getPhoneNumber:ok') {
                var b = app.globalData.api_root + 'Service/get_user_phone_new';
                var that = this;
                var e = app.globalData.getCache('userinfo');
                console.log(e);
                var params = new Object();
                params.token = e.token;
                params.openid = e.openid;
                params.uid = e.uid;
                params.code = c.detail.code;
                http.POST(b, {
                    params: params,
                    success: (res) => {
                        console.log(res);
                        if (res.data.status == 'success') {
                            var e = app.globalData.getCache('userinfo');
                            e.user_phone = res.data.phone;
                            console.log(e);
                            app.globalData.setCache('userinfo', e);
                            uni.showToast({
                                title: res.data.msg,
                                icon: 'none',
                                duration: 2000
                            });
                        } else {
                            uni.showModal({
                                title: '提示',
                                content: res.data.msg,
                                showCancel: false,
                                success: (res) => { }
                            });
                        }
                        that.BindPhone = false;
                    },
                    fail: () => {
                        uni.showModal({
                            title: '提示',
                            content: '网络繁忙，请稍候重试！',
                            showCancel: false,
                            success: (res) => { }
                        });
                    }
                });
            } else {
                uni.showModal({
                    title: '提示',
                    content: c.detail.errMsg,
                    showCancel: false,
                    success: (res) => { }
                });
            }
        },
        VideoCollect() {
            var that = this;
            var e = app.globalData.getCache('userinfo');
            if (!e) {
                return;
            }
            if (e.tourist == 1) {
                this.check_user_login = true;
                return;
            }
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.id;
            params.sc_type = this.ListInfo['is_study_collect'] == true ? 1 : 0;
            var info = that.ListInfo;
            if (info['is_study_collect'] == false) {
                that.ListInfo.is_study_collect = true;
                that.ListInfo.study_collect = Number(info['study_collect']) + 1;
            } else {
                that.ListInfo.is_study_collect = false;
                that.ListInfo.study_collect = Number(info['study_collect']) - 1 < 0 ? 0 : Number(info['study_collect']) - 1;
            }
            var b = app.globalData.api_root + 'User/add_user_collect';
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        },
        /**
         * 点赞
         */
        VideoZan() {
            var that = this;
            var e = app.globalData.getCache('userinfo');
            if (!e) {
                return;
            }
            if (e.tourist == 1) {
                this.check_user_login = true;
                return;
            }
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.ListInfo.id;
            params.applaud_type = 0;
            params.zan_type = this.ListInfo['is_info_zan'] == true ? 1 : 0;
            if (this.ListInfo['is_info_zan'] == false) {
                that.ListInfo.is_info_zan = true;
                that.ListInfo.info_zan_count = Number(this.ListInfo['info_zan_count']) + 1;
            } else {
                that.ListInfo.is_info_zan = false;
                that.ListInfo.info_zan_count = Number(this.ListInfo['info_zan_count']) - 1 < 0 ? 0 : Number(this.ListInfo['info_zan_count']) - 1;
            }
            var b = app.globalData.api_root + 'User/add_user_zan';
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        //wx.vibrateShort();
                        // list[key]['is_info_zan'] = res.data.info_zan;
                        //that.rotate3d(key);
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        },
        BackPage() {
            var pages = getCurrentPages();
            var Page = pages[pages.length - 1]; //当前页
            var prevPage = pages[pages.length - 2]; //上一个页面
            if (pages.length == 1) {
                this.toHome();
                return;
            }
            uni.navigateBack();
        },
        toHome() {
            uni.reLaunch({
                url: '/yl_welore/pages/index/index'
            });
        }
    },
};
</script>
<style>
page {
    background-color: black;
    height: 100%;
}

mp-video-swiper {
    width: 100%;
    height: 100%;
}

.imagePlayer {
    width: 40px;
    height: 40px;
    position: fixed;
    z-index: 1000;
    opacity: 0.6;
}

.viewFloat {
    position: fixed;
    display: flex;
    z-index: 100;
    width: 530rpx;
    bottom: 160rpx;
    color: white;
    margin: 20rpx;
    flex-direction: column;
}

.videoGambit {
    border-radius: 5px;
    font-size: 14px;
    font-weight: 400;
    background-color: rgba(0, 0, 0, 0.1);
    width: max-content;
    height: 30px;
    line-height: 30px;
}

.videoTitle {
    font-size: 16px;
    font-weight: 600;
    margin-top: 10rpx;
}

.videoDes {
    margin-top: 10rpx;
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 2px;
}

.viewTabContainer {
    position: fixed;
    display: flex;
    justify-content: space-around;
    align-items: center;
    flex-direction: column;
    bottom: 210rpx;
    right: 20rpx;
}

.viewTab {
    color: white;
    margin-top: 35rpx;
    text-align: center;
}

.icon_bottom {
    display: block;
    font-size: 12px;
}

.progress-box {
    position: fixed;
    bottom: 80rpx;
    width: 100%;
    z-index: 10;
}

.share_chick {
    font-size: 60rpx;
    transform: rotateY(180deg);
}

button::after {
    line-height: normal;
    font-size: 30rpx;
    width: 0;
    height: 0;
    top: 0;
    left: 0;
}

button {
    line-height: normal;
    display: block;
    padding-left: 0px;
    padding-right: 0px;
    background-color: rgba(255, 255, 255, 0);
    font-size: 30rpx;
    overflow: inherit;
}

.children {
    max-height: 0px;
    transition: max-height 0.3s;
    overflow: hidden;
}
</style>

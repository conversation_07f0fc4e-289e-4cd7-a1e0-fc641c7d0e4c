<template>
    <view>
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
            <view slot="backText"></view>
            <view slot="content" style="color: #000000; font-weight: 600; font-size: 36rpx">中奖者名单</view>
        </cu-custom>
        <view style="padding: 30rpx">
            <view style="color: #5c5c5c">中奖者名单</view>
            <view style="border: 1px #d7d7d7 solid; margin-top: 20rpx" v-for="(item, index) in info" :key="index">
                <view style="padding: 20rpx; text-align: center; background-color: #f7f7f7">
                    <text>{{ item.prizeLevel }}</text>
                    <text>等奖：</text>
                    <text style="margin-left: 10rpx">{{ item.prizeName }}</text>
                </view>

                <view v-if="!item.user_list || item.user_list.length === 0" class="grid col-1 margin-bottom margin-top text-center">
                    <view style="color: #999999">无人中奖</view>
                </view>

                <view
                    v-else
                    class="grid margin-bottom margin-top text-center"
                    :class="{
                        'col-1': item.user_list.length === 1,
                        'col-2': item.user_list.length === 2,
                        'col-3': item.user_list.length > 2
                    }"
                >
                    <view v-for="(u, index1) in item.user_list" :key="index1">
                        <image :src="u.user_head_sculpture" style="border-radius: 50%; width: 100rpx; height: 100rpx"></image>
                        <view>{{ u.user_nick_name }}</view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import http from '../../../util/http.js';
const app = getApp();

export default {
    data() {
        return {
            id: 0,
            info: {}
        };
    },

    onLoad(options) {
        this.id = options.id;
        this.get_info();
    },

    onShow() {
        var check = app.globalData.__PlugUnitScreen('7e1d82d91e04523ae2825c1d2991d5d6');
        if (!check) {
            uni.showToast({
                title: '未开通插件',
                icon: 'none',
                duration: 2000
            });
            return;
        }
    },

    onHide() {},

    onUnload() {},

    onPullDownRefresh() {},

    onReachBottom() {},

    onShareAppMessage() {},

    methods: {
        //获取活动
        get_info() {
            var b = app.globalData.api_root + 'Retrieval/get_prize_info';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.id;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    this.info = res.data;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },
        //获取活动
        get_raffle() {
            return new Promise((resolve, reject) => {
                var b = app.globalData.api_root + 'Retrieval/info';
                var e = app.globalData.getCache('userinfo');
                var params = new Object();
                params.token = e.token;
                params.openid = e.openid;
                params.id = this.id;
                http.POST(b, {
                    params: params,
                    success: (res) => {
                        resolve(res);
                        console.log(res);
                        if (res.data.code == 0) {
                            this.info = res.data.info;
                        } else {
                            uni.showModal({
                                title: '提示',
                                content: res.data.msg,
                                showCancel: false,
                                success: function (res) {}
                            });
                        }
                    },
                    fail: () => {
                        uni.showModal({
                            title: '提示',
                            content: '网络繁忙，请稍候重试！',
                            showCancel: false,
                            success: function (res) {}
                        });
                    }
                });
            });
        }
    }
};
</script>
<style>
page {
    background-color: #ffffff;
}
</style>

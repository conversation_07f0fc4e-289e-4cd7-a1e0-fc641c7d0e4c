<template>
    <view>
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
            <view slot="backText"></view>
            <view slot="content" style="color: #000000; font-weight: 600; font-size: 34rpx">失物招领使用规则</view>
        </cu-custom>
        <view style="padding: 15px">
            <rich-text :user-select="true" :nodes="help_document"></rich-text>
        </view>
    </view>
</template>

<script>
const app = getApp();
var http = require('../../../util/http.js');

export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            config: {},
            help_document: ''
        }
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.getLostConfig();
    },
    methods: {
        getLostConfig() {
            var b = app.globalData.api_root + 'Lost/getLostConfig';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            http.POST(b, {
                params: params,
                success: (res) => {
                    var key = this.formatRichText(res.data.help_document);
                    this.help_document = key;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },
        /**
         * 处理富文本里的图片宽度自适应
         */
        formatRichText(html) {
            let newContent = html.replace(/<img[^>]*>/gi, (match, capture) => {
                match = match.replace(/style="[^"]+"/gi, '').replace(/style='[^']+'/gi, '');
                match = match.replace(/width="[^"]+"/gi, '').replace(/width='[^']+'/gi, '');
                match = match.replace(/height="[^"]+"/gi, '').replace(/height='[^']+'/gi, '');
                return match;
            });
            newContent = newContent.replace(/style="[^"]+"/gi, (match, capture) => {
                match = match.replace(/width:[^;]+;/gi, 'max-width:100%;').replace(/width:[^;]+;/gi, 'max-width:100%;');
                return match;
            });
            newContent = newContent.replace(/<br[^>]*\/>/gi, '');
            newContent = newContent.replace(/\<img/gi, '<img style="max-width:100%;height:auto;;"');
            return newContent;
        }
    }
}
</script>
<style>
page {
    background-color: #ffffff;
}
rich-text {
    line-height: 50rpx;
}
</style>

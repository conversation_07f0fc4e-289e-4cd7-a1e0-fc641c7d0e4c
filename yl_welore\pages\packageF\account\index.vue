<template>
    <view class="page-container">
        <cu-custom bgColor="none" :isSearch="false" :isBack="true" style="color: #000000;">
            <view slot="backText">返回</view>
            <view slot="content" class="header-title">模板消息</view>
        </cu-custom>
        <view class="main-content-card">
            <view class="wechat-icon-section">
                <view class="icon-background">
                    <text class="cuIcon-weixin wechat-icon"></text>
                </view>
            </view>
            <view class="content-title">💬 绑定公众号模板消息</view>
            <view class="steps-section">
                <view class="step-item">👀 1.请先关注公众号</view>
                <view class="step-item">🔗 2.点击绑定按钮</view>
            </view>
            <view class="description-section">
                <view class="description-text">绑定公众号消息有利于接收小程序的互动消息！</view>
                <view class="description-text">让您每个消息不错过！ 📬</view>
            </view>
            <view class="qrcode-section">
                <view class="app-name">{{ info.wx_app_name }}</view>
                <view class="qrcode-container">
                    <image :show-menu-by-longpress="true" class="qrcode-image" :src="info.wx_app_qrcode"></image>
                </view>
                <view class="qrcode-tip">📱 (长按识别二维码)</view>
            </view>
            <view class="button-section">
                <button v-if="info.popular == 0" @tap="open_url" class="bind-button unbind-state">🔗 我要绑定</button>
                <button v-if="info.popular == 1" @tap="open_url" class="bind-button bind-state">✅ 已绑定</button>
            </view>
            <view @tap="clean" class="unbind-link">🔓 解绑</view>
        </view>
    </view>
</template>

<script>
const app = getApp();
var http = require('../../../util/http.js');

export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            http_root: app.globalData.http_root,
            info: ''
        }
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {},
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {
        this.get_account();
    },
    methods: {
        open_url() {
            uni.navigateTo({
                url: '/yl_welore/pages/packageF/account_do/index?wx_app_id=' + this.info.wx_app_id
            });
        },
        get_account() {
            var b = app.globalData.api_root + 'Wechat/get_account';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.code == 0) {
                        this.info = res.data.info;
                    } else {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: () => {
                                uni.navigateBack();
                            }
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: () => {}
                    });
                }
            });
        },
        copy_name() {
            var that = this;
            uni.previewMedia({
                sources: [
                    {
                        url: that.info.wx_app_qrcode,
                        type: 'image'
                    }
                ],
                showmenu: true
            });
            // wx.setClipboardData({
            //     data: this.info.wx_app_name,
            //     success: (res) => {}
            // });
        },

        clean() {
            var that = this;
            uni.showModal({
                title: '提示',
                content: '确定要解绑吗？',
                success(res) {
                    if (res.confirm) {
                        var b = app.globalData.api_root + 'Wechat/clean_account';
                        var e = app.globalData.getCache('userinfo');
                        var params = new Object();
                        params.token = e.token;
                        params.openid = e.openid;
                        http.POST(b, {
                            params: params,
                            success: (res) => {
                                console.log(res);
                                if (res.data.code == 0) {
                                    uni.showToast({
                                        title: res.data.msg,
                                        icon: 'none',
                                        duration: 2000
                                    });
                                } else {
                                    uni.showToast({
                                        title: res.data.msg,
                                        icon: 'none',
                                        duration: 2000
                                    });
                                }
                                that.get_account();
                            },
                            fail: () => {
                                uni.showModal({
                                    title: '提示',
                                    content: '网络繁忙，请稍候重试！',
                                    showCancel: false,
                                    success: () => {}
                                });
                            }
                        });
                    }
                }
            });
        }
    }
};
</script>
<style>
page {
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 50%, #f0f8ff 100%);
    min-height: 100vh;
}

.page-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.header-title {
    color: #333333;
    font-weight: 600;
    font-size: 36rpx;
}

.official-account-section {
    margin: 20rpx;
    border-radius: 24rpx;
    overflow: hidden;
    box-shadow: 0 8rpx 32rpx rgba(28, 187, 180, 0.15);
}

.non-weixin-tip {
    background: #ffffff;
    padding: 30rpx;
    text-align: center;
    color: #666666;
    font-size: 28rpx;
    border-radius: 24rpx;
}

.main-content-card {
    background: #ffffff;
    margin: 20rpx;
    border-radius: 24rpx;
    padding: 40rpx 30rpx;
    box-shadow: 0 12rpx 40rpx rgba(28, 187, 180, 0.12);
    display: flex;
    flex-direction: column;
    align-items: center;
}

.wechat-icon-section {
    margin-bottom: 30rpx;
}

.icon-background {
    width: 160rpx;
    height: 160rpx;
    background: linear-gradient(135deg, rgba(28, 187, 180, 0.1) 0%, rgba(54, 207, 201, 0.2) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 12rpx 30rpx rgba(28, 187, 180, 0.2);
    animation: pulse 3s infinite;
}

.wechat-icon {
    font-size: 120rpx;
    color: #1cbbb4;
}

.content-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333333;
    margin-bottom: 30rpx;
}

.steps-section {
    width: 100%;
    margin: 20rpx 0;
}

.step-item {
    font-size: 30rpx;
    color: #333333;
    margin: 16rpx 0;
    text-align: center;
}

.description-section {
    margin: 20rpx 0;
    width: 100%;
}

.description-text {
    font-size: 28rpx;
    color: #999999;
    text-align: center;
    margin: 10rpx 0;
}

.qrcode-section {
    margin: 30rpx 0;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.app-name {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
    margin-bottom: 20rpx;
}

.qrcode-container {
    padding: 20rpx;
    background: #ffffff;
    border-radius: 20rpx;
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
    border: 2rpx solid rgba(28, 187, 180, 0.2);
}

.qrcode-image {
    width: 250rpx;
    height: 250rpx;
}

.qrcode-tip {
    font-size: 24rpx;
    color: #999999;
    margin-top: 16rpx;
}

.button-section {
    width: 100%;
    margin: 30rpx 0;
}

.bind-button {
    width: 80%;
    height: 90rpx;
    border-radius: 45rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    font-weight: 600;
    box-shadow: 0 12rpx 30rpx rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.unbind-state {
    background: linear-gradient(135deg, #1cbbb4 0%, #36cfc9 100%);
    color: #ffffff;
}

.bind-state {
    background: linear-gradient(135deg, #8799a3 0%, #a5b1c2 100%);
    color: #ffffff;
}

.unbind-link {
    font-size: 28rpx;
    color: #8799a3;
    margin-top: 20rpx;
    padding: 10rpx 30rpx;
    border-radius: 30rpx;
    background: rgba(135, 153, 163, 0.1);
    transition: all 0.3s ease;
}

.unbind-link:active {
    transform: scale(0.95);
    background: rgba(135, 153, 163, 0.2);
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}
</style>

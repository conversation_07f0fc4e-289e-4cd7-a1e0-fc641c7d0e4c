<template>
    <view>
        <cu-custom bgColor="none" :isSearch="false" :isBack="true" :uid="false">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">详情</view>
        </cu-custom>
        <view style="padding: 20rpx">
            <view class="main-card">
                <view class="user-info-header">
                    <view class="flex justify-between align-center">
                        <view class="flex justify-start align-center">
                            <view class="avatar-container">
                                <image class="user-avatar" :src="info.reply_head"></image>
                                <text class="avatar-emoji">✨</text>
                            </view>
                            <view style="margin-left: 15px">
                                <view class="username-text">
                                    <text>👤 {{ info.temp_name }}</text>
                                </view>
                                <view class="time-text">
                                    <text>🕐 {{ info.send_time }}</text>
                                </view>
                            </view>
                        </view>
                        <view v-if="item.re_user_id == 1 || info.admin == 1" class="more-btn">
                            <text @tap="open_di" class="cicon-more"></text>
                        </view>
                    </view>
                </view>
                <view class="content-section">
                    <view v-if="info.user_id != 0" @tap.stop.prevent="open_user" :data-id="info.user_id"
                        class="mention-user">
                        <text class="cicon-at-line"></text>
                        <text>{{ info.user_name }}</text>
                    </view>
                    <view class="main-content">
                        <rich-text class="content-text" :nodes="info.content"></rich-text>
                    </view>
                    <view style="margin-top: 20rpx">
                        <!-- 1 -->
                        <view style="width: 30%" v-if="info.image_part.length == 1 && img != ''"
                            v-for="(img, img_index) in info.image_part" :key="img_index">
                            <image class="now_level" :lazy-load="true" :src="img" @tap.stop.prevent="Preview"
                                :data-src="img" style="border-radius: 3px; width: 100%" mode="widthFix"></image>
                        </view>
                        <!-- 1 -->
                        <!-- 2 -->
                        <view style="width: 30%; float: left; text-align: center" v-if="info.image_part.length == 2"
                            v-for="(img, img_index) in info.image_part" :key="img_index">
                            <image class="now_level" :lazy-load="true" v-if="img_index == 0" :src="img"
                                @tap.stop.prevent="Preview" :data-src="img" style="width: 100%; border-radius: 5px"
                                mode="widthFix"></image>

                            <image class="now_level" :lazy-load="true" v-if="img_index == 1" :src="img"
                                @tap.stop.prevent="Preview" :data-src="img"
                                style="width: 100%; border-radius: 5px; padding-left: 5px" mode="widthFix"></image>
                        </view>
                        <!-- 2 -->
                        <!-- 3 -->
                        <view class="grid col-3 text-center">
                            <block v-if="info.image_part.length > 2" v-for="(img, img_index) in info.image_part"
                                :key="img_index">
                                <view style="text-align: center">
                                    <image class="now_level" :lazy-load="true" :src="img" @tap.stop.prevent="Preview"
                                        :data-src="img" style="border-radius: 5px; width: 100%" mode="widthFix"></image>
                                </view>
                            </block>
                        </view>
                    </view>
                </view>
                <view style="clear: both; height: 0"></view>
                <view class="interaction-section">
                    <view class="praise-count">
                        <text class="praise-emoji">👍</text>
                        <text class="praise-text">{{ info.praise_number }} 赞</text>
                    </view>
                    <view class="action-buttons">
                        <view class="action-btn" @tap="InputFocus">
                            <text class="btn-emoji">💬</text>
                        </view>
                        <view class="action-btn">
                            <text class="btn-emoji">📤</text>
                        </view>
                        <view class="action-btn like-btn" @tap="add_zan" :class="info.applaud == 1 ? 'liked' : ''">
                            <text v-if="info.applaud == 1" class="btn-emoji">❤️</text>
                            <text v-if="info.applaud == 0" class="btn-emoji">🤍</text>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view style="padding: 0 20rpx">
            <view class="comment-header-card">
                <view class="comment-header">
                    <text class="comment-icon">💬</text>
                    <text class="comment-title">评论</text>
                    <text class="comment-count">{{ info.reply_content }}</text>
                </view>
            </view>
        </view>
        <view class="comment-list">
            <!-- 评论列表 -->
            <view class="comment-item" v-for="(item, index) in list" :key="index">
                <view class="comment-avatar-container">
                    <image class="comment-avatar" :src="item.reply_head"></image>
                </view>

                <view class="comment-content">
                    <view class="comment-header-info">
                        <text class="comment-username">{{ item.temp_name }}</text>
                    </view>
                    <view class="comment-text">
                        <rich-text :nodes="item.re_content"></rich-text>
                    </view>
                    <view class="comment-footer">
                        <view class="comment-time">
                            <text class="time-icon">🕐</text>
                            <text>{{ item.reply_time }}</text>
                        </view>
                        <view v-if="item.re_user_id == 1 || info.admin == 1" @tap="del_reply" :data-id="item.id"
                            class="delete-btn">
                            <text class="delete-icon">🗑️</text>
                            <text>删除</text>
                        </view>
                    </view>
                </view>
            </view>


        </view>


        <view class="input-bar" :style="'padding-bottom:' + bottom + 'px;'">
            <view class="input-container">
                <text class="input-emoji">💭</text>
                <input class="comment-input" :value="get_text" placeholder="对此你怎么看，评论区见" @tap="InputFocus"
                    @input="get_reply_text" :adjust-position="false" @keyboardheightchange="abc"
                    :show-confirm-bar="false" :focus="value_false" maxlength="300" cursor-spacing="10" />
            </view>

            <view class="emoji-btn" @tap="openPhEmoji" :class="emoji ? 'active' : ''">

                <text class="emoji-text">😀</text>
            </view>
            <view class="send-btn" @tap="add_hui">
                <text class="send-emoji">🚀</text>
            </view>
        </view>
        <view v-if="emoji"
            style="width: 100%; height: 400rpx; background-color: #f3f3f3; position: fixed; bottom: 30rpx; z-index: 1000">
            <swiper :indicator-dots="true" style="height: 400rpx">
                <block v-for="(emojis, t_index) in emj_list" :key="t_index">
                    <swiper-item>
                        <view class="grid col-9 margin-bottom text-center" style="padding-top: 10px">
                            <view @tap="set_emoji" :data-key="t_index" :data-index="n_index" style="margin: 5px 0px"
                                v-for="(n_item, n_index) in emojis" :key="n_index">
                                <image :src="http_root + 'addons/yl_welore/web/static/expression/' + n_item"
                                    style="width: 60rpx; height: 60rpx"></image>
                            </view>
                        </view>
                    </swiper-item>
                </block>
            </swiper>
        </view>
        <!-- 空状态 -->
        <view v-if="list.length === 0" class="empty-state">
            <view class="empty-icon">💭</view>
            <view class="empty-title">还没有评论</view>
            <view class="empty-desc">快来发表第一条评论吧～</view>
        </view>
        <view v-if="list.length > 0" :class="'cu-load ' + (!di_msg ? 'loading' : 'over')" style="padding: 0px 0px 80px 0px"></view>
    </view>
</template>

<script>
const app = getApp();
var http = require('../../../util/http.js');
export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            http_root: app.globalData.http_root,
            info: {},
            list: [],
            page: 1,
            emoji: false,
            value_false: false,
            emj_list: [],
            get_text: '',
            input_higth: false,
            bottom: 25,
            id: null,
            di_msg: false,
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(op) {
        uni.hideShareMenu();
        var subscribe = app.globalData.getCache('subscribe');
        if (!subscribe) {
            app.globalData.subscribe_message(
                (res) => {
                    //请求成功的回调函数
                    console.log(res);
                    if (res == '') {
                        return;
                    }
                    app.globalData.setCache('subscribe', res.parallelism_data);
                },
                () => {
                    //请求失败的回调函数，不需要时可省略
                }
            );
        }
        this.id = op.id;
        this.get_secret_info();
        this.getExpEmjList();
        this.get_reply();
    },

    /**
     * 加载下一页
     */
    onReachBottom() {
        this.page = this.page + 1;
        this.get_reply();
    },

    methods: {
        open_di() {
            uni.showActionSheet({
                itemList: ['删除'],
                success: (res) => {
                    uni.showModal({
                        title: '提示',
                        content: '确定要删除吗？',
                        success: (res) => {
                            if (res.confirm) {
                                this.del_my_dd();
                            }
                        }
                    });
                }
            });
        },
        del_my_dd() {
            var b = app.globalData.api_root + 'Whisper/del_my_secret';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.id;
            http.POST(b, {
                params: params,
                success: (res) => {
                    if (res.data.code == 0) {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                        setTimeout(() => {
                            //获取页面栈
                            let pages = getCurrentPages();
                            //获取所需页面
                            let prevPage = pages[pages.length - 2]; //上一页
                            prevPage.setData({
                                onload: 1 //你需要传过去的数据
                            });

                            uni.navigateBack();
                        }, 1000);
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) { }
                    });
                }
            });
        },
        open_user(e) {
            var id = e.currentTarget.dataset.id;
            uni.navigateTo({
                url: '/yl_welore/pages/packageB/my_home/index?id=' + id
            });
        },

        add_hui() {
            uni.showLoading({
                title: '回复中...',
                mask: true
            });
            var b = app.globalData.api_root + 'Whisper/secret_hui';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.hui = this.get_text;
            params.id = this.id;
            http.POST(b, {
                params: params,
                success: (res) => {
                    this.get_text = '';
                    this.page = 1;
                    this.list = [];
                    this.get_reply();
                    uni.hideLoading();
                    uni.showModal({
                        title: '提示',
                        content: res.data.msg,
                        success: (res) => {
                            if (res.confirm) {
                                this.get_subscribe();
                            } else if (res.cancel) {
                                this.get_subscribe();
                            }
                        }
                    });
                },
                fail: () => {
                    uni.hideLoading();
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) { }
                    });
                }
            });
        },
        get_subscribe() {
            var subscribe = app.globalData.getCache('subscribe');
            if (subscribe && subscribe['YL0001'] && subscribe['YL0009'] && subscribe['YL0004']) {
                app.globalData.authorization(subscribe['YL0001'], subscribe['YL0009'], subscribe['YL0004'], (res) => { });
            }
        },
        /**
         * 获取留言内容
         */
        get_reply_text(c) {
            this.get_text = c.detail.value;
        },
        abc(d) {
            var height = d.detail.height;
            if (height > 0) {
                this.bottom = height + 5;
            }
            if (!this.emoji && height == 0) {
                this.bottom = 25;
            }
        },
        InputFocus(e) {
            this.value_false = true;
            this.emoji = false;
        },
        openPhEmoji() {
            this.emoji = !this.emoji;
            this.value_false = false;
            if (this.emoji) {
                this.bottom = 230;
            } else {
                this.bottom = 25;
            }
        },
        set_emoji(d) {
            var index = d.currentTarget.dataset.index;
            var t_index = d.currentTarget.dataset.key;
            var str = this.emj_list[t_index][index];
            var k = str.split('.')[0];
            console.log(k);
            this.get_text = this.get_text + '[#:' + k + ']';
        },
        // 预览图片
        Preview(e) {
            var src = e.currentTarget.dataset.src;
            var info = this.info;
            uni.previewImage({
                current: src,
                //当前预览的图片
                urls: info.image_part //所有要预览的图片数组
            });
        },

        get_reply() {
            var b = app.globalData.api_root + 'Whisper/get_reply';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.id;
            params.page = this.page;
            http.POST(b, {
                params: params,
                success: (res) => {
                    if (res.data.length == 0 || res.data.length < 4) {
                        this.di_msg = true;
                    }
                    this.list.push(...res.data);
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) { }
                    });
                }
            });
        },
        getExpEmjList() {
            var b = app.globalData.api_root + 'Polls/get_emj_list';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            http.POST(b, {
                params: params,
                success: (res) => {
                    this.emj_list = res.data;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) { }
                    });
                }
            });
        },
        /**
         * 全部
         */
        get_secret_info() {
            var b = app.globalData.api_root + 'Whisper/get_secret_info';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.id;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res.data);
                    this.info = res.data;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) { }
                    });
                }
            });
        },
        del_reply_do(id) {
            var b = app.globalData.api_root + 'Whisper/del_reply';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = id;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res.data);
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none',
                        duration: 1500
                    });
                    if (res.data.code == 0) {
                        this.list = [];
                        this.page = 1;
                        this.get_reply();
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) { }
                    });
                }
            });
        },
        del_reply(item) {
            uni.showModal({
                title: '提示',
                content: '确定要删除回复吗？',
                success: (res) => {
                    if (res.confirm) {
                        this.del_reply_do(item.currentTarget.dataset.id);
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                }
            });
        },
        add_zan(item) {
            var list_a = 'info.applaud';
            var list_c = 'info.praise_number';
            var info = this.info;
            console.log(info);
            if (info['applaud'] == 0) {
                this.$set(this.info, 'applaud', 1);
                this.$set(this.info, 'praise_number', info['praise_number'] + 1);
            } else {
                this.$set(this.info, 'applaud', 0);
                this.$set(this.info, 'praise_number', info['praise_number'] - 1);
            }
            var b = app.globalData.api_root + 'Whisper/secret_zan';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = info.id;
            http.POST(b, {
                params: params,
                success: (res) => { },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) { }
                    });
                }
            });
        }
    }
};
</script>
<style>
page {
    background-color: #f8fafb;
    min-height: 100vh;
}

/* 主卡片样式 */
.main-card {
    background: #ffffff;
    border-radius: 16rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
    padding: 24rpx;
    border: 1px solid #f0f2f5;
}

/* 用户信息头部 */
.user-info-header {
    background: #ffffff;
    border-radius: 12rpx;
    padding: 20rpx;
    margin: -24rpx -24rpx 24rpx -24rpx;
    position: relative;
    box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.05);
}

.avatar-container {
    position: relative;
    display: inline-block;
}

.user-avatar {
    width: 72rpx;
    height: 72rpx;
    border-radius: 50%;
    border: 2px solid #4facfe;
    box-shadow: 0 2rpx 8rpx rgba(79, 172, 254, 0.15);
}

.avatar-emoji {
    position: absolute;
    bottom: -4rpx;
    right: -4rpx;
    font-size: 20rpx;
    background: #ffd93d;
    border-radius: 50%;
    width: 28rpx;
    height: 28rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid white;
    box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
}

.username-text {
    color: #333;
    font-size: 30rpx;
    font-weight: 600;
    margin-bottom: 6rpx;
}

.time-text {
    color: #6c757d;
    font-size: 22rpx;
}

.more-btn {
    background: #f8f9fa;
    border-radius: 50%;
    width: 56rpx;
    height: 56rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #e9ecef;
}

.more-btn text {
    color: #6c757d;
    font-size: 32rpx;
}

/* 内容区域 */
.content-section {
    margin-top: 24rpx;
}

.mention-user {
    background: #4facfe;
    color: white;
    padding: 12rpx 20rpx;
    border-radius: 40rpx;
    margin-bottom: 20rpx;
    display: inline-block;
    font-size: 26rpx;
    box-shadow: 0 2rpx 8rpx rgba(79, 172, 254, 0.2);
}

.main-content {
    position: relative;
    background: #fafbfc;
    border-radius: 12rpx;
    padding: 24rpx;
    margin-top: 16rpx;
}

.content-emoji {
    position: absolute;
    top: -8rpx;
    left: 16rpx;
    font-size: 28rpx;
    background: #4facfe;
    border-radius: 50%;
    width: 40rpx;
    height: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: 0 2rpx 6rpx rgba(79, 172, 254, 0.3);
}

.content-text {
    color: #333;
    line-height: 1.7;
    font-size: 28rpx;
    letter-spacing: 0.5rpx;
    margin-top: 16rpx;
}

/* 交互区域 */
.interaction-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx 0;
    border-top: 1px solid #f0f2f5;
    margin-top: 24rpx;
}

.praise-count {
    display: flex;
    align-items: center;
    background: #fff3cd;
    padding: 12rpx 20rpx;
    border-radius: 40rpx;
    border: 1px solid #ffeaa7;
}

.praise-emoji {
    font-size: 24rpx;
    margin-right: 6rpx;
}

.praise-text {
    color: #856404;
    font-weight: 500;
    font-size: 26rpx;
}

.action-buttons {
    display: flex;
    gap: 16rpx;
}

.action-btn {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 40rpx;
    padding: 16rpx 24rpx;
    display: flex;
    align-items: center;
    gap: 6rpx;
    transition: all 0.2s ease;
    box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.04);
}

.action-btn:active {
    transform: translateY(1rpx);
    background: #f8f9fa;
}

.like-btn.liked {
    background: #ffe6e6;
    border-color: #ff9999;
    color: #d63384;
}

.btn-emoji {
    font-size: 20rpx;
}

.share-icon {
    transform: rotate(90deg);
}

/* 评论标题卡片 */
.comment-header-card {
    background: #ffffff;
    border-radius: 16rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
    margin-bottom: 20rpx;
}

.comment-header {
    padding: 20rpx 24rpx;
}

.comment-icon {
    font-size: 32rpx;
    margin-right: 12rpx;
}

.comment-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-right: 12rpx;
}

.comment-count {
    font-size: 24rpx;
    color: #6c757d;
    background: #f8f9fa;
    padding: 6rpx 12rpx;
    border-radius: 16rpx;
    border: 1px solid #e9ecef;
}

/* 评论列表 */
.comment-list {
    padding: 0 20rpx;
}

.comment-item {
    display: flex;
    margin-bottom: 24rpx;
    background: #ffffff;
    border-radius: 16rpx;
    padding: 24rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
    border: 1px solid #f0f2f5;
    position: relative;
}

.comment-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
    background: #4facfe;
    border-radius: 2rpx 0 0 2rpx;
}

.comment-avatar-container {
    position: relative;
    margin-right: 20rpx;
    flex-shrink: 0;
}

.comment-avatar {
    width: 72rpx;
    height: 72rpx;
    border-radius: 50%;
    border: 2px solid #e9ecef;
    box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08);
}

.comment-avatar-emoji {
    position: absolute;
    bottom: -6rpx;
    right: -6rpx;
    font-size: 20rpx;
    background: #ffd93d;
    border-radius: 50%;
    width: 28rpx;
    height: 28rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid white;
    box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
}

.comment-content {
    flex: 1;
}

.comment-header-info {
    display: flex;
    align-items: center;
    margin-bottom: 12rpx;
}

.comment-username {
    font-size: 28rpx;
    font-weight: 500;
    color: #4facfe;
    margin-right: 8rpx;
}

.user-badge {
    font-size: 18rpx;
}

.comment-text {
    color: #333;
    line-height: 1.6;
    font-size: 26rpx;
    margin-bottom: 16rpx;
    background: #fafbfc;
    padding: 16rpx;
    border-radius: 8rpx;
}

.comment-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.comment-time {
    display: flex;
    align-items: center;
    color: #6c757d;
    font-size: 22rpx;
}

.time-icon {
    margin-right: 6rpx;
    font-size: 18rpx;
}

.delete-btn {
    display: flex;
    align-items: center;
    background: #fff5f5;
    color: #dc3545;
    padding: 8rpx 16rpx;
    border-radius: 16rpx;
    font-size: 22rpx;
    border: 1px solid #f5c6cb;
}

.delete-icon {
    margin-right: 6rpx;
    font-size: 18rpx;
}

/* 空状态样式 */
.empty-state {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 60rpx 40rpx;
    margin: 0 20rpx 24rpx 20rpx;
    text-align: center;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
    border: 1px solid #f0f2f5;
}

.empty-icon {
    font-size: 80rpx;
    margin-bottom: 24rpx;
    opacity: 0.6;
}

.empty-title {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
    margin-bottom: 16rpx;
}

.empty-desc {
    font-size: 26rpx;
    color: #6c757d;
    line-height: 1.5;
}

/* 输入框区域 */
.input-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #ffffff;
    padding: 16rpx 32rpx;
    display: flex;
    align-items: center;
    gap: 16rpx;
    box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.08);
    border-top: 1px solid #f0f2f5;
    z-index: 1000;
}

.input-container {
    flex: 1;
    background: #f8f9fa;
    border-radius: 40rpx;
    padding: 16rpx 24rpx;
    display: flex;
    align-items: center;
    border: 1px solid #e9ecef;
}

.input-emoji {
    font-size: 28rpx;
    margin-right: 12rpx;
}

.comment-input {
    flex: 1;
    font-size: 26rpx;
    color: #333;
    background: transparent;
    border: none;
    outline: none;
}

.emoji-btn,
.send-btn {
    background: #4facfe;
    border-radius: 50%;
    width: 72rpx;
    height: 72rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: none;
    transition: all 0.2s ease;
    box-shadow: 0 2rpx 6rpx rgba(79, 172, 254, 0.2);
}

.emoji-btn:active,
.send-btn:active {
    transform: scale(0.95);
    background: #3498db;
}

.emoji-btn.active {
    background: #00f2fe;
}

.emoji-btn text,
.send-btn text {
    color: white;
    font-size: 28rpx;
    line-height: 1;
}

.emoji-text,
.send-emoji {
    font-size: 18rpx;
    margin-top: 2rpx;
}
</style>

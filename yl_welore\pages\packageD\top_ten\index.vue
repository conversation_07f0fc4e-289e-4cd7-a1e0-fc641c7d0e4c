<template>
    <view>
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">{{ info.custom_title }}</view>
        </cu-custom>
        <swiper v-if="banner.length > 0" style="height: 222rpx; margin-bottom: 15rpx" :circular="true" :autoplay="true" interval="8000">
            <swiper-item v-for="(item, index) in banner" :key="index">
                <image
                    @tap="open_navigator"
                    :data-src="item.img_url"
                    :data-path="item.appid"
                    :data-type="item.jump_type"
                    :data-url="item.url"
                    class="now_level"
                    :src="item.img_url"
                    style="width: 100%"
                    mode="widthFix"
                ></image>
            </swiper-item>
        </swiper>
        <image style="width: 100%" mode="widthFix" class="now_level" :src="info.custom_head_img"></image>
        <view style="padding-bottom: 50rpx">
            <view style="margin: 10rpx 25rpx 25rpx 25rpx" @tap="open_url" :data-id="index" v-for="(item, index) in list" :key="index">
                <view class="bg-white" style="padding: 30rpx; border-radius: 15rpx">
                    <view style="display: flex; align-items: top">
                        <view style="padding-top: 10rpx">
                            <image
                                v-if="index >= 3"
                                style="width: 30rpx; height: 30rpx; vertical-align: middle"
                                :src="http_root + 'addons/yl_welore/web/static/examine/' + (index + 1) + '.png'"
                            ></image>

                            <image
                                v-if="index < 3"
                                class="now_level"
                                mode="heightFix"
                                style="height: 50rpx; vertical-align: middle"
                                :src="http_root + 'addons/yl_welore/web/static/examine/top' + (index + 1) + '.png'"
                            ></image>
                        </view>
                        <view
                            class="text-bold"
                            :style="
                                'display: inline; color: #000000; letter-spacing: 2rpx; font-size: 30rpx; margin-left: 10rpx; vertical-align: middle; white-space: normal; -webkit-line-clamp: ' +
                                (index < 3 ? 1 : 2) +
                                '; overflow: hidden; text-overflow: ellipsis; display: -webkit-box; -webkit-box-orient: vertical;line-height: 60rpx;'
                            "
                        >
                            {{ item.study_title == '' ? item.study_content : item.study_title }}
                        </view>
                    </view>
                    <view class="flex justify-start align-center" style="font-size: 12px; color: #333333; margin-top: 25rpx; padding-left: 10rpx">
                        <view style="color: #ff6600">围观数{{ item.hort }}</view>
                        <view style="margin: 0px 50rpx">评论数{{ item.reply }}</view>
                        <view>{{ item.realm_name }}</view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp();
var http = require('../../../util/http.js');
export default {
    data() {
        return {
            info: {
                custom_title: '',
                custom_head_img: ''
            },
            list: [],
            banner: [],
            http_root: app.globalData.http_root
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.get_list();
    },
    methods: {
        open_url(d) {
            var info = this.list[d.currentTarget.dataset.id];
            uni.navigateTo({
                url: '/yl_welore/pages/packageA/article/index?id=' + info['id'] + '&type=' + info['study_type']
            });
        },

        open_navigator(d) {
            console.log(d);
            var info = d.target.dataset;
            console.log(info);
            if (info.url == '' || info.url == null) {
                var current = info.src;
                uni.previewImage({
                    current: current,
                    // 当前显示图片的http链接
                    urls: [current] // 需要预览的图片http链接列表
                });
            } else {
                if (info.type == 0) {
                    uni.navigateTo({
                        url: info.url
                    });
                }
                if (info.type == 1) {
                    uni.navigateTo({
                        url: '/yl_welore/pages/web/index?url=' + info.url
                    });
                }
                if (info.type == 2) {
                    uni.navigateToMiniProgram({
                        appId: info.url,
                        path: info.path,
                        envVersion: 'release',
                        success(res) {
                            // 打开成功
                        }
                    });
                }
            }
        },

        get_list() {
            var b = app.globalData.api_root + 'Scanning/paper_heat_config';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    this.list = res.data.list;
                    this.info = res.data.info;
                    this.banner = res.data.banner;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        }
    }
};
</script>
<style>
page {
    background-color: #f3f4f6;
}
</style>

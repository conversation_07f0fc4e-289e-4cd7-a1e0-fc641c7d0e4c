<template>
    <view class="page-container">
        <cu-custom bgColor="none" :isSearch="false" :isBack="true" style="color: #000000;">
            <view slot="backText">返回</view>
            <view slot="content" class="header-title">活动详情</view>
        </cu-custom>

        <!-- 顶部奖杯倒计时区域 -->
        <view class="trophy-countdown-section">
            <view class="trophy-icon" v-if="info.is_open == 0 || info.is_open == 1">🏆</view>
            <view class="trophy-icon" v-if="info.is_open == 2">🏁</view>
            <view class="trophy-icon" v-if="info.is_open == 3">⏰</view>

            <view class="countdown-text" v-if="info.is_open == 0">
                距离开奖还有{{countdown.days}}天{{countdown.hours}}小时{{countdown.minutes}}分钟
            </view>
            <view class="countdown-text" v-if="info.is_open == 1">
                距离开奖还有{{countdown.days}}天{{countdown.hours}}小时{{countdown.minutes}}分钟
            </view>
            <view class="countdown-text" v-if="info.is_open == 2">活动已结束</view>
            <view class="countdown-text" v-if="info.is_open == 3">
                距离活动开始还有{{countdown.days}}天{{countdown.hours}}小时{{countdown.minutes}}分钟
            </view>
        </view>

        <!-- 奖品展示区域 -->
        <view class="prizes-display-section">
            <scroll-view class="prizes-scroll" scroll-x="true" :show-scrollbar="false">
                <view class="prizes-container">
                    <view class="prize-card" v-for="(item, index) in info.prize_list" :key="index">
                        <image class="prize-image" :src="item.prizeImage" mode="aspectFit" @click="previewPrizeImage(index)" />
                        <view class="prize-level-text">{{ item.prizeLevel }}等奖</view>
                        <view class="prize-name-text">{{ item.prizeName }}</view>
                        <view class="prize-count-text">数量 {{ item.extractQuantity }}</view>
                    </view>
                </view>
            </scroll-view>
        </view>
        <!-- 底部活动规则 -->
        <view class="activity-rules-section" @click="showXz = !showXz">
            <view class="rules-text">活动规则</view>
            <view class="cicon-angle"></view>
        </view>

        <!-- 隐藏的详细信息（保持原有功能） -->
        <view class="hidden-details" v-if="showXz">
            <view class="activity-intro-card">
                <view class="intro-title">活动简介</view>
                <view class="intro-content">
                    <mp-html @linktap="linktap" :selectable="true" :copy-link="false" :lazy-load="true"
                        :content="info.campaign_desc" />
                </view>
            </view>
        </view>
        <!-- 我的抽奖号区域 -->
        <view class="lottery-number-section">
            <view v-if="info.participate && info.participate.length > 0">
                <view class="number-title">我的抽奖码</view>
                <view class="lottery-numbers-container">
                    <view v-for="item in info.participate" class="lottery-number" :key="item.id">
                        <text class="lottery-number-text">{{ item.lucky_number }}</text>
                        <text class="lottery-time">{{ item.create_time }}</text>
                        <!-- 中奖状态表情 -->
                        <view v-if="item.is_award == 1 && info.is_open == 2" class="award-status win">
                            <text class="status-emoji">🎉</text>
                            <text class="status-text">中奖了</text>
                        </view>
                        <view v-if="item.is_award == 0 && info.is_open == 2" class="award-status lose">
                            <text class="status-emoji">❌</text>
                            <text class="status-text">未中奖</text>
                        </view>
                    </view>
                </view>
            </view>
            <!-- 立即抽奖按钮 -->
            <view class="draw-button-section">
                <view v-if="info.is_open == 0" class="draw-button active" @tap.stop.prevent="check_msg">
                    <view class="star-icon">⭐</view>
                    <view class="button-text">立即抽奖</view>
                </view>
                <view v-if="info.is_open == 1" class="draw-button waiting">
                    <view class="star-icon">⏰</view>
                    <view class="button-text">等待开奖</view>
                </view>
                <view v-if="info.is_open == 2" class="draw-button ended">
                    <view class="star-icon">🏁</view>
                    <view class="button-text">活动已结束</view>
                </view>
                <view v-if="info.is_open == 3" class="draw-button ended">
                    <view class="star-icon">🔜</view>
                    <view class="button-text">活动未开始</view>
                </view>
            </view>

            <!-- 参与用户区域 -->
            <view class="participants-section">
                <view style="display: flex;justify-content: space-between;align-items: center;">
                    <view class="participants-title">参与用户</view>
                    <view class="participants-count">{{ info.user_count }}人已参与</view>
                </view>
                <view class="participants-avatars">
                    <view class="avatar-item" v-for="(item, index) in info.user_list" :key="index"
                        :style="'background-image:url(' + item.user_head_sculpture + ');'"></view>
                </view>
            </view>
        </view>

    </view>
</template>

<script>
const app = getApp();
let rewardedVideoAd = null;
import http from '../../../util/http.js';
import regeneratorRuntime from '../../../util/runtime';
export default {
    data() {
        return {
            id: 0,
            info: {},
            showXz: false,
            countdown: {
                days: 0,
                hours: 0,
                minutes: 0,
                seconds: 0
            },
            countdownTimer: null
        };
    },
    onLoad(options) {
        this.id = options.id;
        this.doIt();
    },

    onShow() {
        var check = app.globalData.__PlugUnitScreen('7e1d82d91e04523ae2825c1d2991d5d6');
        if (!check) {
            uni.showToast({
                title: '未开通插件',
                icon: 'none',
                duration: 2000
            });
            return;
        }
    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide() { },
    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload() {
        // 清除倒计时定时器
        this.clearCountdownTimer();
    },
    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() { },
    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() { },
    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {
        return {
            title: this.info.lottery_name,
            path: '/yl_welore/pages/packageD/draw_info/index?id=' + this.info.id,
            imageUrl: this.info.prize_list[0].prizeImage
        };
    },

    methods: {
        // 解析中文时间格式
        parseChineseTime(timeStr) {
            if (!timeStr) return null;

            // 解析格式：2030年04月02日 00：00
            const regex = /(\d{4})年(\d{2})月(\d{2})日\s*(\d{2})：(\d{2})/;
            const match = timeStr.match(regex);

            if (match) {
                const [, year, month, day, hour, minute] = match;
                return new Date(year, month - 1, day, hour, minute);
            }
            return null;
        },

        // 计算倒计时
        calculateCountdown() {
            if (!this.info.draw_time) return;

            const targetTime = this.parseChineseTime(this.info.draw_time);
            if (!targetTime) return;

            const now = new Date();
            const diff = targetTime.getTime() - now.getTime();

            if (diff <= 0) {
                this.countdown = { days: 0, hours: 0, minutes: 0, seconds: 0 };
                this.clearCountdownTimer();
                return;
            }

            const days = Math.floor(diff / (1000 * 60 * 60 * 24));
            const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((diff % (1000 * 60)) / 1000);

            this.countdown = { days, hours, minutes, seconds };
        },

        // 启动倒计时定时器
        startCountdownTimer() {
            this.calculateCountdown();
            this.countdownTimer = setInterval(() => {
                this.calculateCountdown();
            }, 1000);
        },

        // 清除倒计时定时器
        clearCountdownTimer() {
            if (this.countdownTimer) {
                clearInterval(this.countdownTimer);
                this.countdownTimer = null;
            }
        },

        // 预览奖品图片
        previewPrizeImage(index) {
            if (!this.info.prize_list || this.info.prize_list.length === 0) {
                return;
            }

            // 提取所有奖品图片URL
            const imageUrls = this.info.prize_list.map(item => item.prizeImage);

            // 使用uni.previewImage预览图片
            uni.previewImage({
                current: index, // 当前显示图片的索引
                urls: imageUrls, // 图片URL数组
                success: () => {
                    console.log('图片预览成功');
                },
                fail: (err) => {
                    console.error('图片预览失败:', err);
                    uni.showToast({
                        title: '图片预览失败',
                        icon: 'none',
                        duration: 2000
                    });
                }
            });
        },
        open_kai() {
            uni.navigateTo({
                url: '/yl_welore/pages/packageD/draw_prize/index?id=' + this.id
            });
        },
        check_msg() {
            var msg = 0;
            var info = this.info;
            if (info.mian >= info.free_entry_count) {
                if (info.ji >= info.video_entry_count) {
                    uni.showToast({
                        title: '参与已达上限！',
                        icon: 'none',
                        duration: 2000
                    });
                    return;
                } else {
                    msg = 1;
                }
            } else {
                msg = 0;
            }
            uni.showModal({
                title: '提示',
                content: msg == 0 ? '免费抽一张奖券' : '看一段广告后抽一张奖券',
                success: (res) => {
                    if (res.confirm) {
                        if (msg == 1) {
                            this.ad();
                        } else {
                            this.clockIn(0);
                        }
                    }
                }
            });
        },
        check() {
            var info = this.info;
            if (info.mian >= info.free_entry_count) {
                console.log(info.ji);
                console.log(info.video_entry_count);
                console.log(info.ji >= info.video_entry_count);
                if (info.ji >= info.video_entry_count) {
                    uni.showToast({
                        title: '参与已达上限！',
                        icon: 'none',
                        duration: 2000
                    });
                    return;
                } else {
                    this.ad();
                }
            } else {
                this.clockIn(0);
            }
        },
        ad() {
            rewardedVideoAd.show().catch(() => {
                rewardedVideoAd
                    .load()
                    .then(() => rewardedVideoAd.show())
                    .catch((err) => {
                        uni.showModal({
                            title: '提示',
                            content: '准备广告中，请稍后重试',
                            showCancel: false
                        });
                    });
            });
        },
        clockIn(type) {
            var b = app.globalData.api_root + 'Retrieval/prizeDraw';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.id;
            params.type = type;
            http.POST(b, {
                params: params,
                success: (res) => {
                    uni.showModal({
                        title: '提示',
                        content: res.data.msg,
                        showCancel: false,
                        success: (res) => { }
                    });
                    this.get_raffle();
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        },
        doIt() {
            app.globalData.getLogin(
                // 成功回调 returnA
                (userInfo) => {
                    console.log(' 登录成功:', userInfo);
                    this.get_raffle();
                    this.ad_cr();
                },
                // 失败回调 returnB
                (err) => {
                    console.error(' 登录失败:', err);
                }
            );
        },
        ad_cr() {
            // 在页面onLoad回调事件中创建激励视频广告实例
            if (uni.createRewardedVideoAd) {
                rewardedVideoAd = uni.createRewardedVideoAd({
                    adUnitId: this.info['ad1']
                });
                rewardedVideoAd.onLoad(() => {
                    console.log('onLoad event emit');
                });
                rewardedVideoAd.onError((err) => {
                    console.log('onError event emit', err);
                });
                rewardedVideoAd.onClose((res) => {
                    console.log('123', res);
                    if (res && res.isEnded) {
                        this.clockIn(1);
                    } else {
                    }
                });
            }
        },
        //获取活动
        get_insKai() {
            var b = app.globalData.api_root + 'Retrieval/insKai';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = 1;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        },
        //获取活动
        get_raffle() {
            var b = app.globalData.api_root + 'Retrieval/info';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.id;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.code == 0) {
                        this.info = res.data.info;
                        // 启动倒计时
                        this.$nextTick(() => {
                            this.startCountdownTimer();
                        });
                    } else {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: (res) => { }
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        }
    }
};
</script>
<style>
page {
    background: #F8F9FD;
    min-height: 100vh;
    background-attachment: fixed;
}

.page-container {
    background: transparent;
    min-height: 100vh;
    padding-bottom: 50rpx;
}

/* 头部标题样式 */
.header-title {
    color: #000000;
    font-weight: 600;
    font-size: 36rpx;
}

/* 顶部奖杯倒计时区域 */
.trophy-countdown-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40rpx 0 60rpx;
}

.trophy-icon {
    font-size: 120rpx;
    margin-bottom: 20rpx;
}

.countdown-text {
    color: #000000;
    font-size: 28rpx;
    font-weight: 500;
}

/* 奖品展示区域 */
.prizes-display-section {
    padding: 0 0 40rpx;
}

.prizes-scroll {
    width: 100%;
    height: 375rpx;
}

.prizes-container {
    display: flex;
    padding: 0 30rpx;
    gap: 20rpx;
    width: max-content;
}

.prize-card {
    background: #ffffff;
    border-radius: 20rpx;
    padding: 30rpx 20rpx;
    text-align: center;
    width: 200rpx;
    height: 350rpx;
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
}

.prize-image {
    width: 100%;
    height: 100%;
    margin-bottom: 15rpx;
    cursor: pointer;
    transition: all 0.3s ease;
}

.prize-image:active {
    transform: scale(0.95);
    opacity: 0.8;
}

.prize-level-text {
    font-size: 28rpx;
    color: #6C5CE7;
    margin-bottom: 8rpx;
    font-weight: 600;
}

.prize-name-text {
    font-size: 26rpx;
    color: #333333;
    font-weight: 600;
    margin-bottom: 8rpx;
}

.prize-count-text {
    font-size: 22rpx;
    color: #999999;
}

/* 我的抽奖号区域 */
.lottery-number-section {
    background: #ffffff;
    margin: 0 30rpx 40rpx;
    border-radius: 20rpx;
    padding: 30rpx;
    text-align: center;
    position: relative;
    margin-bottom: 50rpx;
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.number-title {
    font-size: 28rpx;
    color: #B2BEC3;
    margin-bottom: 20rpx;
}

.lottery-numbers-container {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
}

.lottery-number {
    position: relative;
    background: #F8F9FD;
    width: 100%;
    padding: 20rpx;
    border-radius: 15rpx;
    margin: 0 auto;
    text-align: center;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
    margin-bottom: 20rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8rpx;
}

.lottery-number-text {
    font-size: 70rpx;
    font-weight: 700;
    color: #333333;
    letter-spacing: 8rpx;
}

.lottery-time {
    font-size: 24rpx;
    color: #999999;
    font-weight: 400;
}

/* 中奖状态徽章 */
.award-status {
    position: absolute;
    top: -15rpx;
    right: -15rpx;
    padding: 8rpx 16rpx;
    border-radius: 25rpx;
    font-size: 24rpx;
    font-weight: 600;
    color: #ffffff;
    z-index: 10;
    display: flex;
    align-items: center;
    gap: 6rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
    white-space: nowrap;
}

.award-status.win {
    background: linear-gradient(135deg, #ff4d4f 0%, #fa541c 100%);
    animation: celebrate 1.5s infinite;
    border: 2rpx solid #ffffff;
    box-shadow: 0 4rpx 12rpx rgba(255, 77, 79, 0.4);
}

.award-status.lose {
    background: linear-gradient(135deg, #8c8c8c 0%, #bfbfbf 100%);
    border: 2rpx solid #ffffff;
}

.status-emoji {
    font-size: 28rpx;
}

.status-text {
    font-size: 22rpx;
    font-weight: 600;
}

@keyframes celebrate {
    0% {
        transform: scale(1);
        box-shadow: 0 4rpx 12rpx rgba(255, 77, 79, 0.4);
        opacity: 1;
    }

    25% {
        transform: scale(1.05);
        box-shadow: 0 8rpx 20rpx rgba(255, 77, 79, 0.6);
        opacity: 0.9;
    }

    50% {
        transform: scale(1.1);
        box-shadow: 0 12rpx 30rpx rgba(255, 77, 79, 0.8);
        opacity: 1;
    }

    75% {
        transform: scale(1.05);
        box-shadow: 0 8rpx 20rpx rgba(255, 77, 79, 0.6);
        opacity: 0.9;
    }

    100% {
        transform: scale(1);
        box-shadow: 0 4rpx 12rpx rgba(255, 77, 79, 0.4);
        opacity: 1;
    }
}

@keyframes bounce {

    0%,
    100% {
        transform: translateY(0);
    }

    50% {
        transform: translateY(-5rpx);
    }
}

.number-menu-icon {
    font-size: 32rpx;
    color: #999999;
}

/* 立即抽奖按钮区域 */
.draw-button-section {
    display: flex;
    justify-content: center;
    padding: 40rpx 0;
}

.draw-button {
    width: 220rpx;
    height: 220rpx;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    box-shadow: 0 15rpx 40rpx rgba(0, 0, 0, 0.25);
    transition: all 0.3s ease;
}

.draw-button.active {
    background: linear-gradient(135deg, #6C5CE7 0%, #a29af7 100%);
    animation: breathe 2s ease-in-out infinite;
}

.draw-button.waiting {
    background: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%);
}

.draw-button.ended {
    background: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%);
}

/* 呼吸动画效果 */
@keyframes breathe {

    0%,
    100% {
        transform: scale(1);
        box-shadow: 0 15rpx 40rpx rgba(0, 0, 0, 0.25);
    }

    50% {
        transform: scale(1.05);
        box-shadow: 0 20rpx 50rpx rgba(108, 92, 231, 0.4);
    }
}

.star-icon {
    font-size: 50rpx;
    color: #ffffff;
    margin-bottom: 12rpx;
}

.button-text {
    font-size: 32rpx;
    color: #ffffff;
    font-weight: 600;
}

/* 参与用户区域 */
.participants-section {
    padding: 30rpx;
    text-align: center;
    border-radius: 15rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.participants-title {
    font-size: 28rpx;
    color: #000000;
    margin-bottom: 10rpx;
    font-weight: 600;
}

.participants-count {
    font-size: 24rpx;
    color: #000000;
    margin-bottom: 30rpx;
}

.participants-avatars {
    display: flex;
    gap: 10rpx;

}

.avatar-item {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    background-size: cover;
    background-position: center;
    border: 3rpx solid #ffffff;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

/* 活动规则区域 */
.activity-rules-section {
    background: #ffffff;
    margin: 0 30rpx 30rpx 30rpx;
    border-radius: 20rpx;
    padding: 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
    border: 2rpx solid rgba(255, 255, 255, 0.5);
}

.rules-text {
    font-size: 28rpx;
    color: #333333;
    font-weight: 600;
}

.rules-arrow {
    font-size: 28rpx;
    color: #999999;
    font-weight: 600;
}

.activity-intro-card {
    margin: 0 30rpx 30rpx 30rpx;
    background: #ffffff;
    border-radius: 24rpx;
    padding: 30rpx;
    box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.1), 0 6rpx 20rpx rgba(116, 185, 255, 0.15);
    border: 2rpx solid rgba(255, 255, 255, 0.5);
}

.intro-title {
    font-size: 28rpx;
    color: #666666;
    letter-spacing: 1rpx;
    margin-bottom: 20rpx;
    font-weight: 600;
}

.intro-content {
    color: #333333;
    line-height: 1.6;
}

.my-tickets-card {
    margin: 0 20rpx 30rpx;
    background: #ffffff;
    border-radius: 24rpx;
    padding: 30rpx 20rpx;
    box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.1), 0 6rpx 20rpx rgba(116, 185, 255, 0.15);
    border: 2rpx solid rgba(255, 255, 255, 0.5);
}

.tickets-title {
    font-size: 28rpx;
    color: #666666;
    letter-spacing: 1rpx;
    margin-bottom: 20rpx;
    font-weight: 600;
    padding: 0 10rpx;
}

.tickets-container {
    padding-bottom: 100rpx;
}

.ticket-item {
    position: relative;
    margin-bottom: 20rpx;
}

.award-badge {
    width: 140rpx;
    position: absolute;
    top: -30rpx;
    right: 40rpx;
    z-index: 10;
}

.ticket-time {
    position: absolute;
    top: 27rpx;
    width: 100%;
    text-align: center;
    font-size: 24rpx;
    color: #666666;
    z-index: 5;
}

.ticket-number {
    letter-spacing: 20rpx;
    position: absolute;
    bottom: 10rpx;
    width: 100%;
    text-align: center;
    font-size: 62rpx;
    font-weight: 700;
    color: #333333;
    z-index: 5;
}

/* 奖券样式 */
.card-10 {
    margin: 20px auto;
    width: 600rpx;
    height: 200rpx;
    position: relative;
    border-radius: 20rpx;
    filter: drop-shadow(2px 2px 2px rgba(0, 0, 0, 0.3));
}

.card-10::after {
    content: '';
    border-radius: 20rpx;
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    -webkit-mask-image: radial-gradient(circle at 9px 46px, transparent 9px, red 9.5px), linear-gradient(90deg, transparent 25%, red 0, red 75%, transparent 0);
    mask-image: radial-gradient(circle at 9px 46px, transparent 9px, red 9.5px), linear-gradient(90deg, transparent 25%, red 0, red 75%, transparent 0);
    -webkit-mask-size: 100%, 8px 3px;
    mask-size: 100%, 8px 3px;
    -webkit-mask-repeat: repeat, repeat-x;
    mask-repeat: repeat, repeat-x;
    -webkit-mask-position: -9px, 50% 44.5px;
    mask-position: -9px, 50% 44.5px;
    -webkit-mask-composite: source-out;
    mask-composite: subtract;
    background-image: radial-gradient(73% 147%, #eadfdf 59%, #ece2df 100%), radial-gradient(91% 146%, rgba(255, 255, 255, 0.5) 47%, rgba(0, 0, 0, 0.5) 100%);
    background-blend-mode: screen;
}
</style>

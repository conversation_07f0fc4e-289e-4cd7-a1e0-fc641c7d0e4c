<template>
    <view>
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">{{ info.title }}</view>
        </cu-custom>
        <view style="width: 91%; padding: 20px">
            <mp-html @linktap="linktap" :selectable="true" :copy-link="false" :lazy-load="true" :content="info.info" />
        </view>
    </view>
</template>

<script>
var app = getApp();
import http from '../../../util/http.js';
export default {
    components: {},
    data() {
        return {
            info: [],
            article: '',
            type: 0
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.type = options.type;
        this.getInfo();
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {},
    methods: {
        getInfo() {
            var b = app.globalData.api_root + 'Microseries/all_agreement';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.type = this.type;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    this.info = res.data.list;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },

        linktap() {
            console.log('占位：函数 linktap 未声明');
        }
    }
};
</script>
<style>
/* yl_welore/pages/packageC/all_agreement/index.wxss */
</style>

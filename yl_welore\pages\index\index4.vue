<template>
    <view v-if="new_list" style="background-color: #eff1f0; min-height: 1200rpx;padding:20rpx;">
        <view class="flex" style="gap: 20rpx;">
            <view id="left" style="width: 50%;">
                <view 
                    v-if="dataListindex % 2 == 0"
                    style="background-color: #fff; border-radius: 5px; overflow: hidden; margin-bottom: 10px; padding-bottom: 20rpx; position: relative"
                    v-for="(item, dataListindex) in leftList"
                    :key="dataListindex"
                >
                    <image
                        v-if="item.top_time"
                        :src="http_root + 'addons/yl_welore/web/static/mineIcon/index3/top1.png'"
                        style="opacity: 0.8; width: 70px; height: 70px; position: absolute; right: 0; top: 0; z-index: 200"
                    ></image>

                    <view>
                        <view
                            @tap="home_url"
                            :data-index="dataListindex"
                            data-k="3"
                            :data-type="item.study_type"
                            :data-id="item.id"
                            v-if="item.image_part[0] && item.study_type != 2"
                            style="max-height: 245px; overflow: hidden"
                        >
                            <image :lazy-load="true" :src="item.image_part[0]" style="width: 100%" mode="widthFix"></image>
                        </view>
                        <view v-if="item.study_type == 2">
                            <view @tap="home_url" :data-index="dataListindex" data-k="3" :data-type="item.study_type" :data-id="item.id">
                                <view v-if="item.image_part.length > 0" class="grid flex-sub col-1" style="position: relative">
                                    <image :src="item.image_part[0]" mode="widthFix" style="width: 100%; margin: 0 auto; border-radius: 5px"></image>
                                    <text
                                        class="cuIcon-videofill lg text-white"
                                        style="z-index: 1; font-size: 40px; position: absolute; text-align: center; left: 38%; top: 40%"
                                    ></text>
                                </view>
                                <view
                                    v-if="item.image_part.length == null || item.image_part.length == 0"
                                    class="bg-black padding radius text-center shadow-blur"
                                    style="position: relative; margin: 0 auto; width: 100%; height: 120px; z-index: 100; overflow: hidden; border-radius: 5px; font-size: 16px"
                                >
                                    <text
                                        class="cuIcon-videofill lg text-white"
                                        style="z-index: 1; font-size: 40px; position: absolute; text-align: center; left: 38%; bottom: 28%"
                                    ></text>
                                </view>
                            </view>
                        </view>
                        <view style="text-align: center">
                            <image
                                v-if="item.red == 1 && version == 0"
                                style="margin: 5px 10px; width: 30px; height: 15px; border-radius: 2px"
                                :src="http_root + 'addons/yl_welore/web/static/mineIcon/index3/fl.png'"
                            ></image>
                            <image
                                v-if="item.study_type == 3"
                                style="margin: 5px 10px; width: 30px; height: 15px; border-radius: 2px"
                                :src="http_root + 'addons/yl_welore/web/static/mineIcon/index3/hd.png'"
                            ></image>
                            <image
                                v-if="item.is_buy == 1 && version == 0"
                                style="margin: 5px 10px; width: 30px; height: 15px; border-radius: 2px"
                                :src="http_root + 'addons/yl_welore/web/static/mineIcon/index3/ff.png'"
                            ></image>
                        </view>
                        <view
                            @tap="home_url"
                            :data-index="dataListindex"
                            data-k="3"
                            :data-type="item.study_type"
                            :data-id="item.id"
                            :style="'font-size:13px;padding: 10px 10px 0px 10px;font-weight: 500;color:' + item.study_title_color + ';'"
                        >
                            <text v-if="item.gambit_name" @tap="gambit_list" :data-id="item.gambit_id" style="color: #0099ff; margin-right: 5px">
                                {{ item.gambit_name }}
                            </text>
                            <rich-text class="text_num" :nodes="item.study_title == '' ? item.study_content : item.study_title"></rich-text>
                        </view>
                    </view>

                    <view class="cu-list menu-avatar">
                        <view class="cu-item">
                            <view
                                @tap="home_url"
                                :data-index="dataListindex"
                                data-k="1"
                                :data-user_id="item.user_id"
                                class="cu-avatar round"
                                :style="'background-image:url(' + item.user_head_sculpture + ');'"
                            >
                                <image
                                    v-if="item.user_id != 0"
                                    class="now_level"
                                    style="height: 40px; width: 40px; position: absolute; max-width: initial"
                                    :src="item.avatar_frame"
                                ></image>
                            </view>
                            <view class="content_4">
                                <view style="position: relative">
                                    <text :class="'text-cut text_num_1 ' + (item.user_id != 0 ? item.special : '')" style="font-size: 12px; width: 115px">
                                        {{ item.user_nick_name }}
                                    </text>
                                    <image v-if="item.attr != ''" class="now_level" style="height: 35rpx; width: 35rpx" :src="item.attr.attest.at_icon"></image>
                                    <image
                                        v-if="item.user_vip == 1 && item.user_id != 0"
                                        :src="http_root + 'addons/yl_welore/web/static/applet_icon/vip.png'"
                                        style="width: 30rpx; height: 30rpx; margin-left: 3px"
                                    ></image>
                                    <image v-if="item.user_id != 0" mode="heightFix" class="now_level" :src="item.level" style="height: 26rpx"></image>
                                    <image
                                        mode="heightFix"
                                        class="now_level"
                                        v-if="item.wear_merit && item.user_id != 0"
                                        :src="item.wear_merit"
                                        style="height: 26rpx; margin-left: 3px"
                                    ></image>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view class="grid col-3 text-center" style="margin-bottom: 5px">
                        <view @tap="parseEventDynamicCode($event, item.is_open == 0 ? 'check_share' : '')">
                            <button hover-class="none" :open-type="item.is_open == 0 ? '' : 'share'" :data-key="dataListindex">
                                <image
                                    :src="http_root + 'addons/yl_welore/web/static/mineIcon/index4/zhuanfa.png'"
                                    style="width: 46rpx; vertical-align: middle; height: 46rpx"
                                ></image>
                                <text class="index_nav_name" style="color: #000; font-size: 11px; vertical-align: middle">转发</text>
                            </button>
                        </view>
                        <view>
                            <button
                                style="padding-top: 2px"
                                hover-class="none"
                                @tap="parseEventDynamicCode($event, item.is_buy == 1 ? '' : 'add_zan')"
                                :data-id="item.id"
                                :data-key="dataListindex"
                            >
                                <image
                                    :animation="item.animationData_zan"
                                    v-if="item.is_info_zan == false"
                                    :src="http_root + 'addons/yl_welore/web/static/mineIcon/index4/xihuan.png'"
                                    style="width: 40rpx; height: 40rpx; vertical-align: middle"
                                ></image>
                                <image
                                    :animation="item.animationData_zan"
                                    v-if="item.is_info_zan == true"
                                    :src="http_root + 'addons/yl_welore/web/static/mineIcon/index4/xhuan2.png'"
                                    style="width: 40rpx; height: 40rpx; vertical-align: middle"
                                ></image>
                                <text class="index_nav_name" style="color: #000; font-size: 11px; vertical-align: middle">
                                    {{ item.info_zan_count_this > 10000 ? item.info_zan_count : item.info_zan_count_this }}
                                </text>
                            </button>
                        </view>
                        <view>
                            <button
                                style="padding-top: 2px"
                                hover-class="none"
                                @tap="parseEventDynamicCode($event, item.is_buy == 1 ? '' : 'home_pl')"
                                :data-id="item.id"
                                :data-key="dataListindex"
                            >
                                <image
                                    :src="http_root + 'addons/yl_welore/web/static/mineIcon/index4/xinxi.png'"
                                    style="width: 40rpx; height: 40rpx; vertical-align: middle"
                                ></image>
                                <text class="index_nav_name" style="color: #000; font-size: 11px; vertical-align: middle">
                                    {{ item.study_repount }}
                                </text>
                            </button>
                        </view>
                    </view>

                    <view class="flex justify-center">
                        <view class="">
                            <text
                                v-if="item.check_qq == 'da' && item.user_id != 0"
                                style="background-color: #9966ff; color: #fff; padding: 3px 4px; border-radius: 2px; font-size: 10px; margin-right: 5px"
                            >
                                {{ design.qq_name }}主
                            </text>
                            <text
                                v-if="item.check_qq == 'xiao' && item.user_id != 0"
                                style="background-color: #4facfe; color: #fff; padding: 3px 4px; border-radius: 2px; font-size: 10px; margin-right: 5px"
                            >
                                管理
                            </text>
                        </view>
                        <view @tap="home_url" :data-index="dataListindex" data-k="2" :data-id="item.tory_id">
                            <view style="font-size: 11px; color: #888888">{{ item.adapter_time }} / {{ item.realm_name }}</view>
                        </view>
                    </view>
                </view>
            </view>
            <view id="right" style="width: 50%;">
                <view
                    style="background-color: #fff; border-radius: 5px; overflow: hidden; margin-bottom: 10px; padding-bottom: 20rpx; position: relative"
                    v-for="(item, dataListindex) in rightList" 
                    :key="dataListindex"
                >
                    <image
                        v-if="item.top_time"
                        :src="http_root + 'addons/yl_welore/web/static/mineIcon/index3/top1.png'"
                        style="opacity: 0.8; width: 70px; height: 70px; position: absolute; right: 0; top: 0; z-index: 200"
                    ></image>

                    <view>
                        <view
                            @tap="home_url"
                            :data-index="dataListindex"
                            data-k="3"
                            :data-type="item.study_type"
                            :data-id="item.id"
                            v-if="item.image_part[0] && item.study_type != 2"
                            style="max-height: 245px; overflow: hidden"
                        >
                            <image :lazy-load="true" :src="item.image_part[0]" style="width: 100%" mode="widthFix"></image>
                        </view>
                        <view v-if="item.study_type == 2">
                            <view @tap="home_url" :data-index="dataListindex" data-k="3" :data-type="item.study_type" :data-id="item.id">
                                <view v-if="item.image_part.length > 0" class="grid flex-sub col-1" style="position: relative">
                                    <image :src="item.image_part[0]" mode="widthFix" style="width: 100%; margin: 0 auto; border-radius: 5px"></image>
                                    <text
                                        class="cuIcon-videofill lg text-white"
                                        style="z-index: 1; font-size: 40px; position: absolute; text-align: center; left: 38%; top: 40%"
                                    ></text>
                                </view>
                                <view
                                    v-if="item.image_part.length == null || item.image_part.length == 0"
                                    class="bg-black padding radius text-center shadow-blur"
                                    style="position: relative; margin: 0 auto; width: 100%; height: 120px; z-index: 100; overflow: hidden; border-radius: 5px; font-size: 16px"
                                >
                                    <text
                                        class="cuIcon-videofill lg text-white"
                                        style="z-index: 1; font-size: 40px; position: absolute; text-align: center; left: 38%; bottom: 28%"
                                    ></text>
                                </view>
                            </view>
                        </view>
                        <view style="text-align: center">
                            <image
                                v-if="item.red == 1 && version == 0"
                                style="margin: 5px 10px; width: 30px; height: 15px; border-radius: 2px"
                                :src="http_root + 'addons/yl_welore/web/static/mineIcon/index3/fl.png'"
                            ></image>
                            <image
                                v-if="item.study_type == 3"
                                style="margin: 5px 10px; width: 30px; height: 15px; border-radius: 2px"
                                :src="http_root + 'addons/yl_welore/web/static/mineIcon/index3/hd.png'"
                            ></image>
                            <image
                                v-if="item.is_buy == 1 && version == 0"
                                style="margin: 5px 10px; width: 30px; height: 15px; border-radius: 2px"
                                :src="http_root + 'addons/yl_welore/web/static/mineIcon/index3/ff.png'"
                            ></image>
                        </view>
                        <view
                            @tap="home_url"
                            :data-index="dataListindex"
                            data-k="3"
                            :data-type="item.study_type"
                            :data-id="item.id"
                            :style="'font-size:13px;padding: 10px 10px 0px 10px;font-weight: 500;color:' + item.study_title_color + ';'"
                        >
                            <text v-if="item.gambit_name" @tap="gambit_list" :data-id="item.gambit_id" style="color: #0099ff; margin-right: 5px">
                                {{ item.gambit_name }}
                            </text>
                            <rich-text class="text_num" :nodes="item.study_title == '' ? item.study_content : item.study_title"></rich-text>
                        </view>
                    </view>

                    <view class="cu-list menu-avatar">
                        <view class="cu-item">
                            <view
                                @tap="home_url"
                                :data-index="dataListindex"
                                data-k="1"
                                :data-user_id="item.user_id"
                                class="cu-avatar round"
                                :style="'background-image:url(' + item.user_head_sculpture + ');'"
                            >
                                <image
                                    v-if="item.user_id != 0"
                                    class="now_level"
                                    style="height: 40px; width: 40px; position: absolute; max-width: initial"
                                    :src="item.avatar_frame"
                                ></image>
                            </view>
                            <view class="content_4">
                                <view style="position: relative">
                                    <text :class="'text-cut text_num_1 ' + (item.user_id != 0 ? item.special : '')" style="font-size: 12px; width: 115px">
                                        {{ item.user_nick_name }}
                                    </text>
                                    <image v-if="item.attr != ''" class="now_level" style="height: 35rpx; width: 35rpx" :src="item.attr.attest.at_icon"></image>
                                    <image
                                        v-if="item.user_vip == 1 && item.user_id != 0"
                                        :src="http_root + 'addons/yl_welore/web/static/applet_icon/vip.png'"
                                        style="width: 30rpx; height: 30rpx; margin-left: 3px"
                                    ></image>
                                    <image v-if="item.user_id != 0" mode="heightFix" class="now_level" :src="item.level" style="height: 26rpx"></image>
                                    <image
                                        mode="heightFix"
                                        class="now_level"
                                        v-if="item.wear_merit && item.user_id != 0"
                                        :src="item.wear_merit"
                                        style="height: 26rpx; margin-left: 3px"
                                    ></image>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view class="grid col-3 text-center" style="margin-bottom: 5px">
                        <view @tap="parseEventDynamicCode($event, item.is_open == 0 ? 'check_share' : '')">
                            <button hover-class="none" :open-type="item.is_open == 0 ? '' : 'share'" :data-key="dataListindex">
                                <image
                                    :src="http_root + 'addons/yl_welore/web/static/mineIcon/index4/zhuanfa.png'"
                                    style="width: 46rpx; vertical-align: middle; height: 46rpx"
                                ></image>
                                <text class="index_nav_name" style="color: #000; font-size: 11px; vertical-align: middle">转发</text>
                            </button>
                        </view>
                        <view>
                            <button
                                style="padding-top: 2px"
                                hover-class="none"
                                @tap="parseEventDynamicCode($event, item.is_buy == 1 ? '' : 'add_zan')"
                                :data-id="item.id"
                                :data-key="dataListindex"
                            >
                                <image
                                    :animation="item.animationData_zan"
                                    v-if="item.is_info_zan == false"
                                    :src="http_root + 'addons/yl_welore/web/static/mineIcon/index4/xihuan.png'"
                                    style="width: 40rpx; height: 40rpx; vertical-align: middle"
                                ></image>
                                <image
                                    :animation="item.animationData_zan"
                                    v-if="item.is_info_zan == true"
                                    :src="http_root + 'addons/yl_welore/web/static/mineIcon/index4/xhuan2.png'"
                                    style="width: 40rpx; height: 40rpx; vertical-align: middle"
                                ></image>
                                <text class="index_nav_name" style="color: #000; font-size: 11px; vertical-align: middle">
                                    {{ item.info_zan_count_this > 10000 ? item.info_zan_count : item.info_zan_count_this }}
                                </text>
                            </button>
                        </view>
                        <view>
                            <button
                                style="padding-top: 2px"
                                hover-class="none"
                                @tap="parseEventDynamicCode($event, item.is_buy == 1 ? '' : 'home_pl')"
                                :data-id="item.id"
                                :data-key="dataListindex"
                            >
                                <image
                                    :src="http_root + 'addons/yl_welore/web/static/mineIcon/index4/xinxi.png'"
                                    style="width: 40rpx; height: 40rpx; vertical-align: middle"
                                ></image>
                                <text class="index_nav_name" style="color: #000; font-size: 11px; vertical-align: middle">
                                    {{ item.study_repount }}
                                </text>
                            </button>
                        </view>
                    </view>

                    <view class="flex justify-center">
                        <view class="">
                            <text
                                v-if="item.check_qq == 'da' && item.user_id != 0"
                                style="background-color: #9966ff; color: #fff; padding: 3px 4px; border-radius: 2px; font-size: 10px; margin-right: 5px"
                            >
                                {{ design.qq_name }}主
                            </text>
                            <text
                                v-if="item.check_qq == 'xiao' && item.user_id != 0"
                                style="background-color: #4facfe; color: #fff; padding: 3px 4px; border-radius: 2px; font-size: 10px; margin-right: 5px"
                            >
                                管理
                            </text>
                        </view>
                        <view @tap="home_url" :data-index="dataListindex" data-k="2" :data-id="item.tory_id">
                            <view style="font-size: 11px; color: #888888">{{ item.adapter_time }} / {{ item.realm_name }}</view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view :class="'cu-load ' + (!di_msg ? 'loading' : 'over')" style="padding-bottom: 200rpx"></view>
    </view>
</template>

<script>
export default {
    props: ['data', 'compName'],
    computed: {
        leftList(){
            return this.$parent.$data.leftList;
        },
        rightList(){
            return this.$parent.$data.rightList;
        },
        new_list() {
            return this.$parent.$data.new_list;
        },
        dataListindex() {
            return this.$parent.$data.dataListindex;
        },
        item() {
            return this.$parent.$data.item;
        },
        http_root() {
            return this.$parent.$data.http_root;
        },
        $state() {
            return this.$parent.$data.$state;
        },
        order_time() {
            return this.$parent.$data.order_time;
        },
        version() {
            return this.$parent.$data.version;
        },
        img() {
            return this.$parent.$data.img;
        },
        img_index() {
            return this.$parent.$data.img_index;
        },
        vo_index() {
            return this.$parent.$data.vo_index;
        },
        vo_item() {
            return this.$parent.$data.vo_item;
        },
        voi_item() {
            return this.$parent.$data.voi_item;
        },
        index() {
            return this.$parent.$data.index;
        },
        ad_info() {
            return this.$parent.$data.ad_info;
        },
        di_msg() {
            return this.$parent.$data.di_msg;
        }
    },
    methods: {
        home_url(e) {
            this.$emit('home-url', e);
        },
        gambit_list(e) {
            this.$emit('gambit-list', e);
        },
        dian_option(e) {
            this.$emit('dian-option', e);
        },
        vote_do(e) {
            this.$emit('vote-do', e);
        },
        play(e) {
            this.$emit('play', e);
        },
        stop(e) {
            this.$emit('stop', e);
        },
        sliderChange(e) {
            this.$emit('slider-change', e);
        },
        home_pl(e) {
            this.$emit('home-pl', e);
        },
        parseEventDynamicCode(e, type) {
            this.$emit('dynamic-code', e, type);
        }
    }
};
</script>

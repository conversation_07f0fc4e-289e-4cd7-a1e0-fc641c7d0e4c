<template>
    <view>
        <view
            class="page header-container"
            :style="'height: 400rpx;position:relative;background-image: url(' + http_root + 'addons/yl_welore/web/static/mineIcon/material/top.png);background-size: 100%;'"
        >
            <!-- 渐变遮罩层 -->
            <view class="header-overlay"></view>
            <view style="width: 100%; margin: 0px auto">
                <cu-custom :bgColor="floorstatus ? 'bg-white' : ''" :isSearch="false" :isBack="true" style="color: #fff">
                    <view slot="backText">返回</view>
                    <view class="animation-slide-top" v-if="floorstatus" slot="content" style="color: #000000; font-weight: 600; font-size: 36rpx">
                        🗣️ {{ info.custom_hiss_title }}
                    </view>
                </cu-custom>
            </view>
            <view class="flex justify-start align-center" style="padding: 20rpx 40rpx 40rpx 40rpx">
                <view class="">
                    <image style="width: 65px; height: 65px; border-radius: 15px" :src="http_root + 'addons/yl_welore/web/static/mineIcon/material/sd.png'"></image>
                </view>
                <view style="margin-left: 10px; color: #ffffff">
                    <view style="letter-spacing: 1px">
                        <text style="font-size: 18px; font-weight: 600">{{ info.custom_hiss_title }}</text>
                        <view class="cu-tag bg-purple radius sm" style="margin-left: 10rpx">匿名</view>
                    </view>
                    <view style="margin-top: 10px; letter-spacing: 1px">
                        <text style="font-size: 14px">{{ info.count }}</text>
                        <text style="font-size: 12px; margin-left: 5rpx">条动态</text>
                    </view>
                </view>
            </view>
        </view>
        <view style="position: relative; background-color: #ffffff; border-radius: 10px 10px 0px 0px; top: -20rpx">
            <view
                :class="'flex justify-between align-center bg-white ' + (floorstatus ? 'animation-slide-right' : '') + ' ' + (floorstatus ? 'sticky' : '')"
                style="padding: 30rpx; z-index: 100"
            >
                <view style="font-size: 14px; font-weight: 700">讨论列表</view>
                <view>
                    <ui-tab @change="handleClickItem1" bg="bg-gray" :tab="['新发', '新回', '热门', '我的', '@我']" tpl="btn" ui="tab_home" mark="radius" inline />
                </view>
            </view>
            <view style="padding: 20rpx 40rpx 40rpx 40rpx" v-for="(item, index) in list" :key="index">
                <view class="solid-bottom" @tap="open_info" :data-id="item.id">
                    <view class="flex justify-start align-start">
                        <view class="">
                            <image style="width: 40px; height: 40px; border-radius: 50%" :src="item.reply_head"></image>
                        </view>
                        <view style="margin-left: 10px">
                            <view style="font-size: 14px; letter-spacing: 1px">{{ item.temp_name }}</view>
                            <view style="margin-top: 5px; letter-spacing: 1px; color: #acadb4">
                                <text style="font-size: 12px">{{ item.send_time }}</text>
                            </view>
                        </view>
                    </view>
                    <view style="margin-top: 15px; color: #333333; letter-spacing: 1px">
                        <view class="text_num_3">
                            <view v-if="item.user_id != 0" @tap.stop.prevent="open_user" :data-id="item.user_id" class="text-blue" style="margin-bottom: 20rpx">
                                <text class="cicon-at-line"></text>
                                <text>{{ item.user_name }}</text>
                            </view>
                            <rich-text :nodes="item.content"></rich-text>
                        </view>
                        <view style="margin-top: 20rpx">
                            <!-- 1 -->
                            <view style="width: 30%" v-if="item.image_part.length == 1 && img != ''" v-for="(img, img_index) in item.image_part" :key="img_index">
                                <image
                                    class="now_level"
                                    :lazy-load="true"
                                    :src="img"
                                    @tap.stop.prevent="Preview"
                                    :data-src="img"
                                    :data-index="index"
                                    style="border-radius: 3px; width: 100%; max-height: 200px"
                                    mode="widthFix"
                                ></image>
                            </view>
                            <!-- 1 -->
                            <!-- 2 -->
                            <view
                                style="width: 30%; float: left; text-align: center"
                                v-if="item.image_part.length == 2"
                                v-for="(img, img_index) in item.image_part"
                                :key="img_index"
                            >
                                <image
                                    class="now_level"
                                    :lazy-load="true"
                                    v-if="img_index == 0"
                                    @tap.stop.prevent="Preview"
                                    :data-src="img"
                                    :data-index="index"
                                    :src="img"
                                    style="width: 100%; border-radius: 5px; max-height: 200px"
                                    mode="widthFix"
                                ></image>

                                <image
                                    class="now_level"
                                    :lazy-load="true"
                                    v-if="img_index == 1"
                                    @tap.stop.prevent="Preview"
                                    :data-src="img"
                                    :data-index="index"
                                    :src="img"
                                    style="width: 100%; border-radius: 5px; padding-left: 5px; max-height: 200px"
                                    mode="widthFix"
                                ></image>
                            </view>
                            <!-- 2 -->
                            <!-- 3 -->
                            <view class="grid col-3 text-center">
                                <block v-if="item.image_part.length > 2" v-for="(img, img_index) in item.image_part" :key="img_index">
                                    <view style="text-align: center" v-if="img_index == 0">
                                        <image
                                            class="now_level"
                                            :lazy-load="true"
                                            :src="img"
                                            @tap.stop.prevent="Preview"
                                            :data-src="img"
                                            :data-index="index"
                                            style="border-radius: 5px; width: 100%; max-height: 200px"
                                            mode="widthFix"
                                        ></image>
                                    </view>

                                    <view style="text-align: center; padding: 0px 5px" v-if="img_index == 1">
                                        <image
                                            class="now_level"
                                            :lazy-load="true"
                                            :src="img"
                                            @tap.stop.prevent="Preview"
                                            :data-src="img"
                                            :data-index="index"
                                            style="border-radius: 5px; width: 100%; max-height: 200px"
                                            mode="widthFix"
                                        ></image>
                                    </view>

                                    <view style="text-align: center" v-if="img_index == 2">
                                        <image
                                            class="now_level"
                                            :lazy-load="true"
                                            :src="img"
                                            @tap.stop.prevent="Preview"
                                            :data-src="img"
                                            :data-index="index"
                                            style="border-radius: 5px; width: 100%; max-height: 200px"
                                            mode="widthFix"
                                        ></image>
                                    </view>
                                </block>
                            </view>
                        </view>
                    </view>
                    <view class="flex justify-between align-center" style="padding: 20px 0px; width: 100%">
                        <view style="font-weight: 400; color: #acadb4">
                            <text>{{ item.reply_content }} 评论</text>
                            <text style="margin-left: 10px">{{ item.praise_number }} 赞</text>
                        </view>
                        <view>
                            <view style="font-size: 16px; background-color: #f0f0f0; line-height: 2em; border-radius: 25px; text-align: center; padding: 0px 10px">
                                <view class="cicon-discuss-line" style="margin: 0px 5px"></view>
                                <view class="_icon-move" style="margin: 0px 5px; transform: rotate(90deg)"></view>
                                <view @tap.stop.prevent="add_zan" :data-key="index" style="display: inline-block">
                                    <view v-if="item.applaud == 0" class="cicon-thumb-up-line-o" style="margin: 0px 5px"></view>
                                    <view v-if="item.applaud == 1" class="cicon-thumb-up-line text-red" style="margin: 0px 5px"></view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view style="position: fixed; z-index: 99999; left: 0; bottom: 5%; right: 0; text-align: center">
            <button @tap="add" class="cu-btn round" style="width: 200rpx; height: 75rpx; background-image: linear-gradient(-60deg, #f3c64d 0%, #f9df4e 100%)">
                发{{ info.custom_hiss_title }}
            </button>
        </view>
        <view :class="'cu-load ' + (!di_msg ? 'loading' : 'over')"></view>
    </view>
</template>

<script>
import uiTab from '@/yl_welore/colorui/ui-tab/ui-tab';
import http from '../../../util/http.js';
const app = getApp();

export default {
    components: {
        uiTab
    },
    data() {
        return {
            http_root: app.globalData.http_root,
            list: [],
            info: {},
            page: 1,
            di_msg: false,
            sort_index: 0,
            onload: 0,
            floorstatus: false,
            floorstatus_out: false
        };
    },

    onPageScroll: function (e) {
        if (e.scrollTop > 200) {
            this.floorstatus = true;
            setTimeout(() => {
                this.floorstatus_out = false;
            }, 100);
        } else {
            this.floorstatus = false;
            setTimeout(() => {
                this.floorstatus_out = true;
            }, 100);
        }
    },

    onLoad: function () {
        uni.hideShareMenu();
        var subscribe = app.globalData.getCache('subscribe');
        if (!subscribe) {
            app.globalData.subscribe_message(
                (res) => {
                    console.log(res);
                    if (res == '') {
                        return;
                    }
                    app.globalData.setCache('subscribe', res.parallelism_data);
                },
                () => {}
            );
        }
        this.page = 1;
        this.list = [];
        this.get_secret();
    },

    onShow() {
        if (this.onload == 1) {
            this.page = 1;
            this.list = [];
            this.get_secret();
        }
    },

    onReachBottom: function () {
        this.page = this.page + 1;
        this.get_secret();
    },

    onPullDownRefresh: function () {
        uni.showNavigationBarLoading();
        setTimeout(function () {
            uni.hideNavigationBarLoading();
            uni.stopPullDownRefresh();
        }, 1500);
        this.list = [];
        this.page = 1;
        this.get_secret();
    },

    methods: {
        open_info(item) {
            var id = item.currentTarget.dataset.id;
            uni.navigateTo({
                url: '/yl_welore/pages/packageD/whisper_info/index?id=' + id
            });
        },
        handleClickItem1(item) {
            this.sort_index = item.detail.index;
            this.page = 1;
            this.list = [];
            this.get_secret();
        },
        add() {
            uni.navigateTo({
                url: '/yl_welore/pages/packageD/whisper_add/index'
            });
        },
        open_user(e) {
            var id = e.currentTarget.dataset.id;
            uni.navigateTo({
                url: '/yl_welore/pages/packageB/my_home/index?id=' + id
            });
        },
        add_zan(item) {
            var key = item.currentTarget.dataset.key;
            var info = this.list[key];
            if (info['applaud'] == 0) {
                this.$set(this.list[key], 'applaud', 1);
                this.$set(this.list[key], 'praise_number', info['praise_number'] + 1);
            } else {
                this.$set(this.list[key], 'applaud', 0);
                this.$set(this.list[key], 'praise_number', info['praise_number'] - 1);
            }
            var b = app.globalData.api_root + 'Whisper/secret_zan';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = info.id;
            http.POST(b, {
                params: params,
                success: (res) => {},
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },
        get_secret() {
            var b = app.globalData.api_root + 'Whisper/get_secret';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.page = this.page;
            params.sort_index = this.sort_index;
            var list = this.list;
            http.POST(b, {
                params: params,
                success: (res) => {
                    if (res.data.list.length == 0 || res.data.list.length < 5) {
                        this.di_msg = true;
                    }
                    list.push(...res.data.list);
                    this.list = list;
                    this.info = res.data.info;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },
        Preview(e) {
            var src = e.currentTarget.dataset.src;
            uni.previewImage({
                current: src,
                urls: [src]
            });
        }
    }
};
</script>
<style>
@import '../../../colorui/animation.css';
page {
    background-color: #ffffff;
}
.sticky {
    position: fixed !important;
    top: 8%;
    width: 100%;
    margin-top: 30rpx;
}
.page {
    background-color: transparent;
}
.tab_home {
    height: 25px !important;
    border-radius: 25px;
}
.ui-tab-mark {
    background-color: #ffffff;
    border-radius: 25px !important;
}
</style>

<template>
    <view>
        <cu-custom :isSearch="false" :isBack="false" bgColor="bg-white">
            <view v-if="elect_sheathe == 0 && design.shop_arbor == 1" slot="left_z">
                <view style="display: flex; justify-content: space-around; flex-direction: column; height: 100%">
                    <navigator url="/yl_welore/pages/user_smail/index" hover-class="none">
                        <view class="cu-avatar radius" style="background-color: transparent; width: 35px; height: 35px">
                            <image src="/static/yl_welore/style/icon/bgt.png" style="width: 35px; height: 35px"></image>
                            <view class="cu-tag badge" style="z-index: 1">{{ user_info.user_male > 99 ? '99+' : user_info.user_male }}</view>
                        </view>
                    </navigator>
                </view>
            </view>
            <view slot="content" style="color: #000; font-weight: 600; font-size: 36rpx">个人中心</view>
        </cu-custom>
        <view class="bg-white" style="padding-bottom: 200rpx">
            <view class="bg-white flex padding align-center">
                <view class="padding-lg margin-xs radius">
                    <view @tap="my_home" class="cu-avatar round xl" :style="'background-image:url(' + user_info.user_head_sculpture + ');'">
                        <view
                            style="z-index: 10"
                            v-if="user_info.tourist == 0"
                            :class="'cu-tag badge ' + (user_info.gender == 2 ? 'cuIcon-female bg-pink' : 'cuIcon-male bg-blue')"
                        ></view>
                        <image
                            v-if="user_info.attest"
                            :src="user_info.attest"
                            style="z-index: 10; width: 40rpx; height: 40rpx; position: absolute; right: 0px; bottom: -5px"
                        ></image>
                        <image class="now_level" style="height: 170rpx; width: 170rpx; position: absolute; max-width: initial" :src="user_info.avatar_frame"></image>
                    </view>
                </view>
                <view>
                    <view style="font-size: 17px; font-weight: 700" :class="'course-name ' + user_info.special">
                        <text style="vertical-align: middle">{{ user_info.user_nick_name }}</text>
                        <image
                            mode="widthFix"
                            class="now_level"
                            v-if="user_info.wear_merit"
                            :src="user_info.wear_merit"
                            style="width: 20px; vertical-align: middle; margin-left: 3px"
                        ></image>
                    </view>
                    <view v-if="user_info.tourist == 0" @tap="my_level" style="margin-top: 10px">
                        <image class="now_level" :src="user_info.level_info.level_icon" mode="widthFix" style="width: 20px; vertical-align: middle"></image>
                        <text style="font-weight: 300; letter-spacing: 0.5px; font-size: 13px; color: #666; margin-left: 5px; vertical-align: middle">
                            {{ user_info.level_info.level_name }}
                        </text>
                    </view>
                    <view @tap="onGotUserInfo" v-if="user_info.tourist == 1" style="margin-top: 10px" class="text_lin">登录</view>
                </view>
            </view>
            <view class="bg-white" style="padding: 0px 30rpx 30rpx 30rpx">
                <view class="grid col-3 margin-bottom text-center" style="border-radius: 20rpx; overflow: hidden">
                    <view class="padding-lg" style="background-color: #f9f9f9" @tap="user_url" data-index="2">
                        <view class="center_text_cu">{{ user_info.user_track }}</view>
                        <view class="center_san">我的关注</view>
                    </view>
                    <view class="padding-lg" style="background-color: #f9f9f9">
                        <view @tap="user_url" data-index="1" v-if="copyright.noble_arbor == 1">
                            <image
                                v-if="user_info.is_vip == 0"
                                :src="http_root + 'addons/yl_welore/web/static/mineIcon/user4/no_quanyi.png'"
                                style="width: 25px; height: 25px; margin-top: -1px"
                            ></image>
                            <image
                                v-if="user_info.is_vip == 1"
                                :src="http_root + 'addons/yl_welore/web/static/mineIcon/user4/quanyi.png'"
                                style="width: 25px; height: 25px; margin-top: -1px"
                            ></image>
                            <view class="center_san">{{ user_info.vip_end_time == 0 ? '未成为会员' : user_info.vip_end_time }}</view>
                        </view>
                    </view>
                    <view class="padding-lg" style="background-color: #f9f9f9" @tap="user_url" data-index="3">
                        <view class="center_text_cu">{{ user_info.user_fs }}</view>
                        <view class="center_san">我的粉丝</view>
                    </view>
                </view>
            </view>
            <view class="cu-bar bg-white">
                <view class="action">
                    <text class="cuIcon-titles text-green"></text>
                    <text class="text-xl text-bold">我的服务</text>
                </view>
            </view>
            <view class="bg-white" style="padding: 0px 30rpx">
                <view class="grid col-4 margin-bottom text-center">
                    <view class="padding" @tap="user_url" data-index="19">
                        <view>
                            <image class="di_img" :src="http_root + 'addons/yl_welore/web/static/mineIcon/user4/daka.png'"></image>
                        </view>
                        <view class="left_color">每日签到</view>
                    </view>
                    <view class="padding" @tap="user_url" data-index="18">
                        <view style="position: relative">
                            <image class="di_img" :src="http_root + 'addons/yl_welore/web/static/mineIcon/user4/renwu.png'"></image>
                            <view v-if="user_info.task_count > 0" class="cu-tag badge" style="border-radius: 50%; font-size: 24rpx; padding: 10rpx; height: 35rpx; width: 35rpx">
                                {{ user_info.task_count }}
                            </view>
                        </view>
                        <view class="left_color">任务中心</view>
                    </view>
                    <view class="padding" @tap="user_url" data-index="5">
                        <view>
                            <image class="di_img" :src="http_root + 'addons/yl_welore/web/static/mineIcon/user4/shoucang.png'"></image>
                        </view>
                        <view class="left_color">我的收藏</view>
                    </view>
                    <view class="padding" @tap="user_url" data-index="6">
                        <view>
                            <image class="di_img" :src="http_root + 'addons/yl_welore/web/static/mineIcon/user4/tuandui.png'"></image>
                        </view>
                        <view class="left_color">我的{{ design.landgrave }}</view>
                    </view>
                    <view class="padding" v-if="version == 0 && copyright.shop_arbor == 1" @tap="user_url" data-index="8">
                        <view>
                            <image class="di_img" :src="http_root + 'addons/yl_welore/web/static/mineIcon/user4/duizhang-96.png'"></image>
                        </view>
                        <view class="left_color">我的订单</view>
                    </view>
                    <view class="padding" v-if="copyright.wallet_arbor == 1 && version == 0 && user_info.conceal == 0" @tap="user_url" data-index="9">
                        <view>
                            <image class="di_img" :src="http_root + 'addons/yl_welore/web/static/mineIcon/user4/jifen.png'"></image>
                        </view>
                        <view class="left_color">我的钱包</view>
                    </view>
                    <view class="padding" @tap="user_url" data-index="16">
                        <view>
                            <image class="di_img" :src="http_root + 'addons/yl_welore/web/static/mineIcon/user4/yinhangka.png'"></image>
                        </view>
                        <view class="left_color">我的卡包</view>
                    </view>
                    <view v-if="version == 0 && open_wangpan" class="padding" @tap="user_url" data-index="26">
                        <view>
                            <image class="di_img" :src="http_root + 'addons/yl_welore/web/static/mineIcon/user4/wangpan.png'"></image>
                        </view>
                        <view class="left_color">我的网盘</view>
                    </view>
                </view>
            </view>
            <view class="cu-bar bg-white">
                <view class="action">
                    <text class="cuIcon-titles text-green"></text>
                    <text class="text-xl text-bold">常用工具</text>
                </view>
            </view>
            <view class="bg-white" style="padding: 0px 30rpx">
                <view class="grid col-4 text-center">
                    <view class="padding" @tap="user_url" data-index="24">
                        <view>
                            <image class="di_img" :src="http_root + 'addons/yl_welore/web/static/mineIcon/user4/xunzhang.png'"></image>
                        </view>
                        <view class="left_color">个性装扮</view>
                    </view>
                    <view class="padding" v-if="version == 0 && copyright.whisper_arbor == 1 && user_info.conceal == 0" @tap="user_url" data-index="20">
                        <view>
                            <image class="di_img" :src="http_root + 'addons/yl_welore/web/static/mineIcon/user4/xiehou.png'"></image>
                        </view>
                        <view class="left_color">{{ design.custom_hiss_title ? design.custom_hiss_title : '树洞' }}</view>
                    </view>
                    <view class="padding" v-if="version == 0" @tap="user_url" data-index="10">
                        <view>
                            <image class="di_img" :src="http_root + 'addons/yl_welore/web/static/mineIcon/user4/hezuo.png'"></image>
                        </view>
                        <view class="left_color">邀请好友</view>
                    </view>
                    <view class="padding" v-if="version == 0 && copyright.shop_arbor == 1 && elect_sheathe == 1" @tap="user_url" data-index="11">
                        <view>
                            <image class="di_img" :src="http_root + 'addons/yl_welore/web/static/mineIcon/user4/dianpu.png'"></image>
                        </view>
                        <view class="left_color">{{ design.mall }}</view>
                    </view>
                    <view class="padding" @tap="user_url" data-index="17">
                        <view>
                            <image class="di_img" :src="http_root + 'addons/yl_welore/web/static/mineIcon/user4/xuanxiang.png'"></image>
                        </view>
                        <view class="left_color">活动验证</view>
                    </view>
                    <view class="padding" v-if="version == 0 && copyright.tribute_arbor == 1" @tap="user_url" data-index="7">
                        <view>
                            <image class="di_img" :src="http_root + 'addons/yl_welore/web/static/mineIcon/user4/aixin.png'"></image>
                        </view>
                        <view class="left_color">收到礼物</view>
                    </view>
                    <view class="padding" v-if="version == 0 && copyright.engrave_arbor == 1" @tap="user_url" data-index="21">
                        <view>
                            <image class="di_img" :src="http_root + 'addons/yl_welore/web/static/mineIcon/user4/jiaoseguanli.png'"></image>
                        </view>
                        <view class="left_color">身份铭牌</view>
                    </view>
                    <view class="padding" v-if="version == 0 && copyright.travel_arbor == 1" @tap="user_url" data-index="22">
                        <view>
                            <image class="di_img" :src="http_root + 'addons/yl_welore/web/static/mineIcon/user4/gerenziliao_1.png'"></image>
                        </view>
                        <view class="left_color">身份认证</view>
                    </view>
                    <view
                        class="padding"
                        v-if="version == 0 && copyright.feeling_arbor == 1 && copyright.feeling_stipulate == 0 && user_info.conceal == 0"
                        @tap="user_url"
                        data-index="23"
                    >
                        <view>
                            <image class="di_img" :src="http_root + 'addons/yl_welore/web/static/mineIcon/user4/zhitiao.png'"></image>
                        </view>
                        <view class="left_color">{{ design.feel_title_em }}</view>
                    </view>
                    <view v-if="open_cord" class="padding" @tap="user_url" data-index="25">
                        <view>
                            <image class="di_img" :src="http_root + 'addons/yl_welore/web/static/mineIcon/user4/gonglve.png'"></image>
                        </view>
                        <view class="left_color">卡密兑换</view>
                    </view>
                    <view class="padding" v-if="version == 0" @tap="user_url" data-index="15">
                        <view>
                            <image class="di_img" :src="http_root + 'addons/yl_welore/web/static/mineIcon/user4/lizhi.png'"></image>
                        </view>
                        <view class="left_color">黑名单</view>
                    </view>
                    <view v-if="version == 0 && open_lost" class="padding" @tap="user_url" data-index="28">
                        <view>
                            <image class="di_img" :src="http_root + 'addons/yl_welore/web/static/mineIcon/user4/lost.png'"></image>
                        </view>
                        <view class="left_color">失物招领</view>
                    </view>
                    <view v-if="version == 0 && open_used" class="padding" @tap="user_url" data-index="31">
                        <view>
                            <image class="di_img" :src="http_root + 'addons/yl_welore/web/static/mineIcon/user4/ershou.png'"></image>
                        </view>
                        <view class="left_color">{{ design.custom_title }}</view>
                    </view>
                    <view v-if="version == 0 && open_employ" class="padding" @tap="user_url" data-index="32">
                        <view>
                            <image class="di_img" :src="http_root + 'addons/yl_welore/web/static/mineIcon/user4/pin.png'"></image>
                        </view>
                        <view class="left_color">{{ design.custom_title_em }}</view>
                    </view>
                    <view v-if="version == 0 && open_convenience" class="padding" @tap="user_url" data-index="29">
                        <view>
                            <image class="di_img" :src="http_root + 'addons/yl_welore/web/static/mineIcon/user4/bianmin.png'"></image>
                        </view>
                        <view class="left_color">{{ design.easy_title_em }}</view>
                    </view>
                    <view v-if="version == 0 && open_convenience && user_info.assistant == 1" class="padding" @tap="user_url" data-index="30">
                        <view>
                            <image class="di_img" :src="http_root + 'addons/yl_welore/web/static/mineIcon/user4/tihuo.png'"></image>
                        </view>
                        <view class="left_color">核销记录</view>
                    </view>
                    <view v-if="version == 0 && open_sweepstake" class="padding" @tap="user_url" data-index="33">
                        <view>
                            <image class="di_img" :src="http_root + 'addons/yl_welore/web/static/mineIcon/user4/xingyun.png'"></image>
                        </view>
                        <view class="left_color">{{ design.sweepstake_title ? design.sweepstake_title : '幸运抽奖' }}</view>
                    </view>
                    <view v-if="version == 0 && copyright.short_drama_arbor == 1" class="padding" @tap="user_url" data-index="34">
                        <view>
                            <image class="di_img" :src="http_root + 'addons/yl_welore/web/static/mineIcon/user4/duanju.png'"></image>
                        </view>
                        <view class="left_color">{{ design.micro_title ? design.micro_title : '短剧视频' }}</view>
                    </view>
                </view>
            </view>
            <view class="cu-bar bg-white">
                <view class="action">
                    <text class="cuIcon-titles text-green"></text>
                    <text class="text-xl text-bold">系统服务</text>
                </view>
            </view>
            <view class="bg-white" style="padding: 0px 30rpx 30rpx 30rpx">
                <view class="grid col-4 text-center">
                    <view class="padding" @tap="user_url" data-index="27" v-if="open_account">
                        <view>
                            <image class="di_img" :src="http_root + 'addons/yl_welore/web/static/mineIcon/user4/wechat.png'"></image>
                        </view>
                        <view class="left_color">公众通知</view>
                    </view>
                    <view class="padding" @tap="user_url" data-index="12">
                        <view>
                            <image class="di_img" :src="http_root + 'addons/yl_welore/web/static/mineIcon/user4/bangong.png'"></image>
                        </view>
                        <view class="left_color">服务中心</view>
                    </view>
                    <view class="padding" v-if="admin == 1" @tap="user_url" data-index="13">
                        <view>
                            <image class="di_img" :src="http_root + 'addons/yl_welore/web/static/mineIcon/user4/woderenzheng.png'"></image>
                        </view>
                        <view class="left_color">内容审核</view>
                    </view>
                    <view class="padding" @tap="user_url" data-index="14">
                        <view>
                            <image class="di_img" :src="http_root + 'addons/yl_welore/web/static/mineIcon/user4/fangchanxinxi.png'"></image>
                        </view>
                        <view class="left_color">关于我们</view>
                    </view>
                </view>
            </view>
            <view class="" style="margin-bottom: 40rpx; font-weight: 300">
                <view class="" style="word-break: break-all; text-align: center; color: var(--blue)">{{ copyright.title }}</view>
                <view class="" style="word-break: break-all; text-align: center; font-size: 12px; margin-top: 10px">
                    {{ copyright.copyright }}
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    props: ['data', 'compName'],
    computed: {
        elect_sheathe() {
            return this.$parent.$data.elect_sheathe;
        },
        design() {
            return this.$parent.$data.design;
        },
        user_info() {
            return this.$parent.$data.user_info;
        },
        animationData() {
            return this.$parent.$data.animationData;
        },
        flag() {
            return this.$parent.$data.flag;
        },
        animationDataD() {
            return this.$parent.$data.animationDataD;
        },
        http_root() {
            return this.$parent.$data.http_root;
        },
        copyright() {
            return this.$parent.$data.copyright;
        },
        version() {
            return this.$parent.$data.version;
        },
       
        open_wangpan() {
            return this.$parent.$data.open_wangpan;
        },
        open_cord() {
            return this.$parent.$data.open_cord;
        },
        open_lost() {
            return this.$parent.$data.open_lost;
        },
        open_used() {
            return this.$parent.$data.open_used;
        },
        open_employ() {
            return this.$parent.$data.open_employ;
        },
        open_convenience() {
            return this.$parent.$data.open_convenience;
        },
        open_sweepstake() {
            return this.$parent.$data.open_sweepstake;
        },
        open_account() {
            return this.$parent.$data.open_account;
        },
        admin() {
            return this.$parent.$data.admin;
        },
        
    },
    methods:{
        my_home(e) {
            this.$emit('my_home', e);
        },
        my_level(e) {
            this.$emit('my_level', e);
        },
        onGotUserInfo(e) {
            this.$emit('onGotUserInfo', e);
        },
        bid_qiandao(e) {
            this.$emit('bid_qiandao', e);
        },
        user_url(e) {
            this.$emit('user_url', e);
        }
    }
};
</script>
<style></style>

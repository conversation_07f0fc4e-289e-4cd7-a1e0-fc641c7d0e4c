<script>
import http from "./yl_welore/util/http.js";
import Store from "./yl_welore/util/store.js";
import { weAtob } from "./yl_welore/util/atob.js";
import siteInfo from "./siteinfo.js";
let store = new Store({
  debug: false,
  // 关闭内部日志的输出。
  state: {
    msg_count: 0,
    http_root: '',
    diy: {},
    ad: {},
    copyright: {},
    isIphoneX: false
  }
});
uni.onUserCaptureScreen(function (res) {
  const app = getApp();
  var currentPages = getCurrentPages();
  var _this = currentPages[currentPages.length - 1];
  console.log();
  var pagePath = _this.route;
  if (Object.keys(_this.options).length != 0) {
    var up = app.changes(_this.options);
    pagePath = pagePath + '?' + up;
  }
  var e = app.getCache("userinfo");
  var b = app.api_root + 'Service/CaptureScreen';
  var e = app.getCache("userinfo");
  var params = new Object();
  params.token = e.token;
  params.openid = e.openid;
  params.page = pagePath;
  params.uid = e.uid;
  params.activeId = _this.data.activeId;
  http.POST(b, {
    params: params,
    success: function (red) {
      console.log(red);
    },
    fail: function () { }
  });
});
// wx.setWindowSize({
//   width:375,
//   height:734
// })
export default {
  globalData: {
    appid: 'qtz',
    userInfo: null,

    tabBar: {
      "backgroundColor": "#ffffff",
      "color": "#979795",
      "selectedColor": "#1c1c1b",
      "version": 1,
      "list": [{
        "pagePath": "/yl_welore/pages/index/index",
        "iconPath": "",
        "selectedIconPath": "",
        "text": "",
        "isSpecial": false,
        "home_s": 0
      }, {
        "pagePath": "/yl_welore/pages/circle/index",
        "iconPath": "",
        "selectedIconPath": "",
        "text": "",
        "isSpecial": false,
        "home_s": 0
      }, {
        "pagePath": "",
        "iconPath": "",
        "isSpecial": true,
        "text": "",
        "home_s": 0
      }, {
        "pagePath": "/yl_welore/pages/user_smail/index",
        "iconPath": "",
        "selectedIconPath": "",
        "text": "",
        "isSpecial": false,
        "home_s": 1
      }, {
        "pagePath": "/yl_welore/pages/user/index",
        "iconPath": "",
        "selectedIconPath": "",
        "text": "",
        "isSpecial": false,
        "home_s": 0
      }]
    },

    store: store,
    version: '1.2.17',
    api_root: '',

    // api地址
    http_root: '',

    // 域名地址
    siteInfo: siteInfo,

    changes(obj) {
      var arr = [];
      for (var p in obj) if (obj.hasOwnProperty(p)) {
        arr.push(encodeURIComponent(p) + "=" + encodeURIComponent(obj[p]));
      }
      return arr.join("&");
    },

    compareVersion(v1, v2) {
      //V1=V2相等=0  V1<V2小于-1   V1>V2大于1
      v1 = v1.split('.');
      v2 = v2.split('.');
      var len = Math.max(v1.length, v2.length);
      while (v1.length < len) {
        v1.push('0');
      }
      while (v2.length < len) {
        v2.push('0');
      }
      for (var i = 0; i < len; i++) {
        var num1 = parseInt(v1[i]);
        var num2 = parseInt(v2[i]);
        if (num1 > num2) {
          return 1;
        } else if (num1 < num2) {
          return -1;
        }
      }
      return 0;
    },

    editTabbar() {
      let tabbar = this.tabBar;
      let currentPages = getCurrentPages();
      let _this = currentPages[currentPages.length - 1];
      let pagePath = _this.route;
      var is_diy = uni.getStorageSync('is_diy')['pattern_data'];
      if (!is_diy) {
        return;
      }
      var elect_sheathe = uni.getStorageSync('is_diy')['elect_sheathe'];
      var shop_arbor = uni.getStorageSync('is_diy')['shop_arbor'];
      tabbar.version = uni.getStorageSync('is_diy')['version'];
      tabbar.backgroundColor = is_diy.style['backcolor'];
      tabbar.color = is_diy.style['font_color'];
      tabbar.selectedColor = is_diy.style['font_color_active'];
      tabbar.list[0].iconPath = is_diy['home']['images']['img'];
      tabbar.list[0].selectedIconPath = is_diy['home']['images']['img_active'];
      tabbar.list[0].text = is_diy['home']['title'];
      tabbar.list[1].iconPath = is_diy['plaza']['images']['img'];
      tabbar.list[1].selectedIconPath = is_diy['plaza']['images']['img_active'];
      tabbar.list[1].text = is_diy['plaza']['title'];
      tabbar.list[2].iconPath = is_diy['release']['images']['img'];
      tabbar.list[2].selectedIconPath = is_diy['release']['images']['img_active'];
      tabbar.list[2].text = is_diy['release']['title'];
      if (elect_sheathe == 0 && shop_arbor == 1) {
        tabbar.list[3].pagePath = '/yl_welore/pages/shell_mall/index';
      } else {
        tabbar.list[3].pagePath = '/yl_welore/pages/user_smail/index';
      }
      tabbar.list[3].iconPath = is_diy['goods']['images']['img'];
      tabbar.list[3].selectedIconPath = is_diy['goods']['images']['img_active'];
      tabbar.list[3].text = is_diy['goods']['title'];
      tabbar.list[4].iconPath = is_diy['user']['images']['img'];
      tabbar.list[4].selectedIconPath = is_diy['user']['images']['img_active'];
      tabbar.list[4].text = is_diy['user']['title'];
      pagePath.indexOf('/') != 0 && (pagePath = '/' + pagePath);
      for (let i in tabbar.list) {
        tabbar.list[i].selected = false;
        tabbar.list[i].pagePath == pagePath && (tabbar.list[i].selected = true);
      }
      _this.$vm.tabbar = tabbar;
    },

    /**
     * 设置api地址
     */
    setApiRoot() {
      var dateList = this.siteInfo.siteroot.split("app/index.php");
      this.api_root = dateList[0] + 'addons/yl_welore/web/index.php?s=/api/';
      this.http_root = dateList[0];
      this.store.setState({
        http_root: dateList[0]
      });
    },

    /*
     *创建缓存
     * @param e 缓存名称
     * @param t 缓存值
     * @param i 缓存时间
     */
    setCache: function (e, t, i) {
      var n = +new Date() / 1000,
        a = true,
        o = {
          expire: i ? parseInt(i) : 0,
          value: t
        };
      try {
        uni.setStorageSync(e + this.appid, o);
      } catch (e) {
        a = false;
      }
      return a;
    },

    /*
     *获取缓存
     * @param e 缓存名称
     * @param t 缓存值
     */
    getCache: function (e, t) {
      var i = +new Date() / 1000,
        n = "";
      i = parseInt(i);
      try {
        n = uni.getStorageSync(e + this.appid), n.expire > i || 0 == n.expire ? n = n.value : (n = "", this.removeCache(e));
      } catch (e) {
        n = void 0 === t ? "" : t;
      }
      return n = n || "";
    },

    /*
     *清除缓存
     * @param e 缓存名称
     */
    removeCache: function (e) {
      var t = true;
      try {
        uni.removeStorageSync(e + this.appid);
      } catch (e) {
        t = false;
      }
      return t;
    },

    __CheckTheCertification(user_info) {
      var key = this.__PlugUnitScreen('00e58b17f1821b6616768d50b2367585');
      if (key && user_info.attest_info == '') {
        uni.showModal({
          title: '提示',
          content: '需要认证后才能发帖！',
          cancelText: '先不认证',
          confirmText: '前往认证',
          showCancel: true,
          success(res) {
            if (res.confirm) {
              uni.navigateTo({
                url: '/yl_welore/pages/packageE/certification_list/index'
              });
            } else if (res.cancel) {
              console.log('用户点击取消');
            }
          }
        });
        return true;
      } else {
        return false;
      }
    },

    __PlugUnitScreen(key) {
      var item = this.getCache('__Plug');
      for (var i = 0; i <= item.length; i++) {
        if (item[i] == key) {
          return true;
        }
      }
      return false;
    },

    __PlugUnit() {
      var that = this;
      var url = this.api_root + 'Plugunit/get_plug';
      var params = new Object();
      params.much_id = this.siteInfo.uniacid;
      uni.request({
        url: url,
        data: params,
        method: 'POST',
        header: {
          'content-type': 'application/x-www-form-urlencoded'
        },
        success: function (res) {
          var d = JSON.parse(decodeURIComponent(weAtob(res.data)));
          console.log(d);
          that.setCache('__Plug', d);
        },
        fail: function () {
          //requestHandler.fail()
        },
        complete: function () {
          // complete  
        }
      });
    },
    claerStor() {
      var v = this.getCache('yl_version');
      console.log(v);
      if (v == '') {
        var v2 = -1;
      } else {
        var v2 = this.compareVersion(v, this.version); //相等=0  小于-1  大于1
      }
      this.setCache('yl_version', this.version);
      return v2;
    },
    checkToken() {
      return new Promise((resolve, reject) => {
        var b = this.api_root + 'Check/check_token';
        var e = this.getCache("userinfo");
        var params = new Object();
        params.token = e.token;
        params.openid = e.openid;
        http.POST(b, {
          params: params,
          success: function (res) {
            resolve(res.data.status);
          },
          fail: function () {
            uni.showModal({
              title: '提示',
              content: '网络繁忙，请稍候重试！',
              showCancel: false,
              success: function (res) { }
            });
            reject('网络请求失败');
          },
        });
      });
    },
    /**
            * 登录公共方法 
            * @param {Function} returnA - 成功回调 
            * @param {Function} returnB - 失败回调 
            */
    getLogin(returnA, returnB) {
      var i = 0; // 1执行登录 0不执行登录
      var e = this.getCache("userinfo");
      var v = this.claerStor();

      //字段为空执行登陆
      if (!e) {
        i = 1;
        this._executeLogin(i, v, e, returnA, returnB);
        return;
      }

      // 有userinfo的情况下进行进一步检查
      if (typeof (e.token_impede) == 'undefined') {
        i = 1;
      } else {
        //字段0或者小于当前时间执行登陆
        var t = parseInt(+new Date / 1000);
        if (e.token_impede == 0 || t >= e.token_impede) {
          i = 1;
        }
      }

      // 使用Promise处理checkToken异步调用
      this.checkToken().then((tokenStatus) => {
        console.log('*******', tokenStatus);
        if (tokenStatus == 'no') {
          i = 1;
        }

        // 执行登录逻辑
        this._executeLogin(i, v, e, returnA, returnB);
      }).catch((error) => {
        console.log('checkToken失败:', error);
        // token检查失败，执行登录
        i = 1;
        this._executeLogin(i, v, e, returnA, returnB);
      });
    },

    // 提取登录执行逻辑为独立方法
    _executeLogin(i, v, e, returnA, returnB) {
      if (i == 0 && v != -1) {
        returnA(e);
      } else {
        uni.login({
          success: (loginRes) => {
            const params = { code: loginRes.code };
            // 第一层请求：获取 openid 和 session_key 
            http.POST(this.api_root + 'Login/index', {
              params: params,
              success: (openRes) => {
                console.log(openRes);
                if (openRes.data.code !== 0) {
                  uni.showModal({ title: '提示', content: '小程序参数错误' });
                  returnB({ code: -1, msg: '参数校验失败' }); // 返回明确错误码 
                  return;
                }
                // 第二层请求：注册游客信息 
                const authData = {
                  openid: openRes.data.info.openid,
                  session_key: openRes.data.info.session_key
                };
                http.POST(this.api_root + 'Login/add_tourist', {
                  params: authData,
                  success: (userRes) => {
                    if (userRes.data.status === 'error') {
                      returnB({ code: -2, msg: '登录态失效' }); // 细分错误类型 
                      return;
                    }
                    this.setCache("userinfo", userRes.data.info);
                    console.log(userRes.data.info);
                    returnA(userRes.data.info);  // 返回用户核心数据 
                  },
                  fail: (postErr) => returnB(postErr) // 捕获网络异常 
                });
              },
              fail: (authErr) => returnB(authErr)
            });
          },
          fail: (loginErr) => returnB(loginErr) // 微信登录失败 
        });
      }

    },
    /**
     * 订阅信息
     */
    subscribe_message(successFn, failFn) {
      var b = this.api_root + 'Subscribe/get_msg';
      var e = this.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.uid = e.uid;
      http.POST(b, {
        params: params,
        success: function (res) {
          successFn && successFn(res.data);
        },
        fail: function () {
          failFn && failFn('网络繁忙，请稍候重试！');
        }
      });
    },

    authorization(a, b, c, return_data) {
      var data = [];
      if (a) {
        data.push(a);
      }
      if (b) {
        data.push(b);
      }
      if (c) {
        data.push(c);
      }
      console.log(data);
      uni.requestSubscribeMessage({
        tmplIds: data,
        success(res) {
          for (var key in res) {
            if (key != 'errMsg') {
              if (res[key] == 'reject') {
                uni.showModal({
                  title: '订阅消息',
                  content: '您已拒绝了订阅消息，如需重新订阅请前往设置打开。',
                  confirmText: '去设置',
                  success: res => {
                    if (res.confirm) {
                      uni.openSetting({});
                    }
                  }
                });
                return_data('error');
              } else {
                return_data('success');
                // wx.showToast({
                //   title: '订阅成功'
                // })
              }
            }
          }
        },

        fail(res) {
          console.log(res);
          uni.showModal({
            title: '订阅消息',
            content: '您关闭了"接收订阅信息"，请前往设置打开！',
            confirmText: '去设置',
            showCancel: false,
            success: res => {
              if (res.confirm) {
                uni.openSetting({});
              }
            }
          });
          return_data('error');
        }
      });
    }
  },
  /**
   * 小程序初始化
   */
  onLaunch: function (options) {
    uni.hideTabBar();
    this.globalData.setApiRoot();
    this.globalData.__PlugUnit();
    //缓存当前版本号
    //this.setCache('yl_version', this.version);
  }
};
</script>
<style>
@import "./yl_welore/colorui/main.css";
@import "./yl_welore/colorui/icon.css";
@import "./yl_welore/colorui/icon1.css";
@import "./yl_welore/style/text_style.css";

.now_level {
  height: 0px;
  width: 0px;
}

.v-a-m {
  vertical-align: middle;
}

.text_lin {
  font-weight: 300;
}

.center_text {
  display: -moz-box;
  /*兼容Firefox*/
  display: -webkit-box;
  /*兼容FSafari、Chrome*/
  -moz-box-align: center;
  /*兼容Firefox*/
  -webkit-box-align: center;
  /*兼容FSafari、Chrome */
  -moz-box-pack: center;
  /*兼容Firefox*/
  -webkit-box-pack: center;
  /*兼容FSafari、Chrome */
}

.text_num_3 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: break-word;
  white-space: normal !important;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  letter-spacing: 2px;
  word-break: break-all;
}

.text_num {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: break-word;
  white-space: normal !important;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  letter-spacing: 1px;
  word-break: break-all;
}

.text_num_1 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: break-word;
  white-space: normal !important;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  letter-spacing: 1px;
  word-break: break-all;
}

.font_weight {
  font-weight: 300;
}

/* 隐藏滚动条 */
::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}

.notes_top_color {
  background-color: rgb(26, 20, 54);
}

.welfare_top_color {
  background-color: rgb(126, 108, 253);
}

/* 字体 */
.font-yl-1 {
  font-family: "Courier New", Courier, monospace;
}

.font-yl-2 {
  font-family: "Microsoft Yahei";
}

.font-yl-3 {
  font-family: Arial, Helvetica, sans-serif;
}

.font-yl-4 {
  font-family: "Franklin Gothic Medium", "Arial Narrow", Arial, sans-serif;
}

/* 列表动画 */
[class*=animation-] {
  animation-duration: .5s;
  animation-timing-function: ease-out;
  animation-fill-mode: both
}

.animation-slide-bottom {
  animation-name: slide-bottom;

}

@keyframes slide-bottom {
  0% {
    opacity: 0;
    transform: translateY(100%)
  }

  100% {
    opacity: 1;
    transform: translateY(0)
  }
}

.align-center {
  align-items: center !important;
}
</style>
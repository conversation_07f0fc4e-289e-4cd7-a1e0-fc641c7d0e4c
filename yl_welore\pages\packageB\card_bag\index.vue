<template>
    <view>
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">我的卡包</view>
        </cu-custom>

        <view style="margin: 30rpx; padding: 30rpx; background-color: #ffffff; border-radius: 5px" v-for="(item, index) in list" :key="index">
            <view class="flex solid-bottom justify-between align-center" style="padding: 10px">
                <view>
                    <image :src="item.user.user_head_sculpture" style="width: 35px; height: 35px; vertical-align: middle; border-radius: 50%"></image>
                    <text style="vertical-align: middle; margin-left: 10px">{{ item.user.user_nick_name }}</text>
                </view>
                <view>
                    <view v-if="item.is_write_off == 0" class="cu-tag radius">未验证</view>
                    <view v-else class="cu-tag radius bg-green">已验证</view>
                </view>
            </view>

            <view
                @tap="get_position(item.brisk_address, item.brisk_address_latitude, item.brisk_address_longitude)"
                :data-pos_name="item.brisk_address"
                :data-latitude="item.brisk_address_latitude"
                :data-longitude="item.brisk_address_longitude"
                style="margin: 10px"
            >
                <text>活动地址：{{ item.brisk_address }}</text>
                <text class="cuIcon-location lg text-gray"></text>
            </view>

            <view style="margin: 10px">
                <text>活动时间：{{ item.start_time }} - {{ item.end_time }}</text>
            </view>

            <view style="margin: 10px">
                <text>活动人数：{{ item.number_of_people == 0 ? '不限人数' : item.number_of_people }}</text>
            </view>

            <view style="text-align: center">
                <text v-if="item.overdue == 2" class="text_lin" style="color: #ff0033">活动已过期</text>
            </view>

            <view class="font-yl-1" style="text-align: center; margin: 40rpx">
                <text :style="'font-size: 80rpx;font-weight: 700;letter-spacing: 10px;' + (item.is_write_off == 1 ? 'text-decoration:line-through;' : '')">
                    {{ item.rand_captcha }}
                </text>
            </view>

            <view style="text-align: center; margin: 30rpx">
                <navigator :url="'/yl_welore/pages/packageA/article/index?id=' + item.paper.id + '&type=' + item.paper.study_type" hover-class="none">
                    <text style="font-size: 15px">查看详情</text>
                    <text class="cuIcon-right lg text-gray"></text>
                </navigator>
            </view>
        </view>
        <view :class="'cu-load ' + (list.length == 0 ? 'over' : '')"></view>
    </view>
</template>

<script>
import http from '../../../util/http.js';
export default {
    data() {
        return {
            list: []
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {},
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {
        this.get_card_list();
    },
    /**
     * 下拉刷新
     */
    onPullDownRefresh() {
        //模拟加载
        setTimeout(function () {
            uni.hideNavigationBarLoading(); //完成停止加载
            uni.stopPullDownRefresh(); //停止下拉刷新
        }, 1500);
        this.get_card_list();
    },
    methods: {
        /**
         * 列表
         */
        get_card_list() {
            var app = getApp();
            var b = app.globalData.api_root + 'User/get_card_list';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    this.list = res.data;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },

        /**
         * 打开地图
         */
        get_position(name, latitude, longitude) {
            var a = Number(latitude);
            var o = Number(longitude);
            if (a && o) {
                uni.openLocation({
                    latitude: a,
                    longitude: o,
                    name: name
                });
            }
        }
    }
};
</script>
<style>
page {
    background-color: #f1f1f1;
}
</style>

<template>
    <view style="padding: 40rpx">
        <view class="flex align-center">
            <view>
                <image :src="u.user_head_sculpture" style="width: 100rpx; height: 100rpx"></image>
            </view>
            <view style="margin-left: 20rpx">
                <text style="font-size: 32rpx; color: #999999; letter-spacing: 2rpx">{{ u.autograph }}</text>
            </view>
        </view>
        <view class="cu-list menu sm-border" style="margin-top: 40rpx">
            <view @tap="open_url" data-type="1" class="cu-item arrow" style="background: transparent; color: #000000">
                <view>
                    <text class="cicon-record" style="color: #000000; font-size: 40rpx; vertical-align: middle"></text>
                    <text style="color: #672917; margin-left: 30rpx; vertical-align: middle">我的剧集</text>
                </view>
            </view>
            <view @tap="open_url" data-type="3" class="cu-item arrow" style="background: transparent; color: #000000">
                <view>
                    <text class="cicon-favorite" style="color: #000000; font-size: 40rpx; vertical-align: middle"></text>
                    <text style="color: #672917; margin-left: 30rpx; vertical-align: middle">我的喜欢</text>
                </view>
            </view>
            <view @tap="open_url" data-type="4" class="cu-item arrow" style="background: transparent; color: #000000">
                <view>
                    <text class="cicon-time" style="color: #000000; font-size: 40rpx; vertical-align: middle"></text>
                    <text style="color: #672917; margin-left: 30rpx; vertical-align: middle">观看历史</text>
                </view>
            </view>
            <view @tap="open_url" data-type="5" class="cu-item arrow" style="background: transparent; color: #000000">
                <view>
                    <text class="cicon-order" style="color: #000000; font-size: 40rpx; vertical-align: middle"></text>
                    <text style="color: #672917; margin-left: 30rpx; vertical-align: middle">用户协议</text>
                </view>
            </view>
            <view @tap="open_url" data-type="6" class="cu-item arrow" style="background: transparent; color: #000000">
                <view>
                    <text class="cicon-info" style="color: #000000; font-size: 40rpx; vertical-align: middle"></text>
                    <text style="color: #672917; margin-left: 30rpx; vertical-align: middle">免责声明</text>
                </view>
            </view>
        </view>
    </view>
</template>
<script>
export default {
    props: ['data', 'compName'],
    computed: {
        u() {
            return this.$parent.$data.u;
        },
    },
    methods:{
        open_url(e){
            this.$emit('open_url', e);
        }
    }
};
</script>
<style></style>

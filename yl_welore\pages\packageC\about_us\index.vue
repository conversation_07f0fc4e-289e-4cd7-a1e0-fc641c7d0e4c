<template>
<view>
<cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
  <view slot="backText">返回</view>
  <view slot="content" style="color: #2c2b2b;font-weight: 600; font-size: 36rpx;">关于我们</view>
</cu-custom>
<view>
            <!-- #ifdef MP-WEIXIN -->
            <!-- [miniprogram-to-uniapp] 公众号关注组件 仅微信小程序支持 -->
            <official-account></official-account>
            <!-- #endif -->

            <!-- #ifndef MP-WEIXIN -->
            <view>当前为非微信小程序环境，不支持公众号关注组件，请自行调整当前节点内容！</view>
            <!-- #endif -->
        </view>
<view style="padding:20px;">
  <view style="margin-top:20px;margin-bottom:20px;text-align: center;">

    <image :src="copyright.sgraph" style="width:100px;height:100px;border-radius:50%;"></image>
    <view>
      <view style="word-break:break-all;color:#000">{{copyright.title}}</view>
    </view>
    <view style="word-break:break-all;margin:10px 0px;font-size: 12px;">{{copyright.copyright}}</view>
    <view @tap="call_phone" :data-phone="copyright.cust_phone">
      <view style="word-break:break-all;color: #006699;">客服：{{copyright.cust_phone}}</view>
    </view>
    <view @tap="dian">
      <view style="word-break:break-all;margin:10px 0px;font-size: 12px;">小程序版本：v{{version}}</view>
    </view>
  </view>
</view>
<view v-if="user_show" style="color: rgb(255,255,240);text-align: center;letter-spacing: 1px;font-size: 20px;position: fixed;left: 0;right: 0;bottom: 10%;">{{user.id}}-{{user.much_id}}</view>
</view>
</template>

<script>
var app = getApp();
export default {
  data() {
    return {
      info: [],
      version: app.globalData.version,
      user: {},
      user_show: false,
      index: 0,
      copyright: {}
    };
  },
  
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.copyright = getApp().globalData.store.getState().copyright;
    //this.get_help_info();
  },
  
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    var e = app.globalData.getCache("userinfo");
    this.user = e;
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    var forward = app.globalData.forward;
    console.log(forward);
    if (forward) {
      return {
        title: forward.title,
        path: '/yl_welore/pages/index/index',
        imageUrl: forward.reis_img
      };
    } else {
      return {
        title: '您的好友给您发了一条信息',
        path: '/yl_welore/pages/index/index'
      };
    }
  },

  methods: {
    dian() {
      this.index++;
      console.log(this.index);
      if (this.index >= 10) {
        this.user_show = true;
      }
    },

    /**
     * 打电话
     */
    call_phone(e) {
      var phone = e.currentTarget.dataset.phone;
      uni.makePhoneCall({
        phoneNumber: phone
      });
    }
  }
};
</script>
<style >
 page{
  background-color: #fff;
}  
</style>
<template>
    <view>
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="false">
            <view v-if="elect_sheathe == 0 && design.shop_arbor == 1" slot="left_z">
                <view style="display: flex; justify-content: space-around; flex-direction: column; height: 100%">
                    <navigator url="/yl_welore/pages/user_smail/index" hover-class="none">
                        <view class="bg-white cu-avatar radius" style="width: 35px; height: 35px">
                            <image src="/static/yl_welore/style/icon/bgt.png" style="width: 35px; height: 35px"></image>
                            <view class="cu-tag badge">{{ user_info.user_male }}</view>
                        </view>
                    </navigator>
                </view>
            </view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">个人中心</view>
        </cu-custom>
        <view class="page__bd page__bd_spacing" style="background-color: #ffffff; border-top: 1rpx solid #f9f9f9">
            <view class="">
                <view style="position: relative">
                    <view class="placeholder_login" style="position: relative">
                        <view style="width: 100%; text-align: left; position: relative; top: 10px">
                            <view class="cu-list menu-avatar">
                                <view class="cu-item">
                                    <view @tap="my_home" class="cu-avatar round lg" :style="'background-image:url(' + user_info.user_head_sculpture + ');'">
                                        <view style="z-index: 100" :class="'cu-tag badge ' + (user_info.gender == 2 ? 'cuIcon-female bg-pink' : 'cuIcon-male bg-blue')"></view>
                                        <image
                                            v-if="user_info.attest"
                                            :src="user_info.attest"
                                            style="width: 30rpx; height: 30rpx; position: absolute; right: 0px; bottom: -5px; z-index: 100"
                                        ></image>
                                        <!-- <image wx:if="{{admin==1}}" src='../../style/icon/icon_call_group.png'
                  style='width:60px;height:24px;position:absolute;left:0px;bottom:-5px;z-index: 100;'></image> -->
                                        <image
                                            class="now_level"
                                            style="height: 110rpx; width: 110rpx; position: absolute; max-width: initial"
                                            :src="user_info.avatar_frame"
                                        ></image>
                                    </view>
                                    <view class="content">
                                        <view class="text-sm">
                                            <view class="text_one" style="font-size: 16px" @tap="my_home">
                                                <text :class="user_info.special">{{ user_info.user_nick_name }}</text>
                                            </view>
                                            <view>
                                                <image
                                                    mode="heightFix"
                                                    class="now_level"
                                                    v-if="user_info.wear_merit"
                                                    :src="user_info.wear_merit"
                                                    style="height: 36rpx; margin-left: 3px"
                                                ></image>
                                            </view>
                                        </view>

                                        <view class="text-gray text-sm flex justify-between">
                                            <view v-if="user_info.tourist == 0" @tap="my_level">
                                                <image
                                                    class="now_level"
                                                    :src="user_info.level_info.level_icon"
                                                    mode="widthFix"
                                                    style="width: 40rpx; vertical-align: middle"
                                                ></image>
                                                <text style="vertical-align: middle; letter-spacing: 0.5px; font-size: 12px; color: #666; margin-left: 5px">
                                                    {{ user_info.level_info.level_name }}
                                                </text>
                                            </view>
                                            <view>
                                                <button
                                                    v-if="user_info.tourist == 1"
                                                    style="color: #ffcc00; font-size: 14px; z-index: 200; margin-top: 5px"
                                                    @tap="onGotUserInfo"
                                                    hover-class="none"
                                                >
                                                    立即登录
                                                </button>
                                            </view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                    <view v-if="user_info.check_chou == 0">
                        <image
                            v-if="user_info.is_sign == 0"
                            @tap="bid_qiandao"
                            :animation="animationData"
                            src="/static/yl_welore/style/icon/no_qian.png"
                            style="height: 60px; width: 60px; position: absolute; top: 14px; right: 61px"
                        ></image>
                        <image
                            mode="widthFix"
                            v-if="user_info.is_sign == 1 && flag == false"
                            :animation="animationDataD"
                            src="/static/yl_welore/style/icon/qian.png"
                            style="width: 150px; position: absolute; top: -29px; right: 16px; opacity: 0"
                        ></image>
                        <image
                            v-if="flag == true && user_info.is_sign == 1"
                            src="/static/yl_welore/style/icon/qian.png"
                            style="height: 60px; width: 60px; position: absolute; top: 16px; right: 61px"
                        ></image>
                    </view>
                    <button
                        v-if="user_info.check_chou == 1"
                        @tap="user_url"
                        data-index="19"
                        style="position: absolute; top: 30%; right: 5%"
                        class="cu-btn round bg-white shadow button-hover"
                    >
                        <image v-if="http_root" :src="http_root + 'addons/yl_welore/web/static/wechat/sign_in.png'" style="width: 15px; height: 15px"></image>
                        <text style="margin-left: 5px">签到</text>
                    </button>
                </view>
            </view>
        </view>

        <view class="bg-white padding-xs">
            <view class="grid col-3 text-center">
                <view @tap="user_url" data-index="2">
                    <view class="number_text">{{ user_info.user_track }}</view>
                    <view class="info_text">我的关注</view>
                </view>
                <view @tap="user_url" data-index="18">
                    <view class="number_text" style="position: relative">
                        <image v-if="http_root" :src="http_root + 'addons/yl_welore/web/static/applet_icon/task.png'" style="width: 30px; height: 30px"></image>
                        <view
                            v-if="user_info.task_count > 0"
                            class="cu-tag badge"
                            style="right: 55rpx; border-radius: 50%; font-size: 24rpx; padding: 10rpx; height: 35rpx; width: 35rpx"
                        >
                            {{ user_info.task_count }}
                        </view>
                    </view>
                    <view class="info_text">任务中心</view>
                </view>
                <view @tap="user_url" data-index="3">
                    <view class="number_text">{{ user_info.user_fs }}</view>
                    <view class="info_text">我的粉丝</view>
                </view>
            </view>
            <view class="vip_style" @tap="user_url" data-index="1" v-if="copyright.noble_arbor == 1 && version == 0">
                <view class="flex solid-bottom padding align-center" style="height: 100%">
                    <view class="">
                        <image
                            v-if="user_info.is_vip == 0"
                            :src="http_root + 'addons/yl_welore/web/static/applet_icon/novip.png'"
                            style="width: 25px; height: 25px; vertical-align: middle"
                        ></image>
                        <image
                            v-if="user_info.is_vip == 1"
                            :src="http_root + 'addons/yl_welore/web/static/applet_icon/vip.png'"
                            style="width: 25px; height: 25px; vertical-align: middle"
                        ></image>
                    </view>
                    <view style="color: #f8e0b4; font-size: 16px; font-weight: 500; letter-spacing: 2px; margin-left: 10px">
                        {{ user_info.vip_end_time_tmpl == 0 ? '立即开通会员' : user_info.vip_end_time_tmpl }}
                    </view>
                    <view style="position: absolute; right: 40rpx">
                        <button v-if="user_info.is_vip == 0" style="background-image: linear-gradient(to right, #fcefd7 0%, #eed6a3 70%); font-size: 12px" class="cu-btn round sm">
                            立即开通
                        </button>
                        <button v-if="user_info.is_vip == 1" style="background-image: linear-gradient(to right, #fcefd7 0%, #eed6a3 70%); font-size: 12px" class="cu-btn round sm">
                            查看权限
                        </button>
                    </view>
                </view>
            </view>
            <view v-else style="margin-top: 20px"></view>
            <view class="grid col-4 text-center">
                <view @tap="user_url" data-index="5">
                    <view class="number_text">
                        <image v-if="http_root"
                            :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/sc.png'"
                            style="margin-right: 5px; vertical-align: sub; width: 20px; height: 20px"
                        ></image>
                    </view>
                    <view class="info_text">我的收藏</view>
                </view>
                <view @tap="user_url" data-index="6">
                    <view class="number_text">
                        <image v-if="http_root"
                            :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/quanzi.png'"
                            style="margin-right: 5px; vertical-align: sub; width: 20px; height: 20px"
                        ></image>
                    </view>
                    <view class="info_text">我的{{ design.landgrave }}</view>
                </view>
                <view @tap="user_url" data-index="16">
                    <view class="number_text">
                        <image v-if="http_root"
                            :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/kb.png'"
                            style="margin-right: 5px; vertical-align: sub; width: 20px; height: 20px"
                        ></image>
                    </view>
                    <view class="info_text">我的卡包</view>
                </view>
                <view @tap="user_url" data-index="12">
                    <view class="number_text">
                        <image v-if="http_root"
                            :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/kf.png'"
                            style="margin-right: 5px; vertical-align: sub; width: 20px; height: 20px"
                        ></image>
                    </view>
                    <view class="info_text">服务中心</view>
                </view>
            </view>
        </view>

        <view class="cu-list menu sm-border" style="padding-top: 10rpx">
            <view @tap="user_url" data-index="20" class="cu-item arrow" hover-class="none" v-if="version == 0 && copyright.whisper_arbor == 1 && user_info.conceal == 0">
                <view class="content" style="position: relative">
                    <view
                        v-if="$state.slogin == 0 && user_info.secret > 0"
                        class="cu-tag badge"
                        style="top: 10rpx; border-radius: 50%; font-size: 24rpx; padding: 10rpx; height: 35rpx; width: 35rpx"
                    >
                        {{ user_info.secret }}
                    </view>
                    <image v-if="http_root"
                        :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/secret.png'"
                        style="margin-right: 5px; vertical-align: sub; width: 20px; height: 20px"
                    ></image>
                    <text class="margin-left-sm">{{ design.custom_hiss_title ? design.custom_hiss_title : '树洞' }}</text>
                </view>
            </view>
            <view v-if="version == 0 && copyright.tribute_arbor == 1" @tap="user_url" data-index="7" class="cu-item arrow" hover-class="none">
                <view class="content">
                    <image  :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/lw.png'" style="margin-right: 5px; vertical-align: sub; width: 20px; height: 20px"></image>
                    <text class="margin-left-sm">收到礼物</text>
                </view>
            </view>
            <view v-if="version == 0 && copyright.shop_arbor == 1" @tap="user_url" data-index="8" class="cu-item arrow" hover-class="none">
                <view class="content">
                    <image
                        :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/dui.png'"
                        style="margin-right: 5px; vertical-align: sub; width: 20px; height: 20px"
                    ></image>
                    <text class="margin-left-sm">我的订单</text>
                </view>
            </view>
            <view v-if="copyright.wallet_arbor == 1 && version == 0 && user_info.conceal == 0" @tap="user_url" data-index="9" class="cu-item arrow" hover-class="none">
                <view class="content">
                    <image
                        :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/money.png'"
                        style="margin-right: 5px; vertical-align: sub; width: 20px; height: 20px"
                    ></image>
                    <text class="margin-left-sm">我的钱包</text>
                </view>
            </view>
            <view v-if="version == 0 && open_wangpan" @tap="user_url" data-index="26" class="cu-item arrow" hover-class="none">
                <view class="content">
                    <image 
                        :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/wangpan.png'"
                        style="margin-right: 5px; vertical-align: sub; width: 20px; height: 20px"
                    ></image>
                    <text class="margin-left-sm">我的网盘</text>
                </view>
            </view>
            <view v-if="version == 0" @tap="user_url" data-index="10" class="cu-item arrow" hover-class="none">
                <view class="content">
                    <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/yq.png'" style="margin-right: 5px; vertical-align: sub; width: 20px; height: 20px"></image>
                    <text class="margin-left-sm">邀请好友</text>
                </view>
            </view>
            <view v-if="version == 0 && copyright.shop_arbor == 1 && elect_sheathe == 1" @tap="user_url" data-index="11" class="cu-item arrow" hover-class="none">
                <view class="content">
                    <image  :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/jf.png'" style="margin-right: 5px; vertical-align: sub; width: 20px; height: 20px"></image>
                    <text class="margin-left-sm">{{ design.mall }}</text>
                </view>
            </view>
            <view v-if="version == 0" @tap="user_url" data-index="15" class="cu-item arrow" hover-class="none">
                <view class="content">
                    <image 
                        :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/lahei.png'"
                        style="margin-right: 5px; vertical-align: sub; width: 20px; height: 20px"
                    ></image>
                    <text class="margin-left-sm">黑名单</text>
                </view>
            </view>
            <view v-if="version == 0 && copyright.engrave_arbor == 1" @tap="user_url" data-index="21" class="cu-item arrow" hover-class="none">
                <view class="content">
                    <image 
                        :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/name_p.png'"
                        style="margin-right: 5px; vertical-align: sub; width: 20px; height: 20px"
                    ></image>
                    <text class="margin-left-sm">身份铭牌</text>
                </view>
            </view>
            <view v-if="version == 0 && copyright.travel_arbor == 1" @tap="user_url" data-index="22" class="cu-item arrow" hover-class="none">
                <view class="content">
                    <image 
                        :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/certification.png'"
                        style="margin-right: 5px; vertical-align: sub; width: 20px; height: 20px"
                    ></image>
                    <text class="margin-left-sm">身份认证</text>
                </view>
            </view>
            <view v-if="open_cord" @tap="user_url" data-index="25" class="cu-item arrow" hover-class="none">
                <view class="content">
                    <image 
                        :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/duihuan.png'"
                        style="margin-right: 5px; vertical-align: sub; width: 20px; height: 20px"
                    ></image>
                    <text class="margin-left-sm">卡密兑换</text>
                </view>
            </view>
            <view
                v-if="version == 0 && copyright.feeling_arbor == 1 && copyright.feeling_stipulate == 0 && user_info.conceal == 0"
                @tap="user_url"
                data-index="23"
                class="cu-item arrow"
                hover-class="none"
            >
                <view class="content">
                    <image 
                        :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/friends.png'"
                        style="margin-right: 5px; vertical-align: sub; width: 20px; height: 20px"
                    ></image>
                    <text class="margin-left-sm">{{ design.feel_title_em }}</text>
                </view>
            </view>
            <view v-if="version == 0 && open_lost" @tap="user_url" data-index="28" class="cu-item arrow" hover-class="none">
                <view class="content">
                    <image
                        :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/lost.png'"
                        style="margin-right: 5px; vertical-align: sub; width: 20px; height: 20px"
                    ></image>
                    <text class="margin-left-sm">失物招领</text>
                </view>
            </view>
            <view v-if="version == 0 && open_used" @tap="user_url" data-index="31" class="cu-item arrow" hover-class="none">
                <view class="content">
                    <image
                        :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/ershou.png'"
                        style="margin-right: 5px; vertical-align: sub; width: 20px; height: 20px"
                    ></image>
                    <text class="margin-left-sm">{{ design.custom_title }}</text>
                </view>
            </view>
            <view v-if="version == 0 && open_employ" @tap="user_url" data-index="32" class="cu-item arrow" hover-class="none">
                <view class="content">
                    <image
                        :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/pin.png'"
                        style="margin-right: 5px; vertical-align: sub; width: 20px; height: 20px"
                    ></image>
                    <text class="margin-left-sm">{{ design.custom_title_em }}</text>
                </view>
            </view>
            <view v-if="version == 0 && open_convenience" @tap="user_url" data-index="29" class="cu-item arrow" hover-class="none">
                <view class="content">
                    <image
                        :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/bianmin.png'"
                        style="margin-right: 5px; vertical-align: sub; width: 20px; height: 20px"
                    ></image>
                    <text class="margin-left-sm">{{ design.easy_title_em }}</text>
                </view>
            </view>
            <view v-if="version == 0 && open_convenience && user_info.assistant == 1" @tap="user_url" data-index="30" class="cu-item arrow" hover-class="none">
                <view class="content">
                    <image
                        :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/tihuo.png'"
                        style="margin-right: 5px; vertical-align: sub; width: 20px; height: 20px"
                    ></image>
                    <text class="margin-left-sm">核销记录</text>
                </view>
            </view>
            <view v-if="version == 0 && open_sweepstake" @tap="user_url" data-index="33" class="cu-item arrow" hover-class="none">
                <view class="content">
                    <image
                        :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/xingyun.png'"
                        style="margin-right: 5px; vertical-align: sub; width: 20px; height: 20px"
                    ></image>
                    <text class="margin-left-sm">{{ design.sweepstake_title ? design.sweepstake_title : '幸运抽奖' }}</text>
                </view>
            </view>
            <view v-if="version == 0 && copyright.short_drama_arbor == 1" @tap="user_url" data-index="34" class="cu-item arrow" hover-class="none">
                <view class="content">
                    <image
                        :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/duanju.png'"
                        style="margin-right: 5px; vertical-align: sub; width: 20px; height: 20px"
                    ></image>
                    <text class="margin-left-sm">{{ design.micro_title ? design.micro_title : '短剧视频' }}</text>
                </view>
            </view>
            <view class="cu-list menu sm-border" style="margin-top: 10rpx">
                <view v-if="open_account" @tap="user_url" data-index="27" class="cu-item arrow" hover-class="none">
                    <view class="content">
                        <image
                            :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/wechat.png'"
                            style="margin-right: 5px; vertical-align: sub; width: 23px; height: 23px"
                        ></image>
                        <text class="margin-left-sm">公众通知</text>
                    </view>
                </view>
                <view @tap="user_url" data-index="17" class="cu-item arrow" hover-class="none">
                    <view class="content">
                        <image  v-if="http_root"
                            :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/yan.png'"
                            style="margin-right: 5px; vertical-align: sub; width: 23px; height: 23px"
                        ></image>
                        <text class="margin-left-sm">活动验证</text>
                    </view>
                </view>
                <view v-if="admin == 1" @tap="user_url" data-index="13" class="cu-item arrow" hover-class="none">
                    <view class="content">
                        <image
                            :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/shen.png'"
                            style="margin-right: 5px; vertical-align: sub; width: 20px; height: 20px"
                        ></image>
                        <text class="margin-left-sm">内容审核</text>
                    </view>
                </view>
                <view @tap="user_url" data-index="14" class="cu-item arrow" hover-class="none">
                    <view class="content">
                        <image v-if="http_root"
                            :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/gongsi.png'"
                            style="margin-right: 5px; vertical-align: sub; width: 20px; height: 20px"
                        ></image>
                        <text class="margin-left-sm">关于我们</text>
                    </view>
                </view>
            </view>
            <view class="" style="min-height: 110px; margin-top: 20px; margin-bottom: 40px">
                <view class="" style="word-break: break-all; text-align: center; color: var(--blue)">{{ copyright.title }}</view>
                <view class="" style="word-break: break-all; text-align: center; font-size: 12px; margin-top: 10px">
                    {{ copyright.copyright }}
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    props: ['data', 'compName'],
    computed: {
        elect_sheathe() {
            return this.$parent.$data.elect_sheathe;
        },
        design() {
            return this.$parent.$data.design;
        },
        user_info() {
            return this.$parent.$data.user_info;
        },
        animationData() {
            return this.$parent.$data.animationData;
        },
        flag() {
            return this.$parent.$data.flag;
        },
        animationDataD() {
            return this.$parent.$data.animationDataD;
        },
        http_root() {
            return this.$parent.$data.http_root;
        },
        copyright() {
            return this.$parent.$data.copyright;
        },
        version() {
            return this.$parent.$data.version;
        },
       
        open_wangpan() {
            return this.$parent.$data.open_wangpan;
        },
        open_cord() {
            return this.$parent.$data.open_cord;
        },
        open_lost() {
            return this.$parent.$data.open_lost;
        },
        open_used() {
            return this.$parent.$data.open_used;
        },
        open_employ() {
            return this.$parent.$data.open_employ;
        },
        open_convenience() {
            return this.$parent.$data.open_convenience;
        },
        open_sweepstake() {
            return this.$parent.$data.open_sweepstake;
        },
        open_account() {
            return this.$parent.$data.open_account;
        },
        admin() {
            return this.$parent.$data.admin;
        },
        
    },
    methods:{
        my_home(e) {
            this.$emit('my_home', e);
        },
        my_level(e) {
            this.$emit('my_level', e);
        },
        onGotUserInfo(e) {
            this.$emit('onGotUserInfo', e);
        },
        bid_qiandao(e) {
            this.$emit('bid_qiandao', e);
        },
        user_url(e) {
            this.$emit('user_url', e);
        }
    }
};
</script>
<style></style>

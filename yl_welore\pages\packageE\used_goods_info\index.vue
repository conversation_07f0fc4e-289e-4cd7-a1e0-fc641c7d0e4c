<template>
    <view class="page-container">
        <cu-custom bgColor="bg-white" :isBack="true" :isSearch="false">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">📋 详情</view>
        </cu-custom>
        <block v-if="scene != 1154">
            <swiper class="square-dot image-swiper" indicator-active-color="#4facfe" :indicator-dots="true"
                style="height: 700rpx;" :autoplay="true" interval="5000" duration="1000">
                <swiper-item v-if="info.image_part.length > 0" v-for="(item, index) in info.image_part" :key="index">
                    <image @tap="ViewImage" :data-src="item" class="swiper-image" mode="widthFix" :src="item" />
                </swiper-item>
                <swiper-item v-if="info.image_part.length == 0">
                    <image @tap="PreviewViewImage"
                        :data-src="http_root + 'addons/yl_welore/web/static/applet_icon/zanwu.jpg'" class="swiper-image"
                        mode="widthFix" :src="http_root + 'addons/yl_welore/web/static/applet_icon/zanwu.jpg'" />
                </swiper-item>
            </swiper>
            <view class="content-card">
                <view class="item-header">
                    <view class="item-title">{{info.item_name}}</view>
                    <view class="status-tags" v-if="info.item_status == 2">
                        <view v-if="info.release_type == 0" class="status-tag sold">✅ 已出售</view>
                        <view v-if="info.release_type == 1" class="status-tag bought">🛒 已购买</view>
                        <view v-if="info.release_type == 2" class="status-tag completed">✨ 已完成</view>
                        <view v-if="info.release_type == 3" class="status-tag completed">✨ 已完成</view>
                        <view v-if="info.release_type == 4" class="status-tag completed">✨ 已完成</view>
                    </view>
                </view>

                <view class="price-section">
                    <view class="price-container">
                        <text class="price-emoji">💰</text>
                        <text v-if="info.item_price >= 0" class="price-text">{{ info.item_price }}</text>
                        <text v-else class="price-text">{{ info.item_price }}</text>
                    </view>
                    <view class="type-tags">
                        <view v-if="info.release_type == 0" class="type-tag sell">🏷️ 出售</view>
                        <view v-if="info.release_type == 1" class="type-tag buy">🛍️ 求购</view>
                        <view v-if="info.release_type == 2" class="type-tag rent">🏠 租赁</view>
                        <view v-if="info.release_type == 3" class="type-tag exchange">🔄 置换</view>
                        <view v-if="info.release_type == 4" class="type-tag custom">⚙️ 定制</view>
                        <view class="user-tag">👤 {{ info.name }}</view>
                    </view>
                    <view class="item-description">
                        <text>{{ info.item_detail }}</text>
                    </view>
                    <view class="location-info">
                        <text class="info-emoji">📍</text>
                        <text class="info-text">{{ info.secondhand_address }}</text>
                    </view>
                    <view class="time-info">
                        <text class="info-emoji">🕒</text>
                        <text class="info-text">{{ info.create_time }}</text>
                    </view>
                </view>
                <view v-if="info.item_status == 1 && info.user_id == user.id" class="status-button-container">
                    <view @tap="SetStatus" class="status-button"
                        :class="info.release_type == 0 ? 'status-sell' : 'status-other'">
                        <text v-if="info.release_type == 0" class="status-button-text">✅ 已售出</text>
                        <text v-if="info.release_type == 1" class="status-button-text">🛒 已购买</text>
                        <text v-if="info.release_type == 2 || info.release_type == 3 || info.release_type == 4"
                            class="status-button-text">✨ 已完成</text>
                    </view>
                </view>
                <view v-if="config.top_twig == 1 && info.user_id == user.id && info.item_status == 1"
                    class="top-card">
                    <view class="top-info">
                        <text class="top-emoji">📌</text>
                        <text class="top-label">置顶</text>
                        <text class="top-price-info">({{ config.top_price }}
                            <text v-if="config.price_type == 0 || config.price_type == 1">
                                {{ config.price_type == 0 ? config.design.currency : config.design.confer }}
                            </text>/天)</text>
                    </view>
                    <view class="top-picker">
                        <picker :value="index" :range="ToTop" @change="bindTopChange">
                            <view class="picker-content">
                                <text class="picker-text">{{ info.top_time == 0 ? ToTop[TopIndex] : '⭐ 已置顶' }}</text>
                                <text class="picker-arrow">▶️</text>
                            </view>
                        </picker>
                    </view>
                </view>
                <view v-if="config.top_twig == 1 && info.top_time != 0 && info.user_id == user.id && info.item_status == 1"
                    class="top-expire-info">
                    <text>⏰ 置顶到期：{{ info.top_time }}</text>
                </view>
            </view>
            <view class="comment-header">
                <view class="comment-title">
                    <text class="comment-emoji">💬</text>
                    <text class="comment-text">评论</text>
                </view>
            </view>
            <view class="comment-section">
                <view class="comment-list">
                    <!-- 评论列表 -->
                    <view class="comment-item" v-for="(item, r_index) in reply" :key="r_index">
                        <view @tap="openUrl" :data-uid="item.user_info.id" class="comment-avatar"
                            :style="'background-image:url(' + item.user_info.user_head_sculpture + ');'"></view>

                        <view class="comment-content">
                            <view class="comment-user-info">
                                <text class="user-name">{{ item.user_info.user_nick_name }}</text>
                            </view>
                            <view class="comment-privacy">
                                <block v-if="item.is_secrecy == 0">
                                    <text class="privacy-emoji">👁️</text>
                                    <text class="privacy-text">公开评论</text>
                                </block>
                                <block v-if="item.is_secrecy == 1">
                                    <text class="privacy-emoji">🔒</text>
                                    <text class="privacy-text">楼主可见</text>
                                </block>
                                <block v-if="user.uid == item.user_id || user.id == item.user_id">
                                    <text @tap="UpdateIsSecrecy" :data-index="r_index" :data-id="item.id"
                                        :data-key="item.is_secrecy" class="privacy-change">· 更改</text>
                                </block>
                            </view>
                            <view class="comment-text-content">
                                <rich-text :user-select="true" :nodes="item.content"></rich-text>
                                <image v-if="item.image_part.length > 0" :data-src="item.image_part[0]"
                                    @tap.stop.prevent="PreviewViewImage" :src="item.image_part[0]"
                                    class="comment-image"></image>
                            </view>
                            <view class="comment-footer">
                                <view class="comment-actions" @tap="DelReply" :data-id="item.id">
                                    <text class="comment-time">{{ item.reply_time }}</text>
                                    <text class="delete-action"
                                        v-if="user.uid == item.user_id || user.id == item.user_id || user.uid == info.user_id">🗑️ 删除</text>
                                </view>
                            </view>
                        </view>
                    </view>

                    <!-- 空状态 -->
                    <view v-if="reply.length === 0" class="comment-empty-state">
                        <view class="empty-icon">
                            <text class="empty-emoji">💭</text>
                            <text class="empty-emoji-small">✨</text>
                        </view>
                        <view class="empty-title">还没有评论哦</view>
                        <view class="empty-subtitle">成为第一个评论的人吧 🎉</view>
                        <view @tap="openHuiFu" class="empty-action-btn">
                            <text class="empty-btn-text">💬 写评论</text>
                        </view>
                    </view>
                </view>
                <view v-if="reply.length > 0" :class="'load-more ' + (!di_msg ? 'loading' : 'over')"></view>
            </view>
            <view v-if="!huifu" class="bottom-bar">
                <view @tap="openHuiFu" class="comment-input-box">
                    <text class="input-emoji">✏️</text>
                    <text class="input-placeholder">发布评论</text>
                </view>
                <view class="contact-button-container">
                    <button @tap="openBtn" class="contact-button">📞 联系方式</button>
                </view>
            </view>

            <view :class="'comment-modal ' + (huifu ? 'show' : '')" @tap="hideMode">
                <view class="comment-dialog">
                    <view class="comment-input-container">
                        <view v-if="huifu" class="input-section">
                            <textarea :focus="hui_focus" @tap.stop.prevent="close_emoji" cursor-spacing="300rpx"
                                class="comment-textarea"
                                @input="get_text" :value="text" maxlength="300"
                                :placeholder="!is_secrecy ? '💭 留下你精彩的评论吧' : '🔒 仅发帖人可见的评论'" />
                            <view class="image-preview" v-if="img_arr.length > 0">
                                <view class="image-grid">
                                    <view class="image-item" v-for="(item, vido_index) in img_arr" :key="vido_index">
                                        <image :data-src="item" @tap.stop.prevent="PreviewViewImage" :src="item"
                                            mode="aspectFill" class="preview-image"></image>
                                        <view class="delete-image-btn" @tap.stop.prevent="clearOneImage"
                                            :data-index="vido_index">
                                            <text>❌</text>
                                        </view>
                                    </view>
                                </view>
                            </view>
                            <view class="input-tools">
                                <view @tap.stop.prevent="openEmoji" class="tool-btn"
                                    :class="emoji ? 'active' : ''">
                                    <text>😊</text>
                                </view>
                                <view @tap.stop.prevent="previewOneImage" class="tool-btn">
                                    <text>📷</text>
                                </view>
                                <view @tap.stop.prevent="SetSecrecy" class="tool-btn"
                                    :class="is_secrecy ? 'active' : ''">
                                    <text>{{ is_secrecy ? '🔒' : '👁️' }}</text>
                                </view>
                                <view class="submit-btn-container" @tap.stop.prevent="submit">
                                    <button class="submit-btn">🚀 发布</button>
                                </view>
                            </view>
                        </view>
                        <view v-if="emoji" class="emoji-panel">
                            <swiper :indicator-dots="true" class="emoji-swiper">
                                <block v-for="(emojis, e_index) in emj_list" :key="e_index">
                                    <swiper-item>
                                        <view class="emoji-grid">
                                            <view @tap.stop.prevent="set_emoji" :data-key="e_index"
                                                :data-index="n_index" class="emoji-item"
                                                v-for="(n_item, n_index) in emojis" :key="n_index">
                                                <image
                                                    :src="http_root + 'addons/yl_welore/web/static/expression/' + n_item"
                                                    class="emoji-image"></image>
                                            </view>
                                        </view>
                                    </swiper-item>
                                </block>
                            </swiper>
                        </view>
                    </view>
                </view>
            </view>
            <view class="upload-modal" v-if="loadModal">
                <view class="upload-text">📤 上传中...</view>
            </view>
        </block>
    </view>
</template>

<script>
const app = getApp();
var http = require('../../../util/http.js');
var md5 = require('../../../util/md5.js');
import regeneratorRuntime from '../../../util/runtime';

export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            http_root: app.globalData.http_root,
            user: {},
            reply: [],
            id: 0,
            page: 1,
            is_secrecy: false,
            img_arr: [],
            huifu: false,
            emj_list: [],
            emoji: false,
            text: '',
            config: {},
            TopIndex: 0,
            ToTop: ['未设置', '1天', '2天', '3天', '4天', '5天', '6天', '7天', '8天', '9天', '10天'],
            scene: 0,
            info: {},
            di_msg: false,
            hui_focus: false,
            img_botton: true,
            loadModal: false
        }
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        var op = uni.getLaunchOptionsSync();
        this.scene = op.scene;
        if (op.scene == 1154) {
            uni.showToast({
                title: '前往小程序查看！',
                icon: 'none',
                duration: 666000
            });
            return;
        }
        var lost = app.globalData.__PlugUnitScreen('a11eb9c1955977a6d890dca4991209f6');
        if (!lost) {
            uni.showToast({
                title: '未开通插件',
                icon: 'none',
                duration: 2000
            });
            setTimeout(() => {
                var pages = getCurrentPages();
                if (pages.length == 1) {
                    uni.reLaunch({
                        url: '/yl_welore/pages/index/index'
                    });
                    return;
                }
                uni.navigateBack();
            }, 1000);
            return;
        }
        this.id = options.id;
        this.doIt();
        // var e = app.getCache("userinfo");
    },
    onReachBottom() {
        this.page = this.page + 1;
        this.InfoReply();
    },
    /**
     * 用户点击右上角分享
     */
    onShareTimeline() {
        var info = this.info;
        var msg = '';
        switch (info.release_type) {
            case 0:
                msg = '[出售]';
                break;
            case 1:
                msg = '[求购]';
                break;
            case 2:
                msg = '[租赁]';
                break;
            case 3:
                msg = '[置换]';
                break;
            case 4:
                msg = '[定制]';
                break;
        }
        var img = info.image_part[0];
        return {
            title: msg + info.item_name,
            path: '/yl_welore/pages/packageE/used_goods_info/index?id=' + this.id,
            imageUrl: img
        };
    },
    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {
        var info = this.info;
        var msg = '';
        switch (info.release_type) {
            case 0:
                msg = '[出售]';
                break;
            case 1:
                msg = '[求购]';
                break;
            case 2:
                msg = '[租赁]';
                break;
            case 3:
                msg = '[置换]';
                break;
            case 4:
                msg = '[定制]';
                break;
        }
        var img = info.image_part[0];
        return {
            title: msg + info.item_name,
            path: '/yl_welore/pages/packageE/used_goods_info/index?id=' + this.id,
            imageUrl: img
        };
    },
    methods: {
        UpdateIsSecrecy(d) {
            var id = d.currentTarget.dataset.id;
            var key = d.currentTarget.dataset.key;
            var index = d.currentTarget.dataset.index;
            var msg = key == 1 ? '确定更改为公开评论吗？' : '确定更改为仅楼主可见吗？';
            uni.showModal({
                title: '提示',
                content: msg,
                success: (res) => {
                    if (res.confirm) {
                        this.UpdateIsSecrecyDo(id, key, index);
                    }
                }
            });
        },
        UpdateIsSecrecyDo(id, key, index) {
            var b = app.globalData.api_root + 'Used/UpdateIsSecrecyDo';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.key = key;
            params.id = id;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.code == 1) {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: (res) => { }
                        });
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                        this.reply[index].is_secrecy = key == 0 ? 1 : 0;
                    }

                    //this.InfoReply();
                },

                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        },
        bindTopChange(d) {
            this.TopIndex = d.detail.value;
            console.log('确定');
            if (d.detail.value == 0) {
                return;
            }
            if (this.config.price_type == 0 || this.config.price_type == 1) {
                uni.showModal({
                    title: '提示',
                    content: '确定要置顶' + d.detail.value + '天吗？',
                    success: (res) => {
                        if (res.confirm) {
                            this.pay_submit(this.id, d.detail.value);
                        }
                    }
                });
            }
            if (this.config.price_type == 2) {
                this.pay_submit_wx(this.id, d.detail.value);
            }
        },
        pay_submit(id, top_day) {
            var b = app.globalData.api_root + 'Used/PaySubmit';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.top_day = top_day;
            params.id = id;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.code == 1) {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: (res) => { }
                        });
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                    this.getInfo();
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        },
        /**
         * 充值
         */
        pay_submit_wx(id, top_day) {
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.lost_id = id;
            params.top_day = top_day;
            params.uid = e.id;
            var b = app.globalData.api_root + 'Pay/do_used_pay';
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.return_msg == 'OK') {
                        var timeStamp = (Date.parse(new Date()) / 1000).toString();
                        var pkg = 'prepay_id=' + res.data.prepay_id;
                        var nonceStr = res.data.nonce_str;
                        var paySign = md5
                            .hexMD5(
                                'appId=' +
                                res.data.appid +
                                '&nonceStr=' +
                                nonceStr +
                                '&package=' +
                                pkg +
                                '&signType=MD5&timeStamp=' +
                                timeStamp +
                                '&key=' +
                                res.data.app_info['app_key']
                            )
                            .toUpperCase(); //此处用到hexMD5插件
                        //发起支付
                        uni.requestPayment({
                            timeStamp: timeStamp,
                            nonceStr: nonceStr,
                            package: pkg,
                            signType: 'MD5',
                            paySign: paySign,
                            success: (res) => {
                                uni.showModal({
                                    title: '提示',
                                    content: '支付成功！',
                                    showCancel: false,
                                    success: (res) => { }
                                });
                                this.getInfo();
                            },
                            complete: () => {
                                uni.hideLoading();
                            }
                        });
                    } else {
                        uni.showModal({
                            title: '提示',
                            content: '支付参数错误！',
                            showCancel: false,
                            success: (res) => { }
                        });
                        uni.hideLoading();
                    }
                    this.TopIndex = 0;
                },
                fail: () => {
                    uni.hideLoading();
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        },
        getLostConfig() {
                var b = app.globalData.api_root + 'Used/getLostConfig';
                var e = app.globalData.getCache('userinfo');
                var params = new Object();
                params.token = e.token;
                params.openid = e.openid;
                http.POST(b, {
                    params: params,
                    success: (res) => {
                        console.log(res);
                        this.config = res.data;
                    },
                    fail: () => {
                        uni.showModal({
                            title: '提示',
                            content: '网络繁忙，请稍候重试！',
                            showCancel: false,
                            success: (res) => { }
                        });
                    }
                });
        },
        SetStatus() {
            var info = this.info;
            var msg = '';
            switch (info.release_type) {
                case 0:
                    msg = '已卖出';
                    break;
                case 1:
                    msg = '已购买';
                    break;
                default:
                    msg = '已完结';
                    break;
            }
            uni.showModal({
                title: '提示',
                content: '确定' + msg + '吗？',
                success: (res) => {
                    if (res.confirm) {
                        this.SetStatusDo();
                    }
                }
            });
        },
        SetStatusDo() {
            var b = app.globalData.api_root + 'Used/SetStatusDo';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.id;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.code == 0) {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    } else {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: (res) => { }
                        });
                    }
                    this.getInfo();
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        },
        DelReply(d) {
            var id = d.currentTarget.dataset.id;
            uni.showModal({
                title: '提示',
                content: '确定要删除这个评论吗？',
                success: (res) => {
                    if (res.confirm) {
                        this.DelReplyDo(id);
                    }
                }
            });
        },
        DelReplyDo(id) {
            var b = app.globalData.api_root + 'Used/DelReplyDo';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.id;
            params.reply_id = id;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.code == 0) {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    } else {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: (res) => { }
                        });
                    }
                    this.page = 1;
                    this.reply = [];
                    this.InfoReply();
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        },
        submit() {
            var b = app.globalData.api_root + 'Used/LostDoSubmit';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.id;
            params.content = this.text;
            params.img_arr = JSON.stringify(this.img_arr);
            params.is_secrecy = this.is_secrecy ? 1 : 0;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.code == 0) {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    } else {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: (res) => { }
                        });
                    }
                    this.page = 1;
                    this.reply = [];
                    this.emoji = false;
                    this.huifu = false;
                    this.text = '';
                    this.InfoReply();
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        },
        get_text(e) {
            var length = e.detail.value;
            this.text = length;
        },
        set_emoji(d) {
            var index = d.currentTarget.dataset.index;
            var t_index = d.currentTarget.dataset.key;
            var str = this.emj_list[t_index][index];
            var k = str.split('.')[0];
            console.log(k);
            this.text = this.text + '[#:' + k + ']';
        },
        close_emoji() {
            this.emoji = false;
            this.hui_focus = true;
        },
        SetSecrecy() {
            this.is_secrecy = !this.is_secrecy;
        },
        openHuiFu() {
            this.huifu = true;
        },
        hideMode() {
            this.huifu = false;
        },
        openUrl(d) {
            var uid = d.currentTarget.dataset.uid;
            uni.navigateTo({
                url: '/yl_welore/pages/packageB/my_home/index?id=' + uid
            });
        },
        openBtn() {
            uni.showModal({
                title: '联系方式',
                confirmText: '复制',
                content: this.info.contact_details,
                success: (res) => {
                    if (res.confirm) {
                        uni.setClipboardData({
                            data: this.info.contact_details,
                            success: (res) => { }
                        });
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                }
            });
        },
        doIt() {
            app.globalData.getLogin(
                // 成功回调 returnA
                (userInfo) => {
                    console.log(' 登录成功:', userInfo);
                    this.user = userInfo;
                    this.getLostConfig();
                    this.getInfo();
                    this.InfoReply();
                    this.get_emj_list();
                },
                // 失败回调 returnB
                (err) => {
                    console.error(' 登录失败:', err);
                }
            );
        },
        claerStor() {
            return new Promise((resolve, reject) => {
                var v = app.globalData.getCache('yl_version');
                console.log(v);
                if (v == '') {
                    var v2 = -1;
                } else {
                    var v2 = app.globalData.compareVersion(v, app.globalData.version); //相等=0  小于-1  大于1
                }

                app.globalData.setCache('yl_version', app.globalData.version);
                console.log(v2);
                resolve(v2);
                return v2; //执行登陆
            });
        },
            InfoReply() {
                    var b = app.globalData.api_root + 'Used/LostInfoReply';
                    var e = app.globalData.getCache('userinfo');
                    var params = new Object();
                    params.token = e.token;
                    params.openid = e.openid;
                    params.id = this.id;
                    params.page = this.page;
                    http.POST(b, {
                        params: params,
                        success: (res) => {
                            console.log(res);
                            if (res.data.length == 0 || res.data.length < 5) {
                                this.di_msg = true;
                            }
                            var reply = this.reply;
                            reply.push(...res.data);
                            this.reply = reply;
                        },
                        fail: () => {
                            uni.showModal({
                                title: '提示',
                                content: '网络繁忙，请稍候重试！',
                                showCancel: false,
                                success: (res) => { }
                            });
                        }
                    });
            },
            getInfo() {
                    var b = app.globalData.api_root + 'Used/LostInfo';
                    var e = app.globalData.getCache('userinfo');
                    var params = new Object();
                    params.token = e.token;
                    params.openid = e.openid;
                    params.id = this.id;
                    http.POST(b, {
                        params: params,
                        success: (res) => {
                            if (res.data.code == 0) {
                                this.info = res.data.info;
                            } else {
                                uni.showModal({
                                    title: '提示',
                                    content: res.data.msg,
                                    showCancel: false,
                                    success: (res) => {
                                        uni.navigateBack({
                                            delta: 1
                                        });
                                    }
                                });
                            }
                        },
                        fail: () => {
                            uni.showModal({
                                title: '提示',
                                content: '网络繁忙，请稍候重试！',
                                showCancel: false,
                                success: (res) => { }
                            });
                        }
                    });
            },
            /**
             * 一键复制
             */
            copyBtn(e) {
                uni.setClipboardData({
                    data: e.currentTarget.dataset.no,
                    success: (res) => { }
                });
            },
            ViewImage(e) {
                console.log(e);
                if (this.info['image_part'].length > 0) {
                    uni.previewImage({
                        urls: this.info['image_part'],
                        current: e.currentTarget.dataset.src
                    });
                } else {
                    uni.previewImage({
                        urls: [e.currentTarget.dataset.src],
                        current: e.currentTarget.dataset.src
                    });
                }
            },
            PreviewViewImage(e) {
                uni.previewImage({
                    urls: [e.currentTarget.dataset.src],
                    current: e.currentTarget.dataset.src
                });
            },
            /**
             * 删除图片
             */
            clearOneImage(e) {
                var index = e.target.dataset['index'];
                var notes = this.img_arr;
                notes.splice(index, 1);
                this.img_arr = notes;
                this.img_botton = true;
            },
            openEmoji() {
                this.emoji = !this.emoji;
                this.hui_focus = false;
            },
            get_emj_list() {
                    var e = app.globalData.getCache('userinfo');
                    var params = new Object();
                    params.token = e.token;
                    params.openid = e.openid;
                    var b = app.globalData.api_root + 'Polls/get_emj_list';
                    http.POST(b, {
                        params: params,
                        success: (res) => {
                            this.emj_list = res.data;
                        },
                        fail: () => {
                            uni.showModal({
                                title: '提示',
                                content: '网络繁忙，请稍候重试！',
                                showCancel: false,
                                success: (res) => { }
                            });
                        }
                    });
            },
            /**
             * 回复上传主图
             */
            previewOneImage() {
                var e = app.globalData.getCache('userinfo');
                var b = app.globalData.api_root + 'User/img_upload';
                uni.chooseImage({
                    count: 1,
                    sizeType: ['original', 'compressed'],
                    // 可以指定是原图还是压缩图，默认二者都有
                    sourceType: ['album', 'camera'],
                    // 可以指定来源是相册还是相机，默认二者都有
                    success: (res) => {
                        console.log(res);
                        this.loadModal = true;
                        var tempFilePaths = res.tempFilePaths;
                        uni.uploadFile({
                            url: b,
                            filePath: tempFilePaths[0],
                            name: 'sngpic',
                            header: {
                                'content-type': 'multipart/form-data'
                            },
                            formData: {
                                'content-type': 'multipart/form-data',
                                token: e.token,
                                openid: e.openid,
                                much_id: app.globalData.siteInfo.uniacid
                            },
                            success: (res) => {
                                console.log(res);
                                var data = JSON.parse(res.data);
                                console.log(data);
                                if (data.status == 'error') {
                                    this.loadModal = false;
                                    uni.showModal({
                                        title: '提示',
                                        content: data.msg
                                    });
                                    return;
                                } else {
                                    this.img_arr = this.img_arr.concat(data.url);
                                    this.img_botton = false;
                                }
                                this.loadModal = false;
                            },
                            fail: (res) => {
                                uni.showModal({
                                    title: '提示',
                                    content: '上传错误！'
                                });
                            }
                        });
                    }
                });
            }
        }
    }
</script>
<style>
page {
    background: linear-gradient(135deg, #e3f2fd 0%, #f0f8ff 50%, #e8f5e8 100%);
    min-height: 100vh;
}

.page-container {
    background: linear-gradient(135deg, #e3f2fd 0%, #f0f8ff 50%, #e8f5e8 100%);
    min-height: 100vh;
    padding-bottom:20rpx;
}

/* 轮播图样式 */
.image-swiper {
    border-radius: 20rpx;
    overflow: hidden;
    margin: 20rpx;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.swiper-image {
    width: 100%;
    border-radius: 20rpx;
}

/* 内容卡片 */
.content-card {
    background: rgba(255, 255, 255, 0.95);
    margin: 20rpx;
    border-radius: 24rpx;
    padding: 40rpx;
    box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

/* 商品标题区域 */
.item-header {
    margin-bottom: 40rpx;
}

.item-title {
    font-size: 48rpx;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 20rpx;
    line-height: 1.4;
}

.status-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;
}

.status-tag {
    padding: 12rpx 24rpx;
    border-radius: 50rpx;
    font-size: 24rpx;
    font-weight: 600;
    color: white;
    text-align: center;
}

.status-tag.sold {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
}

.status-tag.bought {
    background: linear-gradient(45deg, #4ecdc4, #44bd87);
}

.status-tag.completed {
    background: linear-gradient(45deg, #a55eea, #8b5cf6);
}

/* 价格区域 */
.price-section {
    margin: 40rpx 0;
}

.price-container {
    display: flex;
    align-items: center;
    margin-bottom: 30rpx;
}

.price-emoji {
    font-size: 40rpx;
    margin-right: 16rpx;
}

.price-text {
    font-size: 56rpx;
    font-weight: 700;
    background: linear-gradient(45deg, #ff6b6b, #ffa726);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 类型标签 */
.type-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;
    margin-bottom: 30rpx;
}

.type-tag, .user-tag {
    padding: 16rpx 32rpx;
    border-radius: 50rpx;
    font-size: 28rpx;
    font-weight: 600;
    color: white;
    text-align: center;
}

.type-tag.sell {
    background: linear-gradient(45deg, #ff9a9e, #fecfef);
    color: #2c3e50;
}

.type-tag.buy {
    background: linear-gradient(45deg, #a8edea, #fed6e3);
    color: #2c3e50;
}

.type-tag.rent {
    background: linear-gradient(45deg, #ffecd2, #fcb69f);
    color: #2c3e50;
}

.type-tag.exchange {
    background: linear-gradient(45deg, #d299c2, #fef9d7);
    color: #2c3e50;
}

.type-tag.custom {
    background: linear-gradient(45deg, #89f7fe, #66a6ff);
    color: #2c3e50;
}

.user-tag {
    background: linear-gradient(45deg, #667eea, #764ba2);
}

/* 商品描述 */
.item-description {
    margin: 30rpx 0;
    padding: 30rpx;
    background: rgba(116, 185, 255, 0.1);
    border-radius: 20rpx;
    border-left: 8rpx solid #74b9ff;
}

.item-description text {
    font-size: 32rpx;
    line-height: 1.6;
    color: #2c3e50;
}

/* 信息项 */
.location-info, .time-info {
    display: flex;
    align-items: center;
    margin: 20rpx 0;
    padding: 20rpx;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 16rpx;
}

.info-emoji {
    font-size: 32rpx;
    margin-right: 16rpx;
}

.info-text {
    font-size: 28rpx;
    color: #636e72;
}

/* 状态按钮 */
.status-button-container {
    text-align: center;
    margin: 40rpx 0;
}

.status-button {
    width: 320rpx;
    height: 120rpx;
    border-radius: 24rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.status-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.status-button:active::before {
    left: 100%;
}

.status-button.status-sell {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24, #ff7675);
    border: 2rpx solid rgba(255, 107, 107, 0.4);
    box-shadow: 0 8rpx 24rpx rgba(255, 107, 107, 0.3);
}

.status-button.status-other {
    background: linear-gradient(135deg, #0984e3, #74b9ff, #a29bfe);
    border: 2rpx solid rgba(9, 132, 227, 0.3);
}

.status-button-text {
    color: white;
    font-size: 32rpx;
    font-weight: 700;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
    letter-spacing: 2rpx;
}

/* 置顶卡片 */
.top-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20rpx;
    padding: 30rpx;
    margin: 30rpx 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.top-info {
    display: flex;
    align-items: center;
    color: white;
}

.top-emoji {
    font-size: 32rpx;
    margin-right: 12rpx;
}

.top-label {
    font-size: 28rpx;
    font-weight: 600;
    margin-right: 8rpx;
}

.top-price-info {
    font-size: 24rpx;
    opacity: 0.9;
}

.picker-content {
    display: flex;
    align-items: center;
    color: white;
}

.picker-text {
    font-size: 28rpx;
    font-weight: 500;
    margin-right: 8rpx;
}

.picker-arrow {
    font-size: 20rpx;
}

.top-expire-info {
    text-align: center;
    font-size: 24rpx;
    color: #636e72;
    margin-top: 20rpx;
    padding: 16rpx;
    background: rgba(255, 193, 7, 0.1);
    border-radius: 12rpx;
}

/* 评论区样式 */
.comment-header {
    background: rgba(255, 255, 255, 0.95);
    margin: 20rpx 20rpx 0rpx 20rpx;
    border-radius: 20rpx 20rpx 0 0;
    padding: 30rpx 40rpx;
    backdrop-filter: blur(10px);
}

.comment-title {
    display: flex;
    align-items: center;
}

.comment-emoji {
    font-size: 36rpx;
    margin-right: 16rpx;
}

.comment-text {
    font-size: 36rpx;
    font-weight: 700;
    color: #2c3e50;
}

.comment-section {
    background: rgba(255, 255, 255, 0.95);
    margin: 0 20rpx 200rpx 20rpx;
    border-radius: 0 0 20rpx 20rpx;
    backdrop-filter: blur(10px);
    padding-bottom: 40rpx;
}

.comment-list {
    padding: 0 40rpx;
}

.comment-item {
    display: flex;
    padding: 30rpx 0;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.comment-avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    margin-right: 24rpx;
    background-size: cover;
    background-position: center;
    border: 4rpx solid #74b9ff;
}

.comment-content {
    flex: 1;
}

.comment-user-info {
    margin-bottom: 12rpx;
}

.user-name {
    font-size: 28rpx;
    font-weight: 600;
    color: #2c3e50;
}

.comment-privacy {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;
}

.privacy-emoji {
    font-size: 24rpx;
    margin-right: 8rpx;
}

.privacy-text {
    font-size: 24rpx;
    color: #636e72;
}

.privacy-change {
    font-size: 24rpx;
    color: #74b9ff;
    margin-left: 8rpx;
}

.comment-text-content {
    margin: 20rpx 0;
    line-height: 1.6;
}

.comment-image {
    width: 140rpx;
    height: 140rpx;
    border-radius: 12rpx;
    margin-top: 16rpx;
}

.comment-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.comment-actions {
    display: flex;
    align-items: center;
    gap: 16rpx;
}

.comment-time {
    font-size: 24rpx;
    color: #b2bec3;
}

.delete-action {
    font-size: 24rpx;
    color: #ff7675;
}

.load-more {
    text-align: center;
    padding: 40rpx;
    color: #b2bec3;
    font-size: 28rpx;
}

/* 评论空状态 */
.comment-empty-state {
    text-align: center;
    padding: 80rpx 40rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.empty-icon {
    position: relative;
    margin-bottom: 40rpx;
}

.empty-emoji {
    font-size: 120rpx;
    opacity: 0.8;
    display: block;
}

.empty-emoji-small {
    font-size: 40rpx;
    position: absolute;
    top: -20rpx;
    right: -20rpx;
    animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
    0%, 100% {
        transform: scale(1) rotate(0deg);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.2) rotate(180deg);
        opacity: 1;
    }
}

.empty-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 16rpx;
}

.empty-subtitle {
    font-size: 28rpx;
    color: #636e72;
    margin-bottom: 40rpx;
    line-height: 1.4;
}

.empty-action-btn {
    background: linear-gradient(45deg, #74b9ff, #0984e3);
    color: white;
    border-radius: 50rpx;
    padding: 24rpx 48rpx;
    box-shadow: 0 8rpx 24rpx rgba(116, 185, 255, 0.3);
    transition: all 0.3s ease;
}

.empty-action-btn:active {
    transform: translateY(2rpx);
    box-shadow: 0 4rpx 12rpx rgba(116, 185, 255, 0.4);
}

.empty-btn-text {
    font-size: 28rpx;
    font-weight: 600;
}

/* 底部操作栏 */
.bottom-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 20rpx 40rpx 40rpx;
    display: flex;
    align-items: center;
    gap: 20rpx;
    box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.comment-input-box {
    flex: 1;
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    border-radius: 50rpx;
    padding: 24rpx 32rpx;
    display: flex;
    align-items: center;
    box-shadow: inset 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.input-emoji {
    font-size: 28rpx;
    margin-right: 16rpx;
}

.input-placeholder {
    font-size: 28rpx;
    color: #636e72;
}

.contact-button-container {
    flex-shrink: 0;
}

.contact-button {
    background: linear-gradient(45deg, #00b894, #00cec9);
    color: white;
    border: none;
    border-radius: 50rpx;
    padding: 10rpx 32rpx;
    font-size: 28rpx;
    font-weight: 600;
    box-shadow: 0 4rpx 16rpx rgba(0, 184, 148, 0.3);
}

/* 评论输入弹窗 */
.comment-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: flex-end;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.comment-modal.show {
    opacity: 1;
    visibility: visible;
}

.comment-dialog {
    width: 100%;
    background: white;
    border-radius: 24rpx 24rpx 0 0;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.comment-modal.show .comment-dialog {
    transform: translateY(0);
}

.comment-input-container {
    padding: 40rpx;
}

.input-section {
    margin-bottom: 20rpx;
}

.comment-textarea {
    width: 100%;
    min-height: 200rpx;
    padding: 30rpx;
    border: 2rpx solid #e9ecef;
    border-radius: 20rpx;
    font-size: 32rpx;
    line-height: 1.5;
    background: #f8f9fa;
    resize: none;
}

.image-preview {
    margin: 30rpx 0;
}

.image-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
}

.image-item {
    position: relative;
    width: 160rpx;
    height: 160rpx;
}

.preview-image {
    width: 100%;
    height: 100%;
    border-radius: 12rpx;
    object-fit: cover;
}

.delete-image-btn {
    position: absolute;
    top: -10rpx;
    right: -10rpx;
    width: 40rpx;
    height: 40rpx;
    background: #ff7675;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20rpx;
    z-index: 100;
}

.input-tools {
    display: flex;
    align-items: center;
    gap: 30rpx;
    margin-top: 30rpx;
}

.tool-btn {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 36rpx;
    transition: all 0.3s ease;
}

.tool-btn.active {
    background: linear-gradient(45deg, #74b9ff, #0984e3);
    color: white;
}

.submit-btn-container {
    margin-left: auto;
}

.submit-btn {
    background: linear-gradient(45deg, #00b894, #00cec9);
    color: white;
    border: none;
    border-radius: 50rpx;
    padding: 20rpx 40rpx;
    font-size: 28rpx;
    font-weight: 600;
    box-shadow: 0 4rpx 16rpx rgba(0, 184, 148, 0.3);
}

/* 表情面板 */
.emoji-panel {
    background: #f8f9fa;
    border-radius: 0 0 24rpx 24rpx;
}

.emoji-swiper {
    height: 400rpx;
}

.emoji-grid {
    display: grid;
    grid-template-columns: repeat(9, 1fr);
    gap: 20rpx;
    padding: 30rpx;
}

.emoji-item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10rpx;
    border-radius: 12rpx;
    transition: background 0.2s ease;
}

.emoji-item:active {
    background: rgba(116, 185, 255, 0.2);
}

.emoji-image {
    width: 60rpx;
    height: 60rpx;
}

/* 上传提示 */
.upload-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.upload-text {
    color: white;
    font-size: 32rpx;
    font-weight: 600;
    text-align: center;
    padding: 40rpx;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 20rpx;
}
</style>

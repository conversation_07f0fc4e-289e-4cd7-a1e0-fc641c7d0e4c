<template>
    <view>
        <view v-if="info_home.length > 0" style="width: 100%">
            <swiper
                class="round-dot"
                :indicator-dots="true"
                :style="'height:' + (info_home[0].length > 4 ? 400 : 220) + 'rpx;'"
                indicator-active-color="#696969"
                indicator-color="rgba(150, 150, 150, 0.8)"
            >
                <block v-for="(one, e_index) in info_home" :key="e_index">
                    <swiper-item>
                        <view class="grid col-4 text-center" style="padding-top: 10px; width: 100%">
                            <view
                                @tap="set_one"
                                :data-type="n_item.practice_type"
                                :data-path="n_item.wx_app_url"
                                :data-url="n_item.url"
                                :data-key="e_index"
                                :data-index="n_index"
                                style="margin: 5px 0px"
                                v-for="(n_item, n_index) in one"
                                :key="n_index"
                            >
                                <image :lazy-load="true" :src="n_item.playbill_url" style="width: 90rpx; height: 90rpx"></image>

                                <view v-if="n_item.playbill_name" class="botton_text">
                                    {{ n_item.playbill_name }}
                                </view>
                            </view>
                        </view>
                    </swiper-item>
                </block>
            </swiper>
        </view>
        <block v-if="put_top_list.length > 0 && put_top_info.display_switch == 1">
            <view v-if="put_top_info.style_type == 0" @tap="top_url" class="text-center" style="position: relative">
                <view class="text-bold" style="color: #ffffff; position: absolute; z-index: 10; font-size: 36rpx; width: 94rpx; left: 56rpx; top: 24rpx; letter-spacing: 6rpx">
                    {{ put_top_info.custom_title }}
                </view>
                <view style="width: 75%; color: #000000; padding-right: 30rpx; position: absolute; z-index: 10; left: 170rpx; top: 30rpx">
                    <swiper easing-function="linear" autoplay interval="5000" circular vertical style="height: 90rpx">
                        <swiper-item v-for="(item, index) in put_top_list" :key="index">
                            <view class="text_num_1 text-bold text-left" style="width: 100%">
                                <image
                                    style="width: 25rpx; height: 25rpx; vertical-align: middle"
                                    :src="http_root + 'addons/yl_welore/web/static/examine/' + (index + 1) + '.png'"
                                ></image>
                                <text style="letter-spacing: 2rpx; font-size: 30rpx; margin-left: 10rpx; vertical-align: middle">
                                    {{ item.study_title == '' ? item.study_content : item.study_title }}
                                </text>
                            </view>

                            <view class="text-right" style="letter-spacing: 3rpx; font-size: 24rpx; margin-top: 20rpx">围观数{{ item.hort }}</view>
                        </swiper-item>
                    </swiper>
                </view>
                <image class="now_level" style="width: 95%; border-radius: 20rpx" mode="widthFix" src="/static/yl_welore/style/icon/sign_top_bac.png"></image>
            </view>
            <view v-if="put_top_info.style_type == 1" @tap="top_url" class="text-center" style="position: relative">
                <view style="width: 75%; color: #000000; padding-right: 30rpx; position: absolute; z-index: 10; left: 170rpx; top: 0rpx">
                    <swiper easing-function="linear" autoplay interval="5000" circular vertical style="height: 120rpx; padding-top: 15rpx">
                        <swiper-item v-for="(item, index) in put_top_list" :key="index">
                            <view class="text_num_1 text-bold text-left" style="width: 100%">
                                <image
                                    style="width: 25rpx; height: 25rpx; vertical-align: middle"
                                    :src="http_root + 'addons/yl_welore/web/static/examine/' + (index + 1) + '.png'"
                                ></image>
                                <text style="letter-spacing: 2rpx; font-size: 30rpx; margin-left: 10rpx; vertical-align: middle">
                                    {{ item.study_title == '' ? item.study_content : item.study_title }}
                                </text>
                            </view>

                            <view class="text-right" style="letter-spacing: 3rpx; font-size: 24rpx; margin-top: 25rpx">围观数{{ item.hort }}</view>
                        </swiper-item>
                    </swiper>
                </view>
                <image class="now_level" style="width: 95%; border-radius: 20rpx" mode="widthFix" src="/static/yl_welore/style/icon/sign_top_bac1.png"></image>
            </view>
        </block>

        <block v-if="copyright.home_my_tory_arbor == 1">
            <view class="flex justify-between align-center" :style="'margin-top: ' + (info_home.length == 0 ? 20 : 0) + 'px;'">
                <view style="padding-left: 15px; font-size: 15px; font-weight: 600">我的{{ $state.diy.landgrave }}</view>
                <view class="text-gray" style="padding-right: 10px" @tap="get_all_qq">
                    <text>全部</text>
                    <text class="cicon-angle"></text>
                </view>
            </view>
            <scroll-view :scroll-x="true" @scrolltolower="nex_my_qq" style="white-space: nowrap; width: 100%; padding: 0px 5px; margin-bottom: 10px">
                <view
                    :data-id="item.id"
                    @tap="this_url"
                    style="
                        height: 100%;
                        margin: 25rpx;
                        text-align: center;
                        background-color: #fff;
                        border-radius: 25rpx;
                        box-sizing: border-box;
                        display: inline-block;
                        position: relative;
                    "
                    v-for="(item, index) in my_qq_list"
                    :key="index"
                >
                    <view class="cu-avatar" :style="'width: 105rpx;height: 105rpx;border-radius: 10px;background-color:#fff;background-image:url(' + item.realm_icon + ');'"></view>

                    <view style="font-size: 12px; margin-top: 10px">
                        <text class="text-cut" style="vertical-align: text-bottom">{{ item.realm_name }}</text>
                    </view>
                </view>
                <view v-if="my_qq_list.length == 0" @tap="get_all_qq" style="padding: 30rpx; text-align: center; width: 100%">
                    <view style="font-size: 14px">
                        <text class="cicon-discover"></text>
                        <text class="text-cut" style="vertical-align: text-bottom; margin-left: 5px">查看更多{{ $state.diy.landgrave }}</text>
                    </view>
                </view>
            </scroll-view>
        </block>
    </view>
</template>

<script>
export default {
    props: ['data', 'compName'],
    computed: {
        info_home() {
            return (this.data && this.data.info_home) || [];
        },
        put_top_list() {
            return (this.data && this.data.put_top_list) || [];
        },
        put_top_info() {
            return (this.data && this.data.put_top_info) || {};
        },
        http_root() {
            return this.data && this.data.http_root;
        },
        copyright() {
            return (this.data && this.data.copyright) || {};
        },
       
        my_qq_list() {
            return (this.data && this.data.my_qq_list) || [];
        },
        
    },
    methods: {
        bindchange_top(e) {
            this.$emit('bindchange_top', e);
        },
        set_one(e) {
           this.$emit('set_one', e);
        },
        top_url(e) {
            this.$emit('top_home_url', e);
        },
        get_all_qq(e) {
            this.$emit('get_all_qq', e);
        },
        nex_my_qq(e) {
            this.$emit('nex_my_qq', e);
        },
        this_url(e) {
            this.$emit('this_url',e);
        }
    }
};
</script>
<style></style>

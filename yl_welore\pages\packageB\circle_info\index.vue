<template>
    <view>
        <cu-custom bgColor="none" :isBack="true" :isSearch="false">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">{{ design.landgrave }}资料</view>
        </cu-custom>
        <view style="padding: 30rpx; position: relative; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 0 0 40rpx 40rpx; margin-bottom: 20rpx;">
            <view style="text-align: center; position: relative; padding: 20rpx 0;">
                <view style="position: relative; display: inline-block;">
                    <image :src="info.realm_icon" style="width: 120px; height: 120px; border-radius: 50%; border: 4px solid rgba(255,255,255,0.3); box-shadow: 0 8px 32px rgba(0,0,0,0.1);"></image>
                    <view style="z-index: 50;position: absolute; bottom: 0; right: 0; background: #fff; border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
                        <text @tap="getAvaterInfo" class="cicon-qr-code-line" style="font-size: 20px; color: #667eea;"></text>
                    </view>
                </view>
            </view>
            <view style="font-weight: 600; margin: 20rpx 10rpx 10rpx; font-size: 22px; color: #fff; text-align: center; text-shadow: 0 2px 4px rgba(0,0,0,0.3);">🏰 {{ info.realm_name }}</view>
            <view style="font-size: 14px; color: rgba(255,255,255,0.9); text-align: center; margin-bottom: 20rpx; line-height: 1.5;">{{ info.realm_synopsis }}</view>
            <view style="display: flex; flex-wrap: wrap; justify-content: center; padding: 20rpx; gap: 15rpx; background: rgba(255,255,255,0.1); border-radius: 20rpx; margin: 20rpx 0;">
                <block v-if="info.attention == 1">
                    <view @tap="open_soms" data-key="1" v-if="info.this_da_qq == 1 || info.this_xiao_qq == 1" style="background: linear-gradient(45deg, #4CAF50, #45a049); color: white; padding: 12rpx 24rpx; border-radius: 25rpx; font-size: 14px; box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3); display: flex; align-items: center; gap: 8rpx;">
                        <text>📝</text>申请列表
                    </view>
                </block>
                <view @tap="open_soms" data-key="2" v-if="info.this_da_qq == 1 || info.this_xiao_qq == 1" style="background: linear-gradient(45deg, #FF9800, #F57C00); color: white; padding: 12rpx 24rpx; border-radius: 25rpx; font-size: 14px; box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3); display: flex; align-items: center; gap: 8rpx;">
                    <text>🚫</text>禁言列表
                </view>
                <view @tap="ts_admin" data-type="0" data-qq="" :data-id="id" style="background: linear-gradient(45deg, #f44336, #d32f2f); color: white; padding: 12rpx 24rpx; border-radius: 25rpx; font-size: 14px; box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3); display: flex; align-items: center; gap: 8rpx;">
                    <text>⚠️</text>投诉{{ design.landgrave }}
                </view>
                <view
                    v-if="info.attention == 1 && (info.this_da_qq == 1 || info.this_xiao_qq == 1)"
                    @tap="open_soms"
                    data-key="3"
                    style="background: linear-gradient(45deg, #9C27B0, #7B1FA2); color: white; padding: 12rpx 24rpx; border-radius: 25rpx; font-size: 14px; box-shadow: 0 4px 15px rgba(156, 39, 176, 0.3); display: flex; align-items: center; gap: 8rpx;"
                >
                    <text>✋</text>踢出{{ design.landgrave }}
                </view>
            </view>
            <block v-if="info.this_da_qq == 1">
                <view style="background: linear-gradient(135deg, #E91E63 0%, #AD1457 100%); margin: 20rpx; border-radius: 20rpx; padding: 30rpx; box-shadow: 0 8px 25px rgba(233, 30, 99, 0.3);">
                    <view style="display: flex; align-items: center; margin-bottom: 20rpx;">
                        <text style="font-size: 24px; margin-right: 10rpx;">🎯</text>
                        <view style="font-size: 16px; font-weight: 600; color: white;">{{ design.landgrave }}背景设置</view>
                    </view>
                    <view style="display: flex; align-items: center; background: rgba(255,255,255,0.9); border-radius: 15rpx; padding: 20rpx;">
                        <view style="margin-right: 30rpx;">
                            <image
                                v-if="info.realm_back_img"
                                mode="aspectFill"
                                style="width: 80px; height: 80px; border-radius: 10rpx; box-shadow: 0 4px 12px rgba(0,0,0,0.1);"
                                :src="info.realm_back_img"
                            ></image>
                            <view v-else style="width: 80px; height: 80px; border-radius: 10rpx; background: #f5f5f5; display: flex; align-items: center; justify-content: center; color: #999; font-size: 12px;">暂无背景</view>
                        </view>
                        <view @tap="previewOneImage" data-key="1" style="background: linear-gradient(45deg, #2196F3, #1976D2); color: white; padding: 12rpx 24rpx; border-radius: 25rpx; font-size: 14px; box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3); display: flex; align-items: center; gap: 8rpx;">
                            <text>📷</text>上传背景图
                        </view>
                    </view>
                </view>
                <block v-if="info.attention == 1">
                    <view style="background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%); margin: 20rpx; border-radius: 20rpx; padding: 30rpx; box-shadow: 0 8px 25px rgba(156, 39, 176, 0.3);">
                        <view style="display: flex; align-items: center; margin-bottom: 20rpx;">
                            <text style="font-size: 24px; margin-right: 10rpx;">🔒</text>
                            <view style="font-size: 16px; font-weight: 600; color: white;">{{ design.landgrave }}暗号设置</view>
                        </view>
                        <view style="background: rgba(255,255,255,0.9); border-radius: 15rpx; padding: 20rpx;">
                            <view style="display: flex; align-items: center; justify-content: space-between;">
                                <view style="flex: 1;">
                                    <view v-if="info.atence == 1" style="display: flex; align-items: center; gap: 15rpx; margin-bottom: 15rpx;">
                                        <view style="background: #f5f5f5; padding: 10rpx 20rpx; border-radius: 10rpx; font-size: 18px; font-weight: 600; color: #333; border: 2px dashed #9C27B0;">{{ info.atcipher }}</view>
                                        <view @tap="copyBtn" :data-no="info.atcipher" style="background: linear-gradient(45deg, #FF9800, #F57C00); color: white; padding: 8rpx 16rpx; border-radius: 20rpx; font-size: 12px; display: flex; align-items: center; gap: 5rpx;">
                                            <text>📋</text>复制
                                        </view>
                                        <view @tap="set_anhao" style="background: linear-gradient(45deg, #607D8B, #455A64); color: white; padding: 8rpx 16rpx; border-radius: 20rpx; font-size: 12px; display: flex; align-items: center; gap: 5rpx;">
                                            <text>🔄</text>更换
                                        </view>
                                    </view>
                                </view>
                                <view style="display: flex; gap: 10rpx;">
                                    <view v-if="info.attention == 1 && info.atence == 0" @tap="open_atence" style="background: linear-gradient(45deg, #4CAF50, #45a049); color: white; padding: 10rpx 20rpx; border-radius: 25rpx; font-size: 14px; display: flex; align-items: center; gap: 8rpx;">
                                        <text>🔓</text>开启暗号
                                    </view>
                                    <view v-if="info.attention == 1 && info.atence == 1" @tap="open_atence" style="background: linear-gradient(45deg, #f44336, #d32f2f); color: white; padding: 10rpx 20rpx; border-radius: 25rpx; font-size: 14px; display: flex; align-items: center; gap: 8rpx;">
                                        <text>🔒</text>关闭暗号
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </block>
            </block>
            <block v-if="info.this_da_qq == 1">
                <view style="background: linear-gradient(135deg, #00BCD4 0%, #0097A7 100%); margin: 20rpx; border-radius: 20rpx; padding: 30rpx; box-shadow: 0 8px 25px rgba(0, 188, 212, 0.3);">
                    <view style="display: flex; align-items: center; margin-bottom: 20rpx;">
                        <text style="font-size: 24px; margin-right: 10rpx;">📱</text>
                        <view style="font-size: 16px; font-weight: 600; color: white;">微信群聊二维码</view>
                    </view>
                    <view style="display: flex; align-items: center; background: rgba(255,255,255,0.9); border-radius: 15rpx; padding: 20rpx;">
                        <view style="margin-right: 30rpx;">
                            <image v-if="info.group_qrcode" mode="aspectFill" style="width: 120px; height: 120px; border-radius: 10rpx; box-shadow: 0 4px 12px rgba(0,0,0,0.1);" :src="info.group_qrcode"></image>
                            <view v-else style="width: 120px; height: 120px; border-radius: 10rpx; background: #f5f5f5; display: flex; align-items: center; justify-content: center; color: #999; font-size: 12px; text-align: center;">暂无群聊<br/>二维码</view>
                        </view>
                        <view @tap="previewOneImage" data-key="2" style="background: linear-gradient(45deg, #4CAF50, #45a049); color: white; padding: 12rpx 24rpx; border-radius: 25rpx; font-size: 14px; box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3); display: flex; align-items: center; gap: 8rpx;">
                            <text>📷</text>上传群聊二维码
                        </view>
                    </view>
                </view>
            </block>
        </view>

        <view style="padding: 30rpx; background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%); margin: 20rpx; border-radius: 20rpx; box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);">
            <view style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 20rpx;">
                <view style="display: flex; align-items: center;">
                    <text style="font-size: 24px; margin-right: 10rpx;">👑</text>
                    <view style="font-size: 16px; font-weight: 600; color: #8B4513;">{{ design.qq_name }}主</view>
                </view>
                <view @tap="da_mod_show" style="background: rgba(255,255,255,0.8); padding: 8rpx 16rpx; border-radius: 20rpx; font-size: 12px; color: #8B4513; display: flex; align-items: center; gap: 5rpx;">
                    <text>📋</text>{{ design.qq_name }}主权益与规范
                    <text class="cuIcon-right" style="color: #8B4513;"></text>
                </view>
            </view>

            <view style="background: rgba(255,255,255,0.9); border-radius: 15rpx; padding: 20rpx; margin-top: 20rpx;">
                <!-- 圈主列表 -->
                <view v-for="(item, index) in da_qq" :key="index" style="display: flex; align-items: center; padding: 15rpx; margin: 10rpx 0; background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%); border-radius: 15rpx; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
                    <view style="position: relative; margin-right: 20rpx;">
                        <image :src="item.user_head_sculpture" style="width: 60px; height: 60px; border-radius: 50%; border: 3px solid #FFD700; box-shadow: 0 4px 8px rgba(0,0,0,0.1);"></image>
                        <view style="z-index: 50;position: absolute; top: -5px; right: -5px; background: #FFD700; border-radius: 50%; width: 25px; height: 25px; display: flex; align-items: center; justify-content: center; font-size: 12px;">👑</view>
                    </view>

                    <view style="flex: 1;">
                        <view style="font-size: 16px; font-weight: 600; color: #333; margin-bottom: 5rpx;">{{ item.user_nick_name }}</view>
                        <view style="font-size: 12px; color: #666; line-height: 1.4;">{{ item.autograph }}</view>
                    </view>

                    <view v-if="da_qq.length != 0 && item.id != uid" @tap="ts_admin" data-type="1" data-qq="0" :data-id="item.id" style="background: linear-gradient(45deg, #ff9732, #ff6b35); color: white; padding: 8rpx 16rpx; border-radius: 20rpx; font-size: 12px; display: flex; align-items: center; gap: 5rpx;">
                        <text>⚠️</text>投诉{{ design.qq_name }}主
                    </view>
                </view>

                <!-- 圈主空状态提示 -->
                <view v-if="da_qq.length === 0" class="empty-state crown-empty">
                    <view class="empty-icon">
                        <text class="main-icon">👑</text>
                        <text class="sub-icon">😔</text>
                    </view>
                    <view class="empty-title">暂无{{ design.qq_name }}主</view>
                    <view class="empty-desc">这个{{ design.landgrave }}还没有{{ design.qq_name }}主，快来申请成为第一个吧！</view>
                </view>
            </view>

            <view
                @tap="shenqing_da_qq"
                v-if="da_qq.length < 3 && info.this_da_qq == 0"
                data-key="da"
                style="background: linear-gradient(45deg, #4CAF50, #45a049); color: white; padding: 12rpx 24rpx; border-radius: 25rpx; font-size: 14px; box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3); margin: 20rpx; display: flex; align-items: center; justify-content: center; gap: 8rpx; width: fit-content; margin-left: auto;"
            >
                <text>📝</text>申请{{ design.qq_name }}主
            </view>
        </view>
        <view style="padding: 30rpx; background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%); margin: 20rpx; border-radius: 20rpx; box-shadow: 0 8px 25px rgba(33, 150, 243, 0.3);">
            <view style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 20rpx;">
                <view style="display: flex; align-items: center;">
                    <text style="font-size: 24px; margin-right: 10rpx;">👮‍♂️</text>
                    <view style="font-size: 16px; font-weight: 600; color: white;">管理员</view>
                </view>
                <view @tap="xiao_mod_show" style="background: rgba(255,255,255,0.8); padding: 8rpx 16rpx; border-radius: 20rpx; font-size: 12px; color: #1976D2; display: flex; align-items: center; gap: 5rpx;">
                    <text>📋</text>管理员权益与规范
                    <text class="cuIcon-right" style="color: #1976D2;"></text>
                </view>
            </view>

            <view style="background: rgba(255,255,255,0.9); border-radius: 15rpx; padding: 20rpx; margin-top: 20rpx;">
                <!-- 管理员列表 -->
                <view v-for="(item, index) in xiao_qq" :key="index" style="display: flex; align-items: center; padding: 15rpx; margin: 10rpx 0; background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%); border-radius: 15rpx; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
                    <view style="position: relative; margin-right: 20rpx;">
                        <image :src="item.user_head_sculpture" style="width: 60px; height: 60px; border-radius: 50%; border: 3px solid #2196F3; box-shadow: 0 4px 8px rgba(0,0,0,0.1);"></image>
                        <view style="z-index: 50;position: absolute; top: -5px; right: -5px; background: #2196F3; border-radius: 50%; width: 25px; height: 25px; display: flex; align-items: center; justify-content: center; font-size: 10px;">👮‍♂️</view>
                    </view>

                    <view style="flex: 1;">
                        <view style="font-size: 16px; font-weight: 600; color: #333; margin-bottom: 5rpx;">{{ item.user_nick_name }}</view>
                        <view style="font-size: 12px; color: #666; line-height: 1.4;">{{ item.autograph }}</view>
                    </view>

                    <view
                        v-if="item.id != uid && info.this_da_qq == 0 && info.this_xiao_qq == 0"
                        @tap="ts_admin"
                        data-type="1"
                        data-qq="1"
                        :data-id="item.id"
                        style="background: linear-gradient(45deg, #ff9732, #ff6b35); color: white; padding: 8rpx 16rpx; border-radius: 20rpx; font-size: 12px; display: flex; align-items: center; gap: 5rpx;"
                    >
                        <text>⚠️</text>投诉管理员
                    </view>
                </view>

                <!-- 管理员空状态提示 -->
                <view v-if="xiao_qq.length === 0" class="empty-state admin-empty">
                    <view class="empty-icon">
                        <text class="main-icon">👮‍♂️</text>
                        <text class="sub-icon">😔</text>
                    </view>
                    <view class="empty-title">暂无管理员</view>
                    <view class="empty-desc" v-if="da_qq.length > 0">{{ design.qq_name }}主还没有设置管理员，等待{{ design.qq_name }}主的任命吧！</view>
                    <view class="empty-desc" v-else>需要先有{{ design.qq_name }}主才能设置管理员哦～</view>
                </view>
            </view>
            <view style="display: flex; justify-content: flex-end; gap: 15rpx; margin-top: 20rpx;">
                <view
                    @tap="shenqing_da_qq"
                    data-key="xiao"
                    v-if="xiao_qq.length < 10 && da_qq.length > 0 && info.this_xiao_qq == 0 && info.this_da_qq == 0"
                    style="background: linear-gradient(45deg, #4CAF50, #45a049); color: white; padding: 12rpx 24rpx; border-radius: 25rpx; font-size: 14px; box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3); display: flex; align-items: center; gap: 8rpx;"
                >
                    <text>📝</text>申请管理员
                </view>
                <navigator :url="'/yl_welore/pages/packageA/apply_admin/index?id=' + id" hover-class="none">
                    <view
                        v-if="xiao_qq.length < 10 && da_qq.length > 0 && info.this_xiao_qq == 0 && info.this_da_qq == 1"
                        style="background: linear-gradient(45deg, #2196F3, #1976D2); color: white; padding: 12rpx 24rpx; border-radius: 25rpx; font-size: 14px; box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3); display: flex; align-items: center; gap: 8rpx;"
                    >
                        <text>📋</text>查看申请列表
                    </view>
                </navigator>
            </view>
        </view>
        <view style="height: 120px; width: 100%; background: linear-gradient(180deg, transparent 0%, rgba(102, 126, 234, 0.1) 100%);"></view>

        <view :class="'cu-modal ' + (shenqing ? 'show' : '')">
            <view class="cu-dialog enhanced-modal-dialog">
                <view class="enhanced-modal-header">
                    <view class="modal-title-with-icon">
                        <text class="modal-emoji">📝</text>
                        <text class="modal-title-text">申请理由</text>
                    </view>
                    <view class="modal-close-btn" @tap="no_shenqing_da_qq">
                        <text class="close-icon">✕</text>
                    </view>
                </view>
                <view v-if="shenqing" class="enhanced-modal-content">
                    <textarea class="enhanced-textarea" @input="get_shenqing_text" :auto-height="true" value="" maxlength="300" placeholder="请详细说明您的申请理由..." />
                </view>
                <view class="enhanced-modal-footer">
                    <button class="enhanced-btn cancel-btn" @tap="no_shenqing_da_qq">
                        <text class="btn-icon">❌</text>
                        <text>取消</text>
                    </button>
                    <button class="enhanced-btn confirm-btn" @tap="shenqing_submit">
                        <text class="btn-icon">✅</text>
                        <text>确定</text>
                    </button>
                </view>
            </view>
        </view>

        <view :class="'cu-modal ' + (tousu ? 'show' : '')">
            <view class="cu-dialog enhanced-modal-dialog">
                <view class="enhanced-modal-header complaint-header">
                    <view class="modal-title-with-icon">
                        <text class="modal-emoji">⚠️</text>
                        <text class="modal-title-text">投诉理由</text>
                    </view>
                    <view class="modal-close-btn" @tap="no_shenqing_da_qq">
                        <text class="close-icon">✕</text>
                    </view>
                </view>
                <view v-if="tousu" class="enhanced-modal-content">
                    <textarea class="enhanced-textarea" @input="get_tc_text" :auto-height="true" value="" maxlength="300" placeholder="请详细说明投诉理由..." />
                </view>
                <view class="enhanced-modal-footer">
                    <button class="enhanced-btn cancel-btn" @tap="no_shenqing_da_qq">
                        <text class="btn-icon">❌</text>
                        <text>取消</text>
                    </button>
                    <button class="enhanced-btn confirm-btn warning-btn" @tap="tc_submit">
                        <text class="btn-icon">📝</text>
                        <text>提交投诉</text>
                    </button>
                </view>
            </view>
        </view>

        <view :class="'cu-modal ' + (da_mod ? 'show' : '')">
            <view class="cu-dialog enhanced-modal-dialog info-modal">
                <view class="enhanced-modal-header crown-header">
                    <view class="modal-title-with-icon">
                        <text class="modal-emoji">👑</text>
                        <text class="modal-title-text">{{ design.qq_name }}主权益与规范</text>
                    </view>
                    <view class="modal-close-btn" @tap="hideModal">
                        <text class="close-icon">✕</text>
                    </view>
                </view>
                <view class="enhanced-modal-content info-content">
                    <view class="info-text">
                        <text class="highlight-text">{{ design.qq_name }}主</text>可以在任职的<text class="highlight-text">{{ design.landgrave }}</text>里管理{{ design.landgrave }}内容和用户,还有闪亮亮的独家<text class="highlight-text">{{ design.qq_name }}主标识</text>哦。
                    </view>
                    <view class="info-text">
                        <text class="highlight-text">{{ design.qq_name }}主团队</text>由{{ design.qq_name }}主和管理员构成。由{{ design.qq_name }}主建立,并为{{ design.landgrave }}的发展和秩序负责。
                    </view>
                    <view class="info-note">
                        <text class="note-icon">📌</text>
                        <text>{{ design.qq_name }}主最多三位</text>
                    </view>
                </view>
                <view class="enhanced-modal-footer single-btn">
                    <button class="enhanced-btn confirm-btn crown-btn" @tap="hideModal">
                        <text class="btn-icon">👍</text>
                        <text>我知道了</text>
                    </button>
                </view>
            </view>
        </view>

        <view :class="'cu-modal ' + (xiao_mod ? 'show' : '')">
            <view class="cu-dialog enhanced-modal-dialog info-modal">
                <view class="enhanced-modal-header admin-header">
                    <view class="modal-title-with-icon">
                        <text class="modal-emoji">👮‍♂️</text>
                        <text class="modal-title-text">管理员权益与规范</text>
                    </view>
                    <view class="modal-close-btn" @tap="hideModal">
                        <text class="close-icon">✕</text>
                    </view>
                </view>
                <view class="enhanced-modal-content info-content">
                    <view class="info-text">
                        管理员由<text class="highlight-text">{{ design.qq_name }}主</text>设置,{{ design.qq_name }}主有权取消其身份。
                    </view>
                    <view class="info-text">
                        作为{{ design.qq_name }}主团队的一份子,管理员拥有<text class="highlight-text">{{ design.landgrave }}</text>内:
                    </view>
                    <view class="admin-privileges">
                        <view class="privilege-item"><text class="privilege-icon">🗑️</text> 删除帖子</view>
                        <view class="privilege-item"><text class="privilege-icon">📌</text> 置顶帖子</view>
                        <view class="privilege-item"><text class="privilege-icon">🏷️</text> 酷炫标识</view>
                    </view>
                    <view class="info-note">
                        <text class="note-icon">📋</text>
                        <text>管理员最多十位</text>
                    </view>
                </view>
                <view class="enhanced-modal-footer single-btn">
                    <button class="enhanced-btn confirm-btn admin-btn" @tap="hideModal">
                        <text class="btn-icon">👍</text>
                        <text>我知道了</text>
                    </button>
                </view>
            </view>
        </view>

        <view :class="'cu-modal ' + (anhao_mode ? 'show' : '')">
            <view class="cu-dialog enhanced-modal-dialog">
                <view class="enhanced-modal-header cipher-header">
                    <view class="modal-title-with-icon">
                        <text class="modal-emoji">🔐</text>
                        <text class="modal-title-text">自动/手动生成暗号</text>
                    </view>
                    <view class="modal-close-btn" @tap="hideModal">
                        <text class="close-icon">✕</text>
                    </view>
                </view>
                <view class="enhanced-modal-content">
                    <view v-if="anhao == 'tab1'" class="cipher-content">
                        <input :value="this_atcipher" @input="get_ah_text" class="cipher-input" />
                        <view @tap="get_ah_random" class="random-btn">
                            <text class="random-icon">🎲</text>
                            <text>随机生成</text>
                        </view>
                    </view>
                </view>
                <view class="enhanced-modal-footer">
                    <button class="enhanced-btn cancel-btn" @tap="hideModal">
                        <text class="btn-icon">❌</text>
                        <text>取消</text>
                    </button>
                    <button class="enhanced-btn confirm-btn cipher-btn" @tap="update_ah">
                        <text class="btn-icon">💾</text>
                        <text>确定</text>
                    </button>
                </view>
            </view>
        </view>

        <view class="enhanced-load-modal" v-if="loadModal">
            <view class="spinner-container">
                <view class="spinner"></view>
            </view>
            <view class="upload-text">
                <text class="upload-icon">📤</text>
                <text>上传中...</text>
            </view>
        </view>

        <view :class="'cu-modal ' + (set_img_quan ? 'show' : '')" @touchmove.stop.prevent="true">
            <view class="cu-dialog enhanced-modal-dialog qrcode-modal">
                <view class="enhanced-modal-header qrcode-header">
                    <view class="modal-title-with-icon">
                        <text class="modal-emoji">📱</text>
                        <text class="modal-title-text">圈子二维码</text>
                    </view>
                </view>
                <view class="qrcode-container">
                    <scroll-view scroll-y v-if="set_img_quan" class="qrcode-content">
                        <view id="canvas-container" class="canvas-wrapper">
                            <canvas canvas-id="myCanvas" class="qrcode-canvas"></canvas>
                        </view>
                    </scroll-view>
                </view>
                <view class="enhanced-modal-footer">
                    <button class="enhanced-btn cancel-btn" @tap="no_set_img_quan">
                        <text class="btn-icon">❌</text>
                        <text>取消</text>
                    </button>
                    <button class="enhanced-btn confirm-btn qrcode-btn" @tap="saveShareImg">
                        <text class="btn-icon">💾</text>
                        <text>保存</text>
                    </button>
                </view>
            </view>
        </view>
    </view>
</template>

<style scoped>
/* 增强弹窗样式 */
.enhanced-modal-dialog {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 24rpx !important;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    animation: modalSlideIn 0.3s ease-out;
    backdrop-filter: blur(10rpx);
}

@keyframes modalSlideIn {
    from {
        transform: scale(0.8) translateY(-50rpx);
        opacity: 0;
    }
    to {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
}

/* 弹窗头部样式 */
.enhanced-modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: white;
    position: relative;
}

.enhanced-modal-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2rpx;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
}

.complaint-header {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
}

.crown-header {
    background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
    color: #8B4513;
}

.admin-header {
    background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
}

.cipher-header {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: #333;
}

.qrcode-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.modal-title-with-icon {
    display: flex;
    align-items: center;
    gap: 15rpx;
}

.modal-emoji {
    font-size: 36rpx;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10rpx);
    }
    60% {
        transform: translateY(-5rpx);
    }
}

.modal-title-text {
    font-size: 32rpx;
    font-weight: 600;
    letter-spacing: 1rpx;
}

.modal-close-btn {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.modal-close-btn:active {
    transform: scale(0.9);
    background: rgba(255, 255, 255, 0.3);
}

.close-icon {
    font-size: 28rpx;
    font-weight: bold;
}

/* 弹窗内容样式 */
.enhanced-modal-content {
    padding: 40rpx;
    min-height: 120rpx;
}

.enhanced-textarea {
    width: 100%;
    min-height: 200rpx;
    padding: 30rpx;
    border: 2rpx solid #e9ecef;
    border-radius: 16rpx;
    font-size: 28rpx;
    line-height: 1.6;
    background: #f8f9fa;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.enhanced-textarea:focus {
    border-color: #667eea;
    background: #ffffff;
    box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

/* 信息内容样式 */
.info-content {
    line-height: 1.8;
    font-size: 28rpx;
    color: #333;
}

.info-text {
    margin-bottom: 20rpx;
    padding: 20rpx;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 12rpx;
    border-left: 4rpx solid #667eea;
}

.highlight-text {
    color: #667eea;
    font-weight: 600;
    background: linear-gradient(135deg, #667eea, #764ba2);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.info-note {
    display: flex;
    align-items: center;
    gap: 10rpx;
    padding: 20rpx;
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-radius: 12rpx;
    margin-top: 20rpx;
    border: 1rpx solid #ffd93d;
}

.note-icon {
    font-size: 24rpx;
}

/* 管理员权限列表 */
.admin-privileges {
    margin: 20rpx 0;
    padding: 20rpx;
    background: rgba(78, 205, 196, 0.1);
    border-radius: 12rpx;
    border: 1rpx solid rgba(78, 205, 196, 0.3);
}

.privilege-item {
    display: flex;
    align-items: center;
    gap: 15rpx;
    padding: 10rpx 0;
    font-size: 26rpx;
    color: #333;
}

.privilege-icon {
    font-size: 24rpx;
}

/* 暗号设置样式 */
.cipher-content {
    text-align: center;
}

.cipher-input {
    width: 100%;
    height: 100rpx;
    font-size: 36rpx;
    text-align: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2rpx solid #dee2e6;
    border-radius: 16rpx;
    margin-bottom: 30rpx;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.cipher-input:focus {
    border-color: #667eea;
    background: #ffffff;
    box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

.random-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10rpx;
    padding: 20rpx;
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    border-radius: 12rpx;
    color: #333;
    font-size: 28rpx;
    transition: all 0.3s ease;
    cursor: pointer;
}

.random-btn:active {
    transform: scale(0.98);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.random-icon {
    font-size: 24rpx;
    animation: rotate 2s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 弹窗底部按钮样式 */
.enhanced-modal-footer {
    padding: 30rpx 40rpx;
    background: rgba(248, 249, 250, 0.8);
    display: flex;
    gap: 20rpx;
    justify-content: flex-end;
    border-top: 1rpx solid rgba(0, 0, 0, 0.05);
}

.enhanced-modal-footer.single-btn {
    justify-content: center;
}

.enhanced-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8rpx;
    padding: 20rpx 40rpx;
    border-radius: 25rpx;
    font-size: 28rpx;
    font-weight: 500;
    border: none;
    transition: all 0.3s ease;
    min-width: 160rpx;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.enhanced-btn:active {
    transform: scale(0.98);
}

.cancel-btn {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #6c757d;
    border: 2rpx solid #dee2e6;
}

.cancel-btn:active {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}

.confirm-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.confirm-btn:active {
    background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
}

.warning-btn {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
}

.crown-btn {
    background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
    color: #8B4513;
}

.admin-btn {
    background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
}

.cipher-btn {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: #333;
}

.qrcode-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.btn-icon {
    font-size: 24rpx;
}

/* 加载弹窗样式 */
.enhanced-load-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(10rpx);
}

.spinner-container {
    margin-bottom: 30rpx;
}

.spinner {
    width: 80rpx;
    height: 80rpx;
    border: 6rpx solid rgba(255, 255, 255, 0.3);
    border-top: 6rpx solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.upload-text {
    display: flex;
    align-items: center;
    gap: 10rpx;
    color: white;
    font-size: 28rpx;
    font-weight: 500;
}

.upload-icon {
    font-size: 32rpx;
    animation: bounce 1.5s infinite;
}

/* 二维码弹窗样式 */
.qrcode-modal {
    width: 600rpx !important;
}

.qrcode-container {
    padding: 40rpx;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.qrcode-content {
    width: 100%;
    height: 750rpx;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.canvas-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
}

.qrcode-canvas {
    width: 100%;
    height: 100%;
    border-radius: 16rpx;
}

/* 响应式优化 */
@media (max-width: 750rpx) {
    .enhanced-modal-dialog {
        margin: 40rpx 20rpx;
        width: auto;
    }

    .qrcode-modal {
        width: auto !important;
        margin: 40rpx 20rpx;
    }

    .enhanced-modal-footer {
        flex-direction: column;
    }

    .enhanced-btn {
        width: 100%;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .enhanced-modal-dialog {
        background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
        color: white;
    }

    .info-text {
        background: rgba(102, 126, 234, 0.2);
        color: #e2e8f0;
    }

    .enhanced-textarea {
        background: #4a5568;
        color: white;
        border-color: #718096;
    }

    .cipher-input {
        background: #4a5568;
        color: white;
        border-color: #718096;
    }
}

/* 空状态样式 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60rpx 40rpx;
    text-align: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 16rpx;
    border: 2rpx dashed #dee2e6;
    margin: 20rpx 0;
    position: relative;
    overflow: hidden;
}

.empty-state::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 50% 50%, rgba(102, 126, 234, 0.05) 0%, transparent 70%);
    pointer-events: none;
}

.crown-empty {
    background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
    border-color: #ffd54f;
}

.crown-empty::before {
    background: radial-gradient(circle at 50% 50%, rgba(255, 193, 7, 0.1) 0%, transparent 70%);
}

.admin-empty {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-color: #64b5f6;
}

.admin-empty::before {
    background: radial-gradient(circle at 50% 50%, rgba(33, 150, 243, 0.1) 0%, transparent 70%);
}

.empty-icon {
    position: relative;
    margin-bottom: 30rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.main-icon {
    font-size: 80rpx;
    opacity: 0.8;
    animation: float 3s ease-in-out infinite;
}

.sub-icon {
    font-size: 40rpx;
    position: absolute;
    bottom: -10rpx;
    right: -10rpx;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    width: 50rpx;
    height: 50rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    animation: bounce 2s ease-in-out infinite;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10rpx);
    }
}

.empty-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #495057;
    margin-bottom: 15rpx;
    letter-spacing: 1rpx;
}

.crown-empty .empty-title {
    color: #f57c00;
}

.admin-empty .empty-title {
    color: #1976d2;
}

.empty-desc {
    font-size: 26rpx;
    color: #6c757d;
    line-height: 1.6;
    max-width: 400rpx;
    opacity: 0.8;
}

.crown-empty .empty-desc {
    color: #ff8f00;
}

.admin-empty .empty-desc {
    color: #1565c0;
}

/* 空状态响应式优化 */
@media (max-width: 750rpx) {
    .empty-state {
        padding: 40rpx 20rpx;
    }

    .main-icon {
        font-size: 60rpx;
    }

    .sub-icon {
        font-size: 30rpx;
        width: 40rpx;
        height: 40rpx;
    }

    .empty-title {
        font-size: 28rpx;
    }

    .empty-desc {
        font-size: 24rpx;
    }
}
</style>

<script>
import http from '../../../util/http.js';
import util from '../../../util/data.js';
var app = getApp();
var fsm = uni.getFileSystemManager();
export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            http_root: app.globalData.http_root,
            id: 0,
            user_info: {},
            current: '男',
            anhao: 'tab1',
            img: '',
            nick_name: '',
            autograph: '',
            shenqing: false,
            animationSqData: {},
            shenqing_text: '',
            da_mod: false,
            get_tc_text: '',
            anhao_mode: false,
            loadModal: false,
            set_img_quan: false,
            uid: null,
            design: null,
            info: {},
            this_atcipher: '',
            da_qq: [],
            xiao_qq: [],
            xiao_mod:false,
            user_id: null,
            user_type: null,
            user_qq: null,
            tousu: false,
            imagePath_c: '',
            shenqing_type: ''
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        var e = app.globalData.getCache('userinfo');
        var design = uni.getStorageSync('is_diy');
        this.id = options.id;
        this.uid = e.uid;
        this.design = design;
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {
        this.get_qq_info();
    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {
        var forward = app.globalData.forward;
        console.log(forward);
        if (forward) {
            return {
                title: forward.title,
                path: '/yl_welore/pages/index/index',
                imageUrl: forward.reis_img
            };
        } else {
            return {
                title: '您的好友给您发了一条信息',
                path: '/yl_welore/pages/index/index'
            };
        }
    },

    methods: {
        /**
         * 先下载头像图片
         */
        getAvaterInfo() {
            uni.showLoading({
                title: '生成中...',
                mask: true
            });
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.img = this.info.realm_icon;
            var b = app.globalData.api_root + 'User/base64EncodeImage';
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    //this.get_drawTextVertical(20, '﹁' + this.data.info.realm_name + '﹂', 135, 110);
                    this.get_qrcode(res.data.base); //继续下载二维码图片
                },

                fail: function () {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },
        get_drawTextVertical(fontSize, text, x, y) {
            var arrText = text.split('');
            var arrWidth = arrText.map(function (letter) {
                return fontSize;
            });
            var k = 0;
            // 开始逐字绘制
            arrText.forEach(function (letter, index) {
                // 确定下一个字符的纵坐标位置
                var letterWidth = arrWidth[index];
                // 是否需要旋转判断
                var code = letter.charCodeAt(0);
                if (code <= 256) {
                } else if (index > 0 && text.charCodeAt(index - 1) < 256) {
                    // y修正
                    y = y + arrWidth[index - 1] / 2;
                }
                // 确定下一个字符的纵坐标位置
                var letterWidth = arrWidth[index];
                y = y + letterWidth;
                k = y;
            });
            console.log(k);
            this.imgheght = k + 200;
            // 水平垂直对齐方式还原
        },

        get_qrcode(avaterSrc) {
            uni.showLoading({
                title: '生成中...',
                mask: true
            });
            this.set_img_quan = true;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.id;
            params.much_id = app.globalData.siteInfo.uniacid;
            var b = app.globalData.api_root + 'Home/quan_qrcode';
            uni.request({
                url: b,
                method: 'POST',
                data: params,
                responseType: 'arraybuffer',
                header: {
                    'content-type': 'application/json,charset=utf-8'
                },
                success: (red) => {
                    console.log(red);
                    var base64 = uni.arrayBufferToBase64(red.data);
                    this.sharePosteCanvas(avaterSrc, base64);
                }
            });
        },
        sharePosteCanvas(avaterSrc, codeSrc) {
            uni.showLoading({
                title: '生成中...',
                mask: true
            });
            const ctx = uni.createCanvasContext('myCanvas', this);
            var width = '';
            uni.createSelectorQuery()
                .in(this)
                .select('#canvas-container')
                .boundingClientRect((rect) => {
                    var height = rect.height;
                    width = rect.width;
                    ctx.save();
                    ctx.setFillStyle('#ffffff');
                    this.darwRoundRect(0, 0, width, height, 15, ctx);
                    ctx.fillRect(0, 0, width, height);
                    ctx.restore();
                    //头像为正方形
                    const fileName_head = `${uni.env.USER_DATA_PATH}` + '/head_img' + this.id + '.png';
                    if (avaterSrc) {
                        ctx.beginPath();
                        //ctx.fill()
                        var avatarurl_width = 80; //绘制的头像宽度
                        var avatarurl_heigth = 80; //绘制的头像高度
                        var avatarurl_x = (width - 80) / 2; //绘制的头像在画布上的位置
                        var avatarurl_y = 30; //绘制的头像在画布上的位置
                        //先画个圆   前两个参数确定了圆心 （x,y） 坐标  第三个参数是圆的半径  四参数是绘图方向  默认是false，即顺时针
                        var headData = avaterSrc.split(',');
                        const head_buffer = util._base64ToArrayBuffer(headData[1]);
                        fsm.writeFileSync(fileName_head, head_buffer, 'binary');
                        ctx.arc(avatarurl_width / 2 + avatarurl_x, avatarurl_heigth / 2 + 30, avatarurl_width / 2, 0, Math.PI * 2, false);
                        ctx.clip();
                        ctx.drawImage(fileName_head, avatarurl_x, avatarurl_y, avatarurl_width, avatarurl_heigth);
                    }
                    ctx.restore();
                    ctx.setFontSize(20);
                    ctx.setFillStyle('#000');
                    ctx.setTextAlign('center');
                    ctx.fillText('「' + this.info.realm_name + '」', width / 2, avatarurl_y + 120);
                    // 绘制二维码
                    const buffer_code = util._base64ToArrayBuffer(codeSrc);
                    const fileName_code = `${uni.env.USER_DATA_PATH}` + '/code_img' + this.id + '.png';
                    fsm.writeFileSync(fileName_code, buffer_code, 'binary');
                    ctx.drawImage(fileName_code, (width - 110) / 2, 200, 110, 110);
                    ctx.setFontSize(14);
                    ctx.setFillStyle('#000');
                    ctx.fillText('长按识别二维码进入圈子', width / 2, 350);
                })
                .exec();
            setTimeout(() => {
                ctx.draw();
                uni.canvasToTempFilePath({
                    canvasId: 'myCanvas',
                    success: (res) => {
                        var tempFilePath = res.tempFilePath;
                        this.imagePath_c = tempFilePath;
                    },
                    fail: function (res) {
                        console.log(res);
                    }
                });
                uni.hideLoading();
            }, 1000);
        },
        no_set_img_quan() {
            this.set_img_quan = false;
        },
        //点击保存到相册
        saveShareImg() {
            uni.showLoading({
                title: '正在保存',
                mask: true
            });
            setTimeout(() => {
                uni.canvasToTempFilePath(
                    {
                        canvasId: 'myCanvas',
                        success: function (res) {
                            uni.hideLoading();
                            var tempFilePath = res.tempFilePath;
                            uni.saveImageToPhotosAlbum({
                                filePath: tempFilePath,
                                success(res) {
                                    uni.showModal({
                                        content: '图片已保存到相册，赶紧晒一下吧~',
                                        showCancel: false,
                                        confirmText: '好的',
                                        confirmColor: '#333',
                                        success: function (res) {
                                            if (res.confirm) {
                                            }
                                        },
                                        fail: function (res) {}
                                    });
                                },
                                fail: function (res) {
                                    console.log(res);
                                    if (res.errMsg == 'saveImageToPhotosAlbum:fail auth deny') {
                                        uni.showModal({
                                            content: '检测到您未打开微信保存图片到相册，开启后即可保存图片',
                                            confirmText: '去开启',
                                            success(res) {
                                                if (res.confirm) {
                                                    uni.openSetting({
                                                        success(res) {}
                                                    });
                                                } else if (res.cancel) {
                                                }
                                            }
                                        });
                                    }
                                }
                            });
                        },

                        fail: function (err) {
                            console.log(err);
                        }
                    },
                    this
                );
            }, 1000);
        },
        /**
         * 绘制圆角矩形
         * @param {*} x 起始点x坐标
         * @param {*} y 起始点y坐标
         * @param {*} w 矩形宽
         * @param {*} h 矩形高
         * @param {*} r 圆角半径
         * @param {*} ctx 画板上下文
         */
        darwRoundRect(x, y, w, h, r, ctx) {
            ctx.save();
            ctx.beginPath();

            // 左上弧线
            ctx.arc(x + r, y + r, r, 1 * Math.PI, 1.5 * Math.PI);
            // 左直线
            ctx.moveTo(x, y + r);
            ctx.lineTo(x, y + h - r);
            // 左下弧线
            ctx.arc(x + r, y + h - r, r, 0.5 * Math.PI, 1 * Math.PI);
            // 下直线
            ctx.lineTo(x + r, y + h);
            ctx.lineTo(x + w - r, y + h);
            // 右下弧线
            ctx.arc(x + w - r, y + h - r, r, 0 * Math.PI, 0.5 * Math.PI);
            // 右直线
            ctx.lineTo(x + w, y + h - r);
            ctx.lineTo(x + w, y + r);
            // 右上弧线
            ctx.arc(x + w - r, y + r, r, 1.5 * Math.PI, 2 * Math.PI);
            // 上直线
            ctx.lineTo(x + w - r, y);
            ctx.lineTo(x + r, y);
            ctx.closePath();
            //ctx.setFillStyle('white')
            ctx.clip();
        },
        drawTextVertical(context, fontSize, text, x, y) {
            var arrText = text.split('');
            var arrWidth = arrText.map(function (letter) {
                return fontSize;
            });
            var align = context.textAlign;
            var baseline = context.textBaseline;
            if (align == 'left') {
                x = x + Math.max.apply(null, arrWidth) / 2;
            } else if (align == 'right') {
                x = x - Math.max.apply(null, arrWidth) / 2;
            }
            if (baseline == 'bottom' || baseline == 'alphabetic' || baseline == 'ideographic') {
                y = y - arrWidth[0] / 2;
            } else if (baseline == 'top' || baseline == 'hanging') {
                y = y + arrWidth[0] / 2;
            }
            context.textAlign = 'center';
            context.textBaseline = 'middle';
            var k = 0;
            // 开始逐字绘制
            arrText.forEach(function (letter, index) {
                // 确定下一个字符的纵坐标位置
                var letterWidth = arrWidth[index];
                // 是否需要旋转判断
                var code = letter.charCodeAt(0);
                if (code <= 256) {
                    context.translate(x, y);
                    // 英文字符，旋转90°
                    context.rotate((90 * Math.PI) / 180);
                    context.translate(-x, -y);
                } else if (index > 0 && text.charCodeAt(index - 1) < 256) {
                    // y修正
                    y = y + arrWidth[index - 1] / 2;
                }
                context.setFillStyle('#000');
                context.fillText(letter, x, y);
                // 旋转坐标系还原成初始态
                context.setTransform(1, 0, 0, 1, 0, 0);
                // 确定下一个字符的纵坐标位置
                var letterWidth = arrWidth[index];
                y = y + letterWidth;
                k = y;
            });
            this.qrcode_top = k;
            // 水平垂直对齐方式还原
            context.textAlign = align;
            context.textBaseline = baseline;
            context.save();
        },
        open_soms(item) {
            var key = item.currentTarget.dataset.key;
            if (key == 1) {
                uni.navigateTo({
                    url: '/yl_welore/pages/packageA/territory_interest/index?id=' + this.id
                });
            }
            if (key == 2) {
                uni.navigateTo({
                    url: '/yl_welore/pages/packageA/user_banned/index?id=' + this.id
                });
            }
            if (key == 3) {
                uni.navigateTo({
                    url: '/yl_welore/pages/packageB/kick_out/index?id=' + this.id
                });
            }
        },

        /**
         * 更换暗号
         */
        set_anhao() {
            this.anhao_mode = true;
        },
        /**
         * 获取新暗号
         */
        get_ah_text(e) {
            this.this_atcipher = e.detail.value;
        },
        /**
         * 获取新暗号
         */
        set_get_new_ah() {},
        /**
         * 修改暗号
         */
        update_ah() {
            if (this.info['atcipher'] == this.this_atcipher) {
                this.hideModal();
                uni.showToast({
                    title: '没有做任何更改!',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }
            var b = app.globalData.api_root + 'User/update_atcipher';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.id;
            params.this_atcipher = this.this_atcipher;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    this.hideModal();
                    if (res.data.status == 'success') {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                        this.get_qq_info();
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },
        /**
         * 打开或关闭暗号
         */
        open_atence() {
            var b = app.globalData.api_root + 'User/open_atence';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.id;
            params.atcipher_type = this.info['atence'];
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                        this.get_qq_info();
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },
        /**
         * 随机生成
         */
        get_ah_random() {
            var b = app.globalData.api_root + 'Circle/get_q_random';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    this.this_atcipher = res.data.code;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },
        /**
         * 投诉用户
         */
        ts_admin(e) {
            this.user_id = e.currentTarget.dataset.id;
            this.user_type = e.currentTarget.dataset.type;
            this.user_qq = e.currentTarget.dataset.qq;
            this.tousu = true;
        },
        handleChange_ah(d) {
            console.log(d);
            this.anhao = d.currentTarget.dataset.key;
        },
        /**
         * 投诉用户提交
         */
        tc_submit() {
            var b = app.globalData.api_root + 'User/add_tc_submit';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            params.id = this.id;
            params.user_id = this.user_id;
            params.user_type = this.user_type;
            params.labor = this.user_qq;
            params.get_tc_text = this.get_tc_text;
            if (this.get_tc_text == '') {
                uni.showToast({
                    title: '请填写投诉理由',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        this.get_tc_text = '';
                        this.tousu = false;
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    } else {
                        this.get_tc_text = '';
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },
        /**
         * 获取投诉内容
         */
        get_tc_text(e) {
            this.get_tc_text = e.detail.value;
        },
        /**
         * 申请理由
         */
        get_shenqing_text(e) {
            this.shenqing_text = e.detail.value;
        },
        /**
         * 圈主权益
         */
        da_mod_show() {
            this.da_mod = true;
        },
        /**
         * 管理员权益
         */
         xiao_mod_show() {
            this.xiao_mod = true;
        },
        hideModal() {
            this.da_mod = false;
            this.xiao_mod = false;
            this.anhao_mode = false;
        },
        /**
         * 申请
         */
        shenqing_da_qq(e) {
            // 创建一个动画实例
            var animation = uni.createAnimation({
                // 动画持续时间
                duration: 150,
                // 定义动画效果，当前是匀速
                timingFunction: 'linear'
            });
            // 将该变量赋值给当前动画
            this.animation = animation;
            // 先在y轴偏移，然后用step()完成一个动画
            animation.translateY(230).step();
            // 用setData改变当前动画
            this.animationSqData = animation.export();
            this.shenqing = true;
            this.shenqing_type = e.currentTarget.dataset.key;
            // 设置setTimeout来改变y轴偏移量，实现有感觉的滑动
            setTimeout(() => {
                animation.translateY(0).step();
                this.animationSqData = animation.export();
            }, 100);
        },
        /**
         * 关闭申请
         */
        no_shenqing_da_qq() {
            this.shenqing = false;
            this.tousu = false;
        },
        /**
         * 申请提交
         */
        shenqing_submit() {
            var b = app.globalData.api_root + 'User/add_territory_learned';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.id;
            params.upshot = this.shenqing_text;
            params.shenqing_type = this.shenqing_type;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                        var subscribe = app.globalData.getCache('subscribe');
                        if (subscribe && subscribe['YL0010'] && subscribe['YL0008'] && subscribe['YL0009']) {
                            app.globalData.authorization(subscribe['YL0010'], subscribe['YL0008'], subscribe['YL0009'], (res) => {});
                        }
                        this.no_shenqing_da_qq();
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },
        /**
         * 上传主图
         */
        previewOneImage(c) {
            var e = app.globalData.getCache('userinfo');
            var b = app.globalData.api_root + 'Service/territory_img_upload';
            var key = c.currentTarget.dataset.key;
            uni.chooseImage({
                count: 1,
                sizeType: ['original', 'compressed'],
                // 可以指定是原图还是压缩图，默认二者都有
                sourceType: ['album', 'camera'],
                // 可以指定来源是相册还是相机，默认二者都有
                success: (res) => {
                    this.loadModal = true;
                    var tempFilePaths = res.tempFilePaths;
                    uni.uploadFile({
                        url: b,
                        filePath: tempFilePaths[0],
                        name: 'sngpic',
                        header: {
                            'content-type': 'multipart/form-data'
                        },
                        formData: {
                            'content-type': 'multipart/form-data',
                            token: e.token,
                            openid: e.openid,
                            tory_id: this.id,
                            much_id: app.globalData.siteInfo.uniacid,
                            key: key
                        },
                        success: (res) => {
                            console.log(res);
                            var data = JSON.parse(res.data);
                            console.log(data);
                            if (data.status == 'error') {
                                uni.showModal({
                                    title: '提示',
                                    content: data.msg
                                });
                            } else {
                                uni.showToast({
                                    title: '上传成功',
                                    icon: 'success',
                                    duration: 2000
                                });
                                this.get_qq_info();
                            }
                            this.loadModal = false;
                        },
                        fail: (res) => {
                            uni.showToast({
                                title: '上传错误！',
                                icon: 'none',
                                duration: 2000
                            });
                        }
                    });
                }
            });
        },
        /**
         * 获取会员信息
         */
        get_qq_info() {
            var b = app.globalData.api_root + 'Circle/get_qq_info';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.id;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        this.info = res.data.info;
                        this.this_atcipher = res.data.info.atcipher;
                        this.da_qq = res.data.info.da_qq;
                        this.xiao_qq = res.data.info.xiao_qq;
                        this.img = res.data.info.realm_back_img;
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },
        handleFruitChange({ detail = {} }) {
            this.current = detail.value;
        },
        /**
         * 一键复制
         */
        copyBtn(e) {
            uni.setClipboardData({
                data: e.currentTarget.dataset.no,
                success: function (res) {}
            });
        },
        /**
         * 返回
         */
        _navback() {
            uni.navigateBack();
        }
    }
};
</script>
<style>
page {
    background-color: #fff;
}
</style>

<template>
<view>
<cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
  <view slot="backText">返回</view>
  <view slot="content" style="color: #2c2b2b;font-weight: 600; font-size: 36rpx;">收货地址</view>
</cu-custom>
<view v-if="copyright.receipt_arbor==1" class="cu-list menu sm-border card-menu margin-top" @tap="get_addras" style="margin:10px;">
  <view class="cu-item">
    <view class="content padding-tb-sm">
      <block v-if="address">
        <view>
          {{address.userName}} {{address.telNumber}}
        </view>
        <view class="text-gray text-sm">
          <text class="cicon-person-pin-circle margin-right-xs text-blue" style="font-size: 18px;vertical-align: middle;"></text>
          <text style="vertical-align: middle;">{{address.provinceName}} {{address.cityName}} {{address.countyName}} {{address.detailInfo}}</text>
        </view>
      </block>
      <block v-if="(!address)">
        <view style="font-size: 14px;">请选择收货地址</view>
      </block>
    </view>
    <view class="action">
      <button class="cu-btn round sm">选择地址</button>
    </view>
  </view>
</view>
<view style="padding:10px;margin:10px;border-radius: 20rpx;" class="bg-white">
  <view class="flex p-xs" style="margin:0px;">
    <view class="flex-sub padding-sm">
      <image :src="goods.product_img[0]" style="width:100px;height:100px;border-radius: 20rpx;"></image>
    </view>
    <view class="flex-twice padding-sm">
      <view style="font-weight: 700;">{{goods.product_name}}</view>
      <view style="margin: 15px 0px;color: #999999;">{{goods.product_synopsis}}</view>
      <view v-if="goods.sku_info" style="color: #999999;">
        <text>{{goods.sku_info.sku_name}}：</text>
        <text>{{goods.sku_info.at_name}}</text>
      </view>
    </view>
  </view>
  <view class="bg-white" style="text-align: right;">
    <image v-if="goods.pay_type==0||goods.pay_type==1" class="now_level" mode="widthFix" style="display: inline-block;width: 30rpx;vertical-align: middle;" :src="$state.diy.currency_icon"></image>
    <text :class="goods.pay_type==2?'text-price':''" style="font-size: 18px;vertical-align: middle;margin-left: 5px;">{{goods.product_price}}</text>
    <text v-if="goods.pay_type==0||goods.pay_type==1" style="font-size: 20rpx;"> ({{goods.pay_type==0?$state.diy.currency:$state.diy.confer}})</text>
  </view>
</view>
<view class="cu-form-group align-start margin" style="border-radius: 20rpx;margin:10px;">
  <textarea @input="set_remark" :value="remark" maxlength="140" style="height: 2.6em;" placeholder="填写备注"></textarea>
</view>

<view class="flex solid-bottom padding bg-white" style="justify-content: space-between;position: fixed;width: 100%;bottom: 0;">
  <view class="padding-sm margin-xs" style="line-height: 30px;">
    <block v-if="goods.open_discount==1">
      <text style="vertical-align: middle;margin-right: 10px;">会员价</text>
      <image v-if="goods.pay_type==0||goods.pay_type==1" class="now_level" mode="widthFix" style="width: 30rpx;vertical-align: middle;" :src="$state.diy.currency_icon"></image>
      <text :class="'text-red '+(goods.pay_type==2?'text-price':'')" style="font-size:20px;margin-left: 10px;font-weight: 600;">{{filters.toFix(goods.product_price*goods.noble_discount)}}</text>
      <text v-if="goods.pay_type==0||goods.pay_type==1" style="font-size: 20rpx;"> ({{goods.pay_type==0?$state.diy.currency:$state.diy.confer}})</text>
    </block>
    <block v-if="goods.open_discount==0">
      <text style="vertical-align: middle;margin-right: 10px;">合计</text>
      <image v-if="goods.pay_type==0||goods.pay_type==1" class="now_level" mode="widthFix" style="width: 30rpx;vertical-align: middle;" :src="$state.diy.currency_icon"></image>
      <text :class="'text-red '+(goods.pay_type==2?'text-price':'')" style="font-size: 20px;margin-left: 10px;font-weight: 600;">{{goods.product_price}}</text>
      <text v-if="goods.pay_type==0||goods.pay_type==1" style="font-size: 20rpx;"> ({{goods.pay_type==0?$state.diy.currency:$state.diy.confer}})</text>
    </block>
  </view>

  <view class="padding-sm margin-xs" @tap="formSubmit">
    <button class="cu-btn bg-red round shadow-blur" :disabled="is_submit">提交订单</button>
  </view>
</view>
</view>
</template>
<!-- <script module="filters" lang="wxs" src="@/yl_welore/pages/packageA/good_info/tofix.wxs"></script> -->
<script >
import http from "../../../util/http.js";
import md5 from "../../../util/md5.js";
const app = getApp();
export default {
  /**
   * 页面的初始数据
   */
  data() {
    return {
      goods: {},
      is_submit: false,
      address: '',
      remark: '',
      sku_index: 0,
	  id: null,
	  copyright: {},
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.sku_index = options.sa_id;
    this.id = options.id;
    this.copyright = getApp().globalData.store.getState().copyright;
    this.get_goods();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    var subscribe = app.globalData.getCache("subscribe");
    if (!subscribe) {
      app.globalData.subscribe_message(res => {
        //请求成功的回调函数
        console.log(res);
        if (res == '') {
          return;
        }
        app.globalData.setCache("subscribe", res.parallelism_data);
      }, () => {
        //请求失败的回调函数，不需要时可省略
      });
    }
  },

  methods: {
    get_addras() {
      uni.chooseAddress({
        success: (res) => {
          this.address = res;
        }
      });
    },

    subscribe() {
      var subscribe = app.globalData.getCache("subscribe");
      if (subscribe && subscribe['YL0009'] && subscribe['YL0010']) {
        app.globalData.authorization(subscribe['YL0009'], subscribe['YL0010'], subscribe['YL0008'], res => {});
      }
    },

    set_remark(d) {
      console.log(d);
      this.remark = d.detail.value;
    },

    /**
     * 提交表单
     */
    formSubmit() {
      uni.showLoading({
        title: '订单提交中'
      });
      this.is_submit = true;

      var b = app.globalData.api_root + 'User/exchange_goods_do';
      var e = app.globalData.getCache("userinfo");
      var params = {};
      params.token = e.token;
      params.openid = e.openid;
      params.id = this.id;
      params.sa_id = this.sku_index;
      if (this.copyright.receipt_arbor == 1) {
        if (this.address == '') {
          uni.showToast({
            title: '请选择收货地址！',
            icon: 'none',
            duration: 1500
          });
          this.is_submit = false;
          return;
        }
        params.real_name = this.address.userName;
        params.phone = this.address.telNumber;
        params.address = this.address.provinceName + this.address.cityName + this.address.countyName + this.address.detailInfo;
        params.remark = this.remark;
      } else {
        params.real_name = '';
        params.phone = '';
        params.address = '';
        params.remark = '';
      }
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          if (res.data.status == 'success') {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
            setTimeout(() => {
              uni.redirectTo({
                url: '/yl_welore/pages/packageA/user_order/index?key=' + res.data.auto_delivery
              });
            }, 1000);
          } else if (res.data.status == 'wxpay') {
            this.pay_submit(res.data.info);
          } else {
            uni.hideLoading();
            this.is_submit = false;
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => {}
          });
        }
      });
    },

    /**
     * 充值
     */
    pay_submit(item) {
      var e = app.globalData.getCache("userinfo");
      var params = {};
      params.token = e.token;
      params.openid = e.openid;
      params.order_id = item.order_id;
      params.uid = e.id;
      var b = app.globalData.api_root + 'Pay/do_goods_pay';
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          if (res.data.return_msg == "OK") {
            var timeStamp = (Date.parse(new Date()) / 1000).toString();
            var pkg = 'prepay_id=' + res.data.prepay_id;
            var nonceStr = res.data.nonce_str;
            var paySign = md5.hexMD5('appId=' + res.data.appid + '&nonceStr=' + nonceStr + '&package=' + pkg + '&signType=MD5&timeStamp=' + timeStamp + "&key=" + res.data.app_info['app_key']).toUpperCase(); //此处用到hexMD5插件
            //发起支付
            uni.requestPayment({
              'timeStamp': timeStamp,
              'nonceStr': nonceStr,
              'package': pkg,
              'signType': 'MD5',
              'paySign': paySign,
              success: (res) => {
                uni.showToast({
                  title: '支付成功！',
                  icon: 'none',
                  duration: 2000
                });
                uni.redirectTo({
                  url: '/yl_welore/pages/packageA/user_order/index'
                });
              },
              complete: () => {
                uni.hideLoading();
                this.is_submit = false;
              }
            });
          } else {
            uni.showModal({
              title: '提示',
              content: '支付参数错误！',
              showCancel: false,
              success: (res) => {}
            });
            uni.hideLoading();
          }
        },
        fail: () => {
          this.is_submit = false;
          uni.hideLoading();
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => {}
          });
        }
      });
    },

    get_goods() {
      var b = app.globalData.api_root + 'Order/get_goods_sku';
      var e = app.globalData.getCache("userinfo");
      var params = {};
      params.token = e.token;
      params.openid = e.openid;
      params.id = this.id;
      params.sa_id = this.sku_index;
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          if (res.data.status == 'success') {
            this.goods = res.data.info;
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => {}
          });
        }
      });
    }
  }
}
</script>
<style >
 .add_submit {background: #ffffff;color:#4facfe;border-radius: 2rem;border: 3px #4facfe solid;
box-shadow:0px 0px  10px 0px#4facfe;
}  
</style>
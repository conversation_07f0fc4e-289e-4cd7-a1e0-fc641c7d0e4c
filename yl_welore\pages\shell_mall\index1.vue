<template>
    <view class="get_page" style="background-image: linear-gradient(to top, #fff1eb 0%, #ace0f9 100%)">
        <cu-custom :isSearch="false" :isBack="$state.diy.elect_sheathe != 0" :ShowUid="false">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">{{ title }}</view>
        </cu-custom>
        <!-- <view style='margin-left:20px;'>
    <view style='font-size:13px;margin-top:7px;margin-left:5px;font-weight:300;'>
      <text>钱包</text>
      <text style="margin-left:5px;">￥{{user.conch}}</text>
    </view>
  </view> -->

        <scroll-view scroll-x class="nav" style="padding: 20px 0px 0px 20px; height: 65px">
            <view :data-key="t_index" @tap="handleChangeScroll" class="cu-item" style="height: 60rpx; line-height: 60rpx" v-for="(item, t_index) in type_list" :key="t_index">
                <text :class="current_scroll == t_index ? '_this_index5_1' : '_no_index5_1'">{{ item.name }}</text>

                <view v-if="current_scroll == t_index" style="width: 50%; height: 2px; background-color: #ffffff; margin-top: 18px; border-radius: 10px; margin: 0 auto"></view>
            </view>
        </scroll-view>
        <view class="bg-white" style="margin: 15px; border-radius: 5px; position: relative" v-for="(item, index) in shop_list" :key="index">
            <view @tap="get_url" :data-id="item.id" class="flex p-xs margin-bottom-sm mb-sm align-center" style="padding: 5px">
                <view class="flex-twice padding-xs radius">
                    <image :src="item.product_img[0]" mode="aspectFit" style="width: 200rpx; height: 200rpx; border-radius: 5px"></image>
                </view>
                <view class="flex-treble padding-xs radius" style="align-self: flex-start">
                    <view class="text_num_1" style="font-size: 16px; font-weight: 300; letter-spacing: 1px">
                        {{ item.product_name }}
                    </view>
                    <view class="text_num" style="font-size: 12px; color: #9b9da8; margin-top: 5px; letter-spacing: 1px">
                        {{ item.product_synopsis }}
                    </view>
                    <view style="font-size: 11px; font-weight: 300; letter-spacing: 1px; color: #9b9da8; position: absolute; bottom: 10px">
                        <text v-if="item.noble_exclusive == 1" style="margin-right: 5px">[会员专属]</text>
                        <text>库存:{{ item.product_inventory }}</text>
                    </view>
                </view>
                <!-- <view wx:if="{{item.open_discount==1}}" class="flex-sub padding-xs radius"
        style="color:#66a6ff;text-alent:center;font-size: 16px;font-weight:300">
        <text>￥</text>
        <text>{{filters.toFix(item.product_price*item.noble_discount)}}</text>
        <view style="color:#9B9DA8;font-size:12px;text-decoration:line-through;text-align: center; ">
          <text>￥</text>
          <text>{{item.product_price}}</text>
        </view>
      </view> -->
                <view class="padding-xs radius" style="color: #66a6ff; text-align: center; font-weight: 300">
                    <view>
                        <image
                            v-if="item.pay_type == 0 || item.pay_type == 1"
                            class="now_level"
                            mode="widthFix"
                            style="width: 30rpx; vertical-align: middle; margin-right: 10rpx"
                            :src="$state.diy.currency_icon"
                        ></image>
                        <text :class="item.pay_type == 2 ? 'text-price' : ''" style="vertical-align: middle; font-size: 30rpx">{{ item.product_price }}</text>
                        <text v-if="item.pay_type == 0 || item.pay_type == 1" style="vertical-align: bottom; font-size: 20rpx">
                            ({{ item.pay_type == 0 ? $state.diy.currency : $state.diy.confer }})
                        </text>
                    </view>
                </view>
            </view>
        </view>
        <view :class="'cu-load ' + (!di_msg ? 'loading' : 'over')"></view>
    </view>
</template>

<script>
export default {
    props: ['data', 'compName'],
    computed: {
        $state() {
            return this.$parent.$data.$state;
        },
        title() {
            return this.$parent.$data.title;
        },
        type_list() {
            return this.$parent.$data.type_list;
        },
        current_scroll() {
            return this.$parent.$data.current_scroll;
        },
        shop_list() {
            return this.$parent.$data.shop_list;
        },
        di_msg() {
            return this.$parent.$data.di_msg;
        }
       
    },
    methods: {
        handleChangeScroll(e) {
            this.$emit('handleChangeScroll', e);
        },
        get_url(e) {
            this.$emit('get_url', e);
        },
    }
};
</script>
<style></style>

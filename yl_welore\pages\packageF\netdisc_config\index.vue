<template>
    <view>
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
            <view slot="backText"></view>
            <view slot="content" style="color: #000000; font-weight: 600; font-size: 36rpx">网盘用户用户协议</view>
        </cu-custom>
        <view class="padding" style="letter-spacing: 0.5px; line-height: 20px">
            <rich-text :nodes="desc"></rich-text>
        </view>
    </view>
</template>

<script>
const app = getApp();
var http = require('../../../util/http.js');
var upload = require('../../../util/upload.js');

export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            desc: ''
        }
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.get_my_volume();
    },
    methods: {
        get_my_volume() {
            var b = app.globalData.api_root + 'Storage/get_my_volume';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            http.POST(b, {
                params: params,
                success(res) {
                    that.desc = res.data.info.netdisc_config;
                },
                fail() {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success(res) {}
                    });
                }
            });
        }
    }
}
</script>
<style>
page {
    background-color: #ffffff;
}
</style>

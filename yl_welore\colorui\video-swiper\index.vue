<template>
    <view class="container">
        <swiper class="video-swiper" :circular="circular" easing-function="default" vertical :current="current" duration="200" @change="animationfinish">
            <!-- curQueue 循环会导致video重新插入，objectFit 不可变更 -->
            <swiper-item v-for="(item, index) in curQueue" :key="index">
                <video
                    :id="'video_' + index"
                    class="video_item"
                    :loop="loop"
                    :enable-play-gesture="false"
                    enable-progress-gesture
                    :show-center-play-btn="false"
                    :controls="false"
                    :custom-cache="false"
                    :src="item.url"
                    :data-id="item.id"
                    :object-fit="item.objectFit || 'contain'"
                    :data-index="index"
                    @play="onPlay"
                    @pause="onPause"
                    @ended="onEnded"
                    @error="onError"
                    @timeupdate="onTimeUpdate"
                    @waiting="onWaiting"
                    @progress="onProgress"
                    @loadedmetadata="onLoadedMetaData"
                ></video>
            </swiper-item>
        </swiper>
    </view>
</template>

<script>
const app = getApp();
export default {
    data() {
        return {
            current: 0,
            nextQueue: [],
            prevQueue: [],
            curQueue: [],
            circular: false,
            last_var: 1,
            change_var: -1,
            invalidUp_var: 0,
            invalidDown_var: 0,
            videoContexts_var: [],
            newVals: []
        };
    },
    options: {
        addGlobalClass: true,
        pureDataPattern: /^_/
    },
    props: {
        duration: {
            type: Number,
            default: 500
        },
        easingFunction: {
            type: String,
            default: 'easeInOutCubic'
        },
        loop: {
            type: Boolean,
            default: true
        },
        videoListNewDom: {
            type: Array,
            default: () => []
        },
        videoList: {
            type: Array,
            default: () => []
        }
    },
    mounted() {
        // 处理小程序 attached 生命周期
        this.attached();
    },
    methods: {
        attached: function attached() {},

        createvideoContexts: function createvideoContexts(newVal, num) {
            let newVals = this._videoContexts;
            newVal.map((item, index) => {
                let navval = uni.createVideoContext('video_' + (num + index), this);
                newVals.push(navval);
            });
            this._videoContexts = newVals;
        },

        videoListChangedFun: function _videoListChanged(newVal) {
            var that = this;
            var data = this;
            if (newVal.length > 0) {
                this.createvideoContexts(newVal, 0);
            }
            newVal.forEach(function (item) {
                data.nextQueue.push(item);
            });
            if (data.curQueue.length === 0) {
                this.setData({
                    curQueue: data.nextQueue.splice(0, newVal.length)
                });
            }
        },

        animationfinish: function animationfinish(e) {
            var u = app.globalData.getCache('userinfo');
            if (!u) {
                uni.showToast({
                    title: '更多内容请前往小程序查看',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }
            if (e.detail.current > 7) {
                this.setData({
                    current: 0,
                    curQueue: [],
                    videoContexts_var: [],
                    nextQueue: [],
                    prevQueue: [],
                    circular: false,
                    last_var: 1,
                    change_var: -1,
                    invalidUp_var: 0,
                    invalidDown_var: 0,
                    newVals: []
                });
            } else {
                this.setData({
                    current: e.detail.current
                });
            }
            uni.setStorageSync('slidecurrent', this.current);
            this.trigger(e, 'slide');
            var _data = this;
            var _last = _data._last;
            var _change = _data._change;
            var curQueue = _data.curQueue;
            var prevQueue = _data.prevQueue;
            var nextQueue = _data.nextQueue;
            var current = this.current;
            console.log(current);
            var diff = current - _last;
            if (diff === 0) {
                return;
            }
            this._last = current;
            this.playCurrent(current);
            console.log(current);
            console.log(curQueue);
            // this.triggerEvent('change', {
            //     activeId: curQueue[current].id
            // });
            var direction = diff === 1 || diff === -2 ? 'up' : 'down';
            if (direction === 'up') {
                if (this._invalidDown === 0) {
                    var change = (_change + 1) % 3;
                    var add = nextQueue.shift();
                    var remove = curQueue[change];
                    if (add) {
                        prevQueue.push(remove);
                        curQueue[change] = add;
                        this._change = change;
                    } else {
                        this._invalidUp += 1;
                    }
                } else {
                    this._invalidDown -= 1;
                }
            }
            if (direction === 'down') {
                if (this._invalidUp === 0) {
                    var _change2 = _change;
                    var _remove = curQueue[_change2];
                    var _add = prevQueue.pop();
                    if (_add) {
                        curQueue[_change2] = _add;
                        nextQueue.unshift(_remove);
                        this._change = (_change2 - 1 + 3) % 3;
                    } else {
                        this._invalidDown += 1;
                    }
                } else {
                    this._invalidUp -= 1;
                }
            }
            var circular = true;
            if (nextQueue.length === 0 && current !== 0) {
                circular = false;
            }
            if (prevQueue.length === 0 && current !== 2) {
                circular = false;
            }
            this.setData({
                curQueue: curQueue,
                circular: circular
            });
        },

        playCurrent: function playCurrent(current) {
            this._videoContexts.forEach(function (ctx, index) {
                index != current ? ctx.stop() : ctx.play();
            });
        },

        onPlay: function onPlay(e) {
            this.trigger(e, 'play');
        },

        onPause: function onPause(e) {
            this.trigger(e, 'pause');
        },

        onEnded: function onEnded(e) {
            this.trigger(e, 'ended');
        },

        onError: function onError(e) {
            this.trigger(e, 'error');
        },

        onTimeUpdate: function onTimeUpdate(e) {
            this.trigger(e, 'timeupdate');
        },

        onWaiting: function onWaiting(e) {
            this.trigger(e, 'wait');
        },

        onProgress: function onProgress(e) {
            this.trigger(e, 'progress');
        },

        onLoadedMetaData: function onLoadedMetaData(e) {
            this.trigger(e, 'loadedmetadata');
        },

        trigger: function trigger(e, type) {
            var ext = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
            var detail = e.detail;
            var activeId = e.target.dataset.id;
            this.$emit(type, {
                detail: Object.assign(
                    Object.assign(Object.assign({}, detail), {
                        activeId: activeId
                    }),
                    ext
                )
            });
        }
    },
    created: function () {},
    watch: {
        videoListNewDom: {
            handler: function observer() {
                var newValis = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];
                if (this.curQueue.length > 0) {
                    this.createvideoContexts(newValis, this.curQueue.length - newValis.length);
                }
            },

            immediate: true,
            deep: true
        },

        videoList: {
            handler: function observer() {
                var newVal = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];
                if (this.curQueue.length > 0) {
                    this.setData({
                        curQueue: newVal
                    });
                } else {
                    this.videoListChangedFun(newVal);
                }
            },

            immediate: true,
            deep: true
        }
    }
};

/***/
</script>
<style>
.container {
    width: 100%;
    height: 100%;
}
.video-swiper {
    width: 100%;
    height: 100%;
}
.video_item {
    height: 100%;
    width: 100%;
}
</style>
